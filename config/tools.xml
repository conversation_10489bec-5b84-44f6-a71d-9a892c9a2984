<?xml version="1.0" encoding="UTF-8"?>
<Tools>

<!-- This file contains all the tools that can be used to process queries for different schema.org types. -->

  <Item>
    <Tool name="search" enabled="true">
      <method>builtin</method>
      <example>Find Italian restaurants near me</example>
      <example>Show me action movies from the 1990s</example>
      <example>I need vegetarian pasta recipes</example>
      <example>Find wireless headphones under $200</example>
      <prompt>
        The user has the following query: {request.query}.

        The search tool finds items that match specific search criteria. It's designed for:
        - Finding recipes/movies/books/products that match certain requirements
        - Discovering items based on attributes like genre, cuisine type, price range, or features
        - Broad exploration when users want to see what's available
        
        The search tool is NOT the best choice if the user is asking for specific details 
        about a PARTICULAR named item (like ingredients, instructions, cast, price, etc.).
        
        Assign a score from 0 to 100 for whether the search tool is appropriate.
        Also provide the search query that should be passed to the tool.
      </prompt>
      <returnStruc>
        {
          "score": "integer between 0 and 100",
          "search_query": "the search query that should be passed to the tool"
        }
      </returnStruc>
    </Tool>

    <Tool name="details" enabled="true">
      <method>extension</method>
      <handler>methods.item_details.ItemDetailsHandler</handler>
      <example>What are the ingredients in Chicken Alfredo?</example>
      <example>Who directed The Dark Knight?</example>
     
      <prompt>
        The user has the following query: {request.query}.
        
        The details tool gets specific information about a PARTICULAR named item and is best for:
        - Getting ingredients, instructions, or other details about a SPECIFIC recipe/item
        - Getting the price, specifications, color, etc. for a specific product
        - Queries that mention a specific item name and ask for particular details about it.
        
        The details tool should score HIGH (80-100) if the user names a specific item and wants details.
        The details tool should score LOW (0-30) if the user wants to find/search for items.

        If you are not sure whether the user is asking for details about a specific item, 
        or if they are asking for a general search, assume that they are doing a search
        and score the tool as LOW. Details queries should specify what details are being requested.
        
        Assign a score from 0 to 100 for whether the details tool is appropriate.
        If the user is asking for details about a specific item, provide the item name and what details are being requested.
      </prompt>
      <returnStruc>
        {
          "score": "integer between 0 and 100",
          "item_name": "the name or identifier of the specific item for which details are being sought",
          "details_requested": "the details that the user is asking for"
        }
      </returnStruc>
    </Tool>

    <Tool name="compare" enabled="true">
      <method>extension</method>
      <handler>methods.compare_items.CompareItemsHandler</handler>
      <example>Compare pizza margherita vs pepperoni pizza</example>
      <example>Which is better: iPhone vs Samsung Galaxy?</example>
      <example>Compare The Dark Knight vs The Dark Knight Rises</example>
      <example>Which has better ratings: Olive Garden vs Carrabba's?</example>
      <prompt>
        The user has the following query: {request.query}.
        
        The compare tool compares two items side by side and is best for:
        - Direct comparisons between two specific named items
        - Questions asking "which is better" or "what's the difference"
        - Evaluating items against each other on specific criteria
        
        Assign a score from 0 to 100 for whether the compare tool is appropriate.

        If the score is above 74, provide the names of the two items being compared in the item1_name and item2_name fields.
        If there is a specific criteria for the comparison, provide it in the details_requested field.
      </prompt>
      <returnStruc>
        {
          "score": "integer between 0 and 100",

          "item1_name": "the name or identifier of the first item to compare",
          "item2_name": "the name or identifier of the second item to compare",
          "details_requested": "the details that the user is asking for"
        }
      </returnStruc>
    </Tool>
  </Item>

  <Recipe>
  

    <Tool name="details" enabled="true">
      <method>code</method>
      <handler>methods.item_details.ItemDetailsHandler</handler>
      <example>What are the nutritional facts for Chicken Alfredo?</example>
      <example>Show me the ingredients for Beef Stroganoff</example>
      <example>How long does it take to make Homemade Lasagna?</example>
      <example>What's the calorie content of Thai Green Curry?</example>
      <prompt>
        The user has the following query: {request.query}.
        
        The recipe details tool gets detailed information about a specific recipe including nutritional content, ingredients list, cooking instructions, prep time, and serving size. It's best for:
        - Getting ingredients for a SPECIFIC named recipe
        - Getting cooking instructions for a particular recipe
        - Getting nutritional information for a specific dish
        - Queries that mention a specific recipe name and ask for details about it

        Queries that don't mention a very specific recipe should be scored LOW (0-30).
        
        The details tool should score HIGH (80-100) if the user names a specific recipe and wants details.
        The details tool should score LOW (0-30) if the user wants to find/search for recipes.
        
        Assign a score from 0 to 100 for whether the recipe details tool is appropriate.
        If the user is asking for details about a specific recipe, provide the recipe name and what details are being requested.
      </prompt>
      <returnStruc>
        {
          "score": "integer between 0 and 100",
          "item_name": "the name or identifier of the specific recipe for which details are being sought",
          "details_requested": "the details that the user is asking for"
        }
      </returnStruc>
    </Tool>

    <Tool name="compare" enabled="true">
      <method>code</method>
      <handler>methods.compare_items.CompareItemsHandler</handler>
      <example>Compare the calories in pizza margherita vs pepperoni pizza</example>
      <example>Which has more protein: grilled chicken or salmon teriyaki?</example>
      <example>Compare cooking time between homemade bread and no-knead bread</example>
      <example>Which is healthier: caesar salad or greek salad?</example>
      <prompt>
        The user has the following query: {request.query}.
        
        The recipe compare tool compares two recipes side by side for nutritional content, ingredients, cooking time, difficulty, or cost. It's best for:
        - Direct comparisons between two specific named recipes
        - Questions asking "which is healthier" or "which has more protein"
        - Evaluating recipes against each other on specific criteria
        
        Assign a score from 0 to 100 for whether the recipe compare tool is appropriate.
        If appropriate, identify the two recipes being compared.
      </prompt>
      <returnStruc>
        {
          "score": "integer between 0 and 100",
          "item1": "the name or identifier of the first recipe to compare",
          "item2": "the name or identifier of the second recipe to compare"
        }
      </returnStruc>
    </Tool>

    <Tool name="recipe_substitutions" enabled="true">
      <method>code</method>
      <handler>methods.recipe_substitution.SubstitutionHandler</handler>
      <example>How can I make chocolate cake dairy-free?</example>
      <example>What can I substitute for eggs in cookie dough?</example>
      <example>Make this pasta recipe gluten-free</example>
      <example>I don't have buttermilk, what can I use instead?</example>
      <prompt>
        The user has the following query: {request.query}.
        
        The recipe substitutions tool suggests ingredient substitutions for recipes to accommodate dietary restrictions, allergies, or ingredient availability. It's best for:
        - Making recipes dairy-free, gluten-free, vegan, etc.
        - Finding alternatives when ingredients are unavailable
        - Adapting recipes for allergies or dietary restrictions
        - Questions about ingredient replacements
        
        Assign a score from 0 to 100 for whether the recipe substitutions tool is appropriate.
        If appropriate, identify the recipe, dietary need, or ingredient substitution being requested.
        Only fill out the fields that are explicitly mentioned in the user's query.
        
        IMPORTANT: If the user mentions a specific food item or recipe (e.g., "chocolate chip cookies", "chocolate cake", "pasta"), 
        include it in the recipe_name field even if they don't explicitly say "recipe".
      </prompt>
      <returnStruc>
        {
          "score": "integer between 0 and 100",
          "recipe_name": "name of the recipe to modify",
          "dietary_need": "specific dietary requirement",
          "unavailable_ingredient": "ingredient that needs to be substituted",
          "preference": "preference for substitution"
        }
      </returnStruc>
    </Tool>

    <Tool name="accompaniment" enabled="true">
      <method>code</method>
      <handler>methods.accompaniment.AccompanimentHandler</handler>
      <example>what salad would go best with an eggplant lasagna</example>
      <example>give me a sauce that would balance out a bitter melon dish</example>
      <example>wines that would pair with a pear dessert</example>
      <example>what side dishes go well with roast turkey</example>
      <prompt>
        The user has the following query: {request.query}.
        
        The accompaniment tool finds items that complement or pair well with a main item. It is best for:
        - Finding wines that pair with specific dishes
        - Suggesting side dishes or salads for main courses
        - Recommending sauces or condiments that balance flavors
        - Finding complementary items where one enhances the other
        
        This tool should score HIGH (80-100) when the user:
        - Asks what goes well with a specific dish
        - Wants pairing recommendations (wine, sides, sauces)
        - Needs something to complement or balance another item
        
        This tool should score LOW (0-30) when the user:
        - Just wants a recipe for something
        - Is looking for ingredient substitutions
        - Wants nutritional information
        
        Assign a score from 0 to 100 for whether this tool is appropriate.
        If the score is above 74, provide:
        - search_query: the type of accompaniment (e.g., "salad", "sauce", "wine")
        - main_item: what it should pair with (e.g., "eggplant lasagna", "bitter melon")
      </prompt>
      <returnStruc>
        {
          "score": "integer between 0 and 100",
          "search_query": "the decontextualized search term for the accompaniment type only",
          "main_item": "the main item to find accompaniments for"
        }
      </returnStruc>
    </Tool>
  </Recipe>

  <Movie>
  
    <Tool name="details" enabled="true">
      <method>code</method>
      <handler>methods.item_details.ItemDetailsHandler</handler>
      <example>Who directed The Dark Knight?</example>
      <example>What's the plot of Inception?</example>
      <example>How long is Avengers Endgame?</example>
      <example>What's the rating for Parasite?</example>
      <prompt>
        The user has the following query: {request.query}.
        
        The movie details tool gets detailed information about a specific movie including cast, director, plot, runtime, release date, and ratings. It's best for:
        - Getting cast information for a SPECIFIC named movie
        - Getting plot, director, or other details for a particular movie
        - Getting ratings or runtime for a specific film
        - Queries that mention a specific movie title and ask for details about it
        
        The details tool should score HIGH (80-100) if the user names a specific movie and wants details.
        The details tool should score LOW (0-30) if the user wants to find/search for movies.
        
        Assign a score from 0 to 100 for whether the movie details tool is appropriate.
        If the user is asking for details about a specific movie, provide the movie title and what details are being requested.
      </prompt>
      <returnStruc>
        {
          "score": "integer between 0 and 100",
          "item_name": "the title or identifier of the specific movie for which details are being sought",
          "details_requested": "the details that the user is asking for"
        }
      </returnStruc>
    </Tool>

  </Movie>

  <Product>
   

    <Tool name="details" enabled="true">
      <method>code</method>
      <handler>methods.item_details.ItemDetailsHandler</handler>
      <example>What colors does the iPhone 15 Pro come in?</example>
      <example>What's the price of the Samsung 65-inch QLED TV?</example>
      <example>Show me customer reviews for the Keurig K-Elite</example>
      <example>Is the Levi's 501 Jeans available in size 32?</example>
      <prompt>
        The user has the following query: {request.query}.
        
        The product details tool gets detailed information about a specific product including price, specifications, available colors/sizes, customer reviews, and shipping information. It's best for:
        - Getting price or specs for a SPECIFIC named product
        - Getting availability, colors, or sizes for a particular product
        - Getting customer reviews for a specific item
        - Queries that mention a specific product name and ask for details about it
        
        The details tool should score HIGH (80-100) if the user names a specific product and wants details.
        The details tool should score LOW (0-30) if the user wants to find/search for products.
        
        Assign a score from 0 to 100 for whether the product details tool is appropriate.
        If the user is asking for details about a specific product, provide the product name and what details are being requested.
      </prompt>
      <returnStruc>
        {
          "score": "integer between 0 and 100",
          "item_name": "the name or identifier of the specific product for which details are being sought",
          "details_requested": "the details that the user is asking for"
        }
      </returnStruc>
    </Tool>

    <Tool name="compare" enabled="true">
      <method>code</method>
      <handler>methods.compare_items.CompareItemsHandler</handler>
      <example>Compare iPhone 15 vs Samsung Galaxy S24 prices and features</example>
      <example>Which is better: Sony WH-1000XM5 or Bose QuietComfort headphones?</example>
      <example>Compare these three laptops for gaming performance</example>
      <example>What's the price difference between these two coffee machines?</example>
      <prompt>
        The user has the following query: {request.query}.
        
        The product compare tool compares two or more products side by side for price, features, specifications, customer ratings, or other attributes. It's best for:
        - Direct comparisons between two specific named products
        - Questions asking "which is better" or "what's the price difference"
        - Evaluating products against each other on specific criteria
        
        Assign a score from 0 to 100 for whether the product compare tool is appropriate.
        If appropriate, identify the products being compared.
      </prompt>
      <returnStruc>
        {
          "score": "integer between 0 and 100",
          "item1": "the name or identifier of the first product to compare",
          "item2": "the name or identifier of the second product to compare"
        }
      </returnStruc>
    </Tool>
  </Product>

  <Restaurant>
    <Tool name="search" enabled="true">
      <method>builtin</method>
      <example>Find Italian restaurants near downtown with outdoor seating</example>
      <example>Show me highly rated sushi places within 5 miles</example>
      <example>I need cheap eats near the airport</example>
      <example>Find upscale restaurants for a date night in the city center</example>
      <prompt>
        The user has the following query: {request.query}.

        The restaurant search tool finds restaurants in a specific area that match criteria such as cuisine type, price range, ratings, or special features. It's designed for:
        - Finding restaurants by cuisine, location, price, or features
        - Discovering restaurants that meet specific requirements
        - Broad restaurant exploration when users want to see what's available
        
        The search tool is NOT the best choice if the user is asking for specific details 
        about a PARTICULAR named restaurant (like menu, hours, ratings, location, etc.).
        
        Assign a score from 0 to 100 for whether the restaurant search tool is appropriate.
        Also provide the search query that should be passed to the tool.
      </prompt>
      <returnStruc>
        {
          "score": "integer between 0 and 100",
          "search_query": "the search query that should be passed to the tool"
        }
      </returnStruc>
    </Tool>

    <Tool name="details" enabled="true">
      <method>code</method>
      <handler>methods.item_details.ItemDetailsHandler</handler>
      <example>What are the hours for Olive Garden?</example>
      <example>Show me the menu for Joe's Pizza</example>
      <example>What's the rating for The French Laundry?</example>
      <example>Does Chipotle have outdoor seating?</example>
      <prompt>
        The user has the following query: {request.query}.
        
        The restaurant details tool gets detailed information about a specific restaurant including menu, hours, location, ratings, and special features. It's best for:
        - Getting hours, menu, or location for a SPECIFIC named restaurant
        - Getting ratings or reviews for a particular restaurant
        - Getting specific features like outdoor seating for a named restaurant
        - Queries that mention a specific restaurant name and ask for details about it
        
        The details tool should score HIGH (80-100) if the user names a specific restaurant and wants details.
        The details tool should score LOW (0-30) if the user wants to find/search for restaurants.
        
        Assign a score from 0 to 100 for whether the restaurant details tool is appropriate.
        If the user is asking for details about a specific restaurant, provide the restaurant name and what details are being requested.
      </prompt>
      <returnStruc>
        {
          "score": "integer between 0 and 100",
          "item_name": "the name or identifier of the specific restaurant for which details are being sought",
          "details_requested": "the details that the user is asking for"
        }
      </returnStruc>
    </Tool>

    <Tool name="compare" enabled="true">
      <method>code</method>
      <example>Compare Olive Garden vs Carrabba's Italian Grill</example>
      <example>Which is more expensive: Ruth's Chris or Morton's?</example>
      <example>Compare the ratings of In-N-Out vs Five Guys</example>
      <example>Which has better vegetarian options: Chipotle or Qdoba?</example>
      <prompt>
        The user has the following query: {request.query}.
        
        The restaurant compare tool compares two restaurants side by side for ratings, price, cuisine, or other attributes. It's best for:
        - Direct comparisons between two specific named restaurants
        - Questions asking "which is better" or "which is more expensive"
        - Evaluating restaurants against each other on specific criteria
        
        Assign a score from 0 to 100 for whether the restaurant compare tool is appropriate.
        If appropriate, identify the two restaurants being compared.
      </prompt>
      <returnStruc>
        {
          "score": "integer between 0 and 100",
          "item1": "the name or identifier of the first restaurant to compare",
          "item2": "the name or identifier of the second restaurant to compare"
        }
      </returnStruc>
    </Tool>


</Restaurant>

  <Item>
    <Tool name="ensemble">
      <method>code</method>
      <handler>methods.ensemble_tool.EnsembleToolHandler</handler>
      <example>Give me an appetizer, main and dessert for an Asian fusion dinner</example>
      <example>I am spending a few hours in Barcelona. Give some museums I can go to and nearby restaurants</example>
      <example>Suggest the right footwear, jacket, etc. for hiking in the Grand Canyon in January</example>
      <example>Plan a romantic date night with dinner, activity, and dessert</example>
      <prompt>
        The user has the following query: {request.query}.
        
        The ensemble tool handles requests where users ask for multiple related items that go together as a set or collection. It's designed for:
        - Meal planning (appetizer + main + dessert combinations)
        - Travel itineraries (attractions + restaurants + activities in a location)
        - Outfit recommendations (clothing + accessories for specific occasions/weather)
        - Event planning (venue + catering + entertainment)
        - Any request asking for multiple complementary items
        
        The ensemble tool should score HIGH (80-100) if the user wants multiple related items that form a cohesive set.
        The ensemble tool should score LOW (0-30) if the user wants just one specific item type (e.g, spices for 
        a particular recipe, or a specific type of clothing for a specific occasion).
        
        Assign a score from 0 to 100 for whether the ensemble tool is appropriate.
        
        If the score is above 74, extract the individual queries that should be searched for.
        For example:
        - "Asian fusion dinner" → ["Asian fusion appetizer", "Asian fusion main course", "Asian fusion dessert"]
        - "Museums and restaurants in Barcelona" → ["museums in Barcelona", "restaurants near museums in Barcelona"]
        - "Hiking outfit for Grand Canyon in January" → ["hiking boots for cold weather", "winter hiking jacket", "cold weather hiking accessories"]
      </prompt>
      <returnStruc>
        {
          "score": "integer between 0 and 100",
          "queries": "array of search queries to retrieve related items",
          "ensemble_type": "type of ensemble being requested"
        }
      </returnStruc>
    </Tool>
  </Item>

  <Statistics>
   
    <Tool name="statistics_query" enabled="true">
      <method>code</method>
      <handler>methods.statistics_handler.StatisticsHandler</handler>
      <example>What is the median income in San Francisco county?</example>
      <example>Which county has the highest population?</example>
      <example>What is the correlation between median age and median income across counties?</example>
      <example>How does population in Los Angeles county compare to San Diego county?</example>
      <example>What are the top 5 counties by number of veterans?</example>
      <example>Which counties have median income above $100,000?</example>
      <prompt>
        The user has the following query: {request.query}.
        
        The statistics query tool retrieves and analyzes statistical data about places (counties, cities, states) from Data Commons. It can handle:
        - Single variable queries for a specific place (e.g., "What is the median income in X county?", "population of Y city")
        - Comparisons between places for a variable (e.g., "compare population between county A and B")
        - Rankings and extremes (e.g., "top N counties by variable", "which county has highest X")
        - Correlations between variables (e.g., "correlation between X and Y")
        - Filtering queries (e.g., "counties where X > threshold")
        - Statistical aggregations (e.g., "average X across all counties")
        - Trend analysis (e.g., "how has X changed from year1 to year2")
        - Ratio and percentage calculations (e.g., "ratio of X to Y")
        
        Common variables include: population, median age, median income, number of homes, 
        number of veterans, number of disabled, unemployment rate, poverty rate, education level,
        and other demographic/economic indicators.
        
        The tool should score VERY HIGH (95-100) when the user asks:
        - "What is the [variable] in [place]?" (e.g., "What is the median income in San Francisco county?")
        - "Population of [place]" or "[variable] of [place]"
        - Any question about demographic or economic data for geographic locations
        
        The tool should score HIGH (80-94) when the user asks about:
        - Comparisons between places
        - Rankings or extremes based on variables
        - Correlations or relationships between variables
        - Statistical aggregations or distributions
        
        The tool should score LOW (0-30) when:
        - The query is not about statistical/demographic data
        - The query is about non-place entities (people, products, etc.)
        - The query asks for general details about a place without mentioning specific statistics
        
        Assign a score from 0 to 100 for whether this tool is appropriate.
        If the score is above 74, extract the key components of the query.
      </prompt>
      <returnStruc>
        {
          "score": "integer between 0 and 100",
          "query_type": "type of statistical query (single_value, comparison, ranking, correlation, filter, aggregation, trend, ratio)",
          "variables": "array of variable names mentioned in the query",
          "places": "array of place names mentioned in the query",
          "filters": "any filtering conditions (thresholds, comparisons)",
          "time_range": "time period if specified (year1, year2)",
          "aggregation": "type of aggregation if any (average, median, sum, etc.)",
          "limit": "number of results requested (for top/bottom N queries)"
        }
      </returnStruc>
    </Tool>
    
  </Statistics>

</Tools>
