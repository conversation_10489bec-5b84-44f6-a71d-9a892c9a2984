{"variables": {"population": "Count_Person", "median age": "Median_Age_Person", "median income": "Median_Income_Person", "number of homes": "Count_HousingUnit", "number of veterans": "Count_Person_Veteran", "number of disabled": "Count_Person_Disabled", "unemployment rate": "UnemploymentRate_Person", "poverty rate": "Count_Person_BelowPovertyLevelInThePast12Months", "median home value": "Median_Value_HousingUnit", "median rent": "Median_GrossRent_HousingUnit", "life expectancy": "LifeExpectancy_Person", "crime rate": "Count_CriminalActivities_CombinedCrime", "education level": "Count_Person_EducationalAttainmentBachelorsDegree<PERSON>r<PERSON><PERSON><PERSON>", "household income": "Median_Income_Household", "per capita income": "Mean_Income_Person", "arthritis prevalence": "Percent_Person_<PERSON><PERSON><PERSON><PERSON>is", "percent with arthritis": "Percent_Person_<PERSON><PERSON><PERSON><PERSON>is", "asthma prevalence": "Percent_Person_WithAsthma", "percent with asthma": "Percent_Person_WithAsthma", "cancer prevalence": "Percent_Person_WithCancerExcludingSkinCancer", "percent with cancer": "Percent_Person_WithCancerExcludingSkinCancer", "kidney disease prevalence": "Percent_Person_WithChronicKidneyDisease", "percent with kidney disease": "Percent_Person_WithChronicKidneyDisease", "chronic kidney disease": "Percent_Person_WithChronicKidneyDisease", "copd prevalence": "Percent_Person_WithChronicObstructivePulmonaryDisease", "percent with copd": "Percent_Person_WithChronicObstructivePulmonaryDisease", "chronic obstructive pulmonary disease": "Percent_Person_WithChronicObstructivePulmonaryDisease", "heart disease prevalence": "Percent_Person_WithCoronaryHeartDisease", "percent with heart disease": "Percent_Person_WithCoronaryHeartDisease", "coronary heart disease": "Percent_Person_WithCoronaryHeartDisease", "diabetes prevalence": "Percent_Person_WithDiabetes", "percent with diabetes": "Percent_Person_WithDiabetes", "high blood pressure prevalence": "Percent_Person_WithHighBloodPressure", "percent with high blood pressure": "Percent_Person_WithHighBloodPressure", "hypertension prevalence": "Percent_Person_WithHighBloodPressure", "high cholesterol prevalence": "Percent_Person_WithHighCholesterol", "percent with high cholesterol": "Percent_Person_WithHighCholesterol", "mental health issues": "Percent_Person_WithMentalHealthNotGood", "percent with poor mental health": "Percent_Person_WithMentalHealthNotGood", "mental health prevalence": "Percent_Person_WithMentalHealthNotGood", "physical health issues": "Percent_Person_WithPhysicalHealthNotGood", "percent with poor physical health": "Percent_Person_WithPhysicalHealthNotGood", "physical health prevalence": "Percent_Person_WithPhysicalHealthNotGood", "stroke prevalence": "Percent_Person_WithStroke", "percent with stroke": "Percent_Person_WithStroke"}, "place_types": {"county": "County", "city": "City", "state": "State", "country": "Country", "zip": "CensusZipCodeTabulationArea"}}