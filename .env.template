# This holds the environment variables needed for the webapp to run.
# It is recommended to set these variables in your shell profile (e.g., .bashrc, .zshrc) or in a secure environment.
# This script is for demonstration purposes only and should not be used in production.
# Make sure to replace the placeholder values with your actual keys and endpoints.

# For instructions on obtaining the required Azure service keys, 
# see ../docs/Azure.md

# For instructions on utilizing Snowflake services, see ../docs/Snowflake.md

AZURE_VECTOR_SEARCH_ENDPOINT="https://TODO.search.windows.net" 
AZURE_VECTOR_SEARCH_API_KEY="<TODO>"

AZURE_OPENAI_ENDPOINT="https://TODO.openai.azure.com/"
AZURE_OPENAI_API_KEY="<TODO>"

ANTHROPIC_API_KEY="<TODO>"

INCEPTION_ENDPOINT="https://api.inceptionlabs.ai/v1/chat/completions"
INCEPTION_API_KEY="<TODO>"

OPENAI_ENDPOINT="https://api.openai.com/v1/chat/completions"
OPENAI_API_KEY="<TODO>"

SNOWFLAKE_ACCOUNT_URL="<TODO>"
SNOWFLAKE_PAT="<TODO>"
# One of https://docs.snowflake.com/en/user-guide/snowflake-cortex/vector-embeddings#text-embedding-models
SNOWFLAKE_EMBEDDING_MODEL=snowflake-arctic-embed-l-v2.0

# Fully qualified name of the cortex search service in your snowflake account
# For example TEMP.NLWEB.NLWEB_SAMPLE
# if you used snowflake.sql with --database TEMP --schema NLWEB
SNOWFLAKE_CORTEX_SEARCH_SERVICE=TODO

# IF USING MILVUS FOR RETRIEVAL
# Milvus has several deployment modes:
# - For dev, run Milvus Lite: Specify a local a file to persist data. No token needed.
MILVUS_ENDPOINT="../data/db/milvus.db"
# - For production, deploy a server and specify the URI and optionally "username:password" as token.
# MILVUS_ENDPOINT="http://localhost:19530"
# MILVUS_TOKEN="username:password" # If authentication is turned on, otherwise use empty string.
# - For fully managed Milvus (Zilliz Cloud): use Public Endpoint and API key of you Zilliz cluster.
# MILVUS_ENDPOINT="<copy_your_cluster_endpoint_here>"
# MILVUS_TOKEN="<copy_your_api_key_here>"

# IF USING QDRANT FOR RETRIEVAL
QDRANT_URL="http://localhost:6333"
QDRANT_API_KEY="<OPTIONAL>"

# IF USING OPENSEARCH FOR RETRIEVAL
OPENSEARCH_ENDPOINT="<TODO>"
# Authentication credentials (username:password for basic auth, or API key)
OPENSEARCH_CREDENTIALS="<TODO>"

OLLAMA_URL="http://localhost:11434"
# IF USING ELASTICSEARCH FOR RETRIEVAL
ELASTICSEARCH_URL="http://localhost:9200"
ELASTICSEARCH_API_KEY="<TODO>"

# If using Postgres connection string
POSTGRES_CONNECTION_STRING="postgresql://<HOST>:<PORT>/<DATABASE>?user=<USERNAME>&sslmode=require"
POSTGRES_PASSWORD="<PASSWORD>"

# Local Directory for file writes
#NLWEB_OUTPUT_DIR=/home/<USER>/data/nlweb

# NLWeb Logging profile (production, development, testing)
# This is used to set the logging level and other configurations in config/config_logging.py
NLWEB_LOGGING_PROFILE=production

# Hugging Face Inference Providers env variables
HF_TOKEN="<TODO>"

# Cloudflare AutoRAG env variables
CLOUDFLARE_API_TOKEN="<TODO>"
CLOUDFLARE_RAG_ID_ENV="<TODO>"
CLOUDFLARE_ACCOUNT_ID="<TODO>"