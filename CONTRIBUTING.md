# Contributing

This project welcomes contributions and suggestions.  Most contributions require you to agree to a
Contributor License Agreement (CLA) declaring that you have the right to, and actually do, grant us
the rights to use your contribution. For details, visit <https://cla.opensource.microsoft.com>.

When you submit a pull request, a CLA bot will automatically determine whether you need to provide
a CLA and decorate the PR appropriately (e.g., status check, comment). Simply follow the instructions
provided by the bot. You will only need to do this once across all repos using our CLA.

# NLWeb Contributions

When contributing to NLWeb, please keep in mind the following principles:

1. We endeavor to keep the repo as small and minimially complicated as reasonably possible.  Meaning, please don't add complicated logic changing a significant amount of files, adds a new **required** pattern, translates to a different language, or other similar types of changes.  We welcome for there to be 'flavors' of NLWeb that have different functionality or languages, but ask for these to please be in a fork.  
2. Please don't change the preferred retrieval or LLM endpoints.  We don't want this to be an ongoing battle. 🙂
3. Test your PRs!  This helps us save time debugging.  Please see [testing > README.md](/code/testing/README.md) for instructions on how to use a simple script to automate this.
4. Want to add a retrieval provider or an LLM?  Please make sure to follow the [instructions](docs/nlweb-providers.md)
5. Have questions on a contribution you would like to make, but there aren't instructions for it?  Please open an issue and we can provide feedback on how to proceed.  PRs are wonderful, but we may ask for a lot of changes, and it also helps us improve our documentation.
6. THANK YOU.  We appreciate your contributions!

This project has adopted the [Microsoft Open Source Code of Conduct](https://opensource.microsoft.com/codeofconduct/).
For more information see the [Code of Conduct FAQ](https://opensource.microsoft.com/codeofconduct/faq/) or
contact [<EMAIL>](mailto:<EMAIL>) with any additional questions or comments.