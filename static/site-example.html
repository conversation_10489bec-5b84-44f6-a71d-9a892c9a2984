<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Site-Specific Example - AllTrails</title>
  <link rel="stylesheet" href="/static/fp-styles.css">
  <link rel="stylesheet" href="/static/chat-page-styles.css">
</head>
<body>
  <div class="app-container">
    <!-- Include the same HTML structure as index.html -->
    <div id="sidebar" class="sidebar">
      <!-- Sidebar content... -->
    </div>
    <div class="main-content">
      <!-- Main content... -->
    </div>
  </div>

  <script type="module">
    import { ModernChatInterface } from '/static/fp-chat-interface.js';
    
    // Initialize with a specific site
    document.addEventListener('DOMContentLoaded', () => {
      const chatInterface = new ModernChatInterface({
        site: 'alltrails',  // Restrict to alltrails site
        skipAutoInit: false
      });
    });
  </script>
</body>
</html>