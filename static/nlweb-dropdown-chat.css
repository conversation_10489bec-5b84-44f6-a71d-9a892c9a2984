/**
 * NLWeb Dropdown Chat Styles
 * Standalone CSS for the dropdown chat component
 */

/* Container styles */
.nlweb-dropdown-container {
    position: relative;
}

/* Search wrapper */
.nlweb-dropdown-search-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    gap: 8px;
}

/* History icon */
.nlweb-dropdown-history-icon {
    position: absolute;
    left: 12px;
    width: 20px;
    height: 20px;
    cursor: pointer;
    color: #666;
    transition: color 0.2s;
    z-index: 1;
}

.nlweb-dropdown-history-icon:hover {
    color: #333;
}

/* Search input */
.nlweb-dropdown-search-input {
    width: 100%;
    padding: 12px 16px 12px 44px;
    font-size: 16px;
    border: 2px solid #ddd;
    border-radius: 8px;
    outline: none;
    transition: border-color 0.2s;
    box-sizing: border-box;
}

.nlweb-dropdown-search-input:focus {
    border-color: #0066cc;
}

/* Dropdown results */
.nlweb-dropdown-results {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    margin-top: 4px;
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    height: 70vh;
    max-height: 70vh;
    min-height: 300px;
    display: none;
    z-index: 1000;
    overflow: visible;
    flex-direction: column;
}

.nlweb-dropdown-results.show {
    display: flex;
}

/* Conversations panel - slides out to the left */
.nlweb-dropdown-conversations-panel {
    position: absolute;
    left: -5px;
    top: 0;
    bottom: 0;
    width: 5px;
    background-color: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 8px 0 0 8px;
    box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: all 0.3s ease;
    z-index: 999;
    display: flex;
    flex-direction: column;
}

.nlweb-dropdown-conversations-panel.show {
    width: 250px;
    left: -250px;
}

.nlweb-dropdown-conversations-list {
    opacity: 0;
    transition: opacity 0.3s ease;
    width: 250px;
}

.nlweb-dropdown-conversations-panel.show .nlweb-dropdown-conversations-list {
    opacity: 1;
}

/* Conversations header */
.nlweb-dropdown-conversations-header {
    padding: 12px 16px;
    border-bottom: 1px solid #e5e5e5;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.nlweb-dropdown-conversations-header h3 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: #333;
}

/* Remembered items section */
.nlweb-dropdown-remembered-section {
    border-top: 1px solid #e5e5e5;
    margin-top: 8px;
}

.nlweb-dropdown-remembered-header {
    padding: 12px 16px 8px 16px;
    background-color: #fff;
}

.nlweb-dropdown-remembered-header h3 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: #333;
}

.nlweb-dropdown-remembered-list {
    padding: 0 8px 8px 8px;
    max-height: 200px;
    overflow-y: auto;
}

.nlweb-dropdown-remembered-item {
    padding: 8px 12px;
    margin-bottom: 4px;
    background-color: #f5f5f5;
    border-radius: 6px;
    font-size: 13px;
    color: #333;
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    gap: 8px;
}

.nlweb-dropdown-remembered-content {
    flex: 1;
    line-height: 1.4;
}

.nlweb-dropdown-remembered-remove {
    width: 20px;
    height: 20px;
    border: none;
    background: none;
    color: #666;
    cursor: pointer;
    font-size: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: all 0.2s;
    flex-shrink: 0;
    opacity: 0.6;
}

.nlweb-dropdown-remembered-remove:hover {
    background-color: rgba(255, 0, 0, 0.1);
    color: #ff4444;
    opacity: 1;
}

.nlweb-dropdown-empty-remembered {
    padding: 16px;
    text-align: center;
    color: #666;
    font-size: 13px;
}

/* Conversations list */
.nlweb-dropdown-conversations-list {
    padding: 8px;
    flex: 1;
    overflow-y: auto;
    min-height: 0;
}

.nlweb-dropdown-conversation-item {
    padding: 12px;
    margin-bottom: 4px;
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.2s;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.nlweb-dropdown-conversation-item:hover {
    background-color: #f0f0f0;
}

.nlweb-dropdown-conversation-item.active {
    background-color: #e8e8e8;
}

.nlweb-dropdown-conversation-title {
    font-size: 14px;
    color: #333;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex: 1;
    margin-right: 8px;
}

.nlweb-dropdown-conversation-delete {
    width: 24px;
    height: 24px;
    border: none;
    background: none;
    color: #666;
    cursor: pointer;
    font-size: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: all 0.2s;
    opacity: 0;
    flex-shrink: 0;
}

.nlweb-dropdown-conversation-item:hover .nlweb-dropdown-conversation-delete {
    opacity: 0.6;
}

.nlweb-dropdown-conversation-delete:hover {
    background-color: rgba(255, 0, 0, 0.1);
    color: #ff4444;
    opacity: 1;
}

.nlweb-dropdown-empty-conversations {
    padding: 16px;
    text-align: center;
    color: #666;
    font-size: 14px;
}

/* Messages container */
.nlweb-dropdown-messages-container {
    padding: 0;
    flex: 1;
    overflow-y: auto;
    min-height: 0;
    display: flex;
    flex-direction: column;
    position: relative;
}

/* Close button */
.nlweb-dropdown-close {
    position: absolute;
    top: 8px;
    right: 8px;
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    color: #666;
    padding: 4px 8px;
    line-height: 1;
    z-index: 10;
}

.nlweb-dropdown-close:hover {
    color: #333;
}

/* Chat messages */
.message {
    padding: 8px 12px;
    margin: 0;
    border-bottom: 1px solid #eee;
}

.message:last-child {
    border-bottom: none;
}

.assistant-message {
    background: transparent;
    animation: none;
}

/* Hide debug icon in dropdown */
.message-debug-icon {
    display: none;
}

.message-layout {
    padding: 0;
}

.message-content {
    padding: 0;
}

/* Chat input in dropdown */
.nlweb-dropdown-chat-input-container {
    border-top: 1px solid #e5e5e5;
    background-color: #ffffff;
    padding: 12px;
    position: sticky;
    bottom: 0;
    margin-top: auto;
}

.nlweb-dropdown-chat-input-wrapper {
    padding: 0;
}

.nlweb-dropdown-chat-input-box {
    background-color: #f7f7f8;
    border: 1px solid #e5e5e5;
    border-radius: 8px;
    padding: 8px 12px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.nlweb-dropdown-chat-input {
    flex: 1;
    border: none;
    background: transparent;
    font-size: 14px;
    line-height: 1.5;
    resize: none;
    outline: none;
    max-height: 100px;
    overflow-y: auto;
    font-family: inherit;
}

.nlweb-dropdown-send-button {
    background-color: transparent;
    color: #666;
    border: none;
    border-radius: 4px;
    padding: 4px 8px;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 14px;
}

.nlweb-dropdown-send-button:hover {
    background-color: #f0f0f0;
    color: #333;
}

.nlweb-dropdown-send-button svg {
    width: 16px;
    height: 16px;
}

/* Loading dots */
.loading-dots {
    display: inline-flex;
    gap: 4px;
}

.loading-dot {
    width: 8px;
    height: 8px;
    background-color: #666;
    border-radius: 50%;
    animation: loading 1.4s infinite ease-in-out both;
}

.loading-dot:nth-child(1) {
    animation-delay: -0.32s;
}

.loading-dot:nth-child(2) {
    animation-delay: -0.16s;
}

@keyframes loading {
    0%, 80%, 100% {
        transform: scale(0);
    }
    40% {
        transform: scale(1);
    }
}

/* Item container styles */
.item-container {
    background-color: #ffffff;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 12px;
    display: flex;
    gap: 16px;
    width: 100%;
}

/* Item content styles */
.item-content {
    flex: 1;
    overflow: hidden;
}

.item-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 8px;
    color: #333;
}

.item-description {
    font-size: 14px;
    color: #666;
    line-height: 1.5;
    margin-bottom: 8px;
}

.item-metadata {
    font-size: 12px;
    color: #999;
}

/* Item image styles */
.item-image-container {
    flex-shrink: 0;
    width: 100px;
    height: 100px;
    overflow: hidden;
    border-radius: 6px;
}

.item-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Ensure images within item containers are constrained */
.item-container img {
    max-width: 100px;
    max-height: 100px;
    width: auto;
    height: auto;
    object-fit: cover;
    border-radius: 6px;
    flex-shrink: 0;
}

/* Scrollbar styling */
.nlweb-dropdown-results ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.nlweb-dropdown-results ::-webkit-scrollbar-track {
    background: transparent;
}

.nlweb-dropdown-results ::-webkit-scrollbar-thumb {
    background: #d0d0d0;
    border-radius: 4px;
}

.nlweb-dropdown-results ::-webkit-scrollbar-thumb:hover {
    background: #b0b0b0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .nlweb-dropdown-results {
        left: 0;
        right: 0;
        width: 100%;
    }
    
    .nlweb-dropdown-conversations-panel.show {
        width: 200px;
        left: -200px;
    }
}

/* Fix conversations panel positioning on narrow windows */
@media (max-width: 750px) {
    /* When window is too narrow, use a different approach */
    .nlweb-dropdown-conversations-panel {
        position: fixed;
        left: -100vw;
        top: 0;
        bottom: 0;
        width: 100vw;
        max-width: 300px;
        border-radius: 0;
        transform: translateX(0);
        transition: transform 0.3s ease;
    }
    
    .nlweb-dropdown-conversations-panel.show {
        left: -100vw;
        width: 100vw;
        max-width: 300px;
        transform: translateX(100vw);
        box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
    }
    
    /* Add overlay for mobile */
    .nlweb-dropdown-conversations-panel.show::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.3);
        z-index: -1;
    }
}