/* Right Sidebar Styles - Recreated */

.right-sidebar {
  position: fixed;
  top: 0;
  right: -280px; /* Hidden by default */
  width: 280px;
  height: 100%;
  background-color: var(--bg-sidebar);
  border-left: 1px solid var(--border-color);
  transition: right 0.2s ease-in-out;
  z-index: 1001;
  display: flex;
  flex-direction: column;
}

.right-sidebar.open {
  right: 0;
}

.right-sidebar-toggle {
  position: fixed;
  top: 20px;
  right: 0;
  width: 32px;
  height: 32px;
  background-color: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-right: none;
  border-radius: 6px 0 0 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: right 0.2s ease-in-out;
  z-index: 1000;
}

.right-sidebar-toggle:hover {
  background-color: var(--hover-bg);
}

/* Move toggle when sidebar is open on non-mobile screens */
@media (min-width: 769px) {
  .app-container.right-sidebar-open .right-sidebar-toggle {
    right: 280px;
  }
}

.right-sidebar-toggle svg {
  width: 16px;
  height: 16px;
  transition: transform 0.2s ease;
}

/* SVG points left by default, rotate to point right when open */
.right-sidebar-toggle.sidebar-open svg {
  transform: rotate(180deg);
}

.right-sidebar-content {
  padding: 16px;
  overflow-y: auto;
  flex-grow: 1;
}

.right-sidebar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.right-sidebar-title {
  font-size: 16px;
  font-weight: 600;
}

/* Match the style of the left sidebar's new chat button */
.new-site-btn {
  width: 32px;
  height: 32px;
  background-color: transparent;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}

.new-site-btn:hover {
  background-color: var(--hover-bg);
}

.new-site-btn .plus-icon {
  font-size: 20px;
  font-weight: bold;
  color: var(--text-secondary);
}

.right-sidebar-section h3 {
  font-size: 12px;
  color: var(--text-secondary);
  font-weight: 600;
  padding: 8px 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.site-list-container {
  font-size: 14px;
  color: var(--text-secondary);
}

.site-list-item {
  padding: 8px 12px;
  background-color: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  margin-bottom: 4px;
}

.no-sites-message {
  padding: 8px 0;
  font-style: italic;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Mobile backdrop */
.right-sidebar-backdrop {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,0.4);
  z-index: 1000;
}

.right-sidebar-backdrop.show {
  display: block;
}
