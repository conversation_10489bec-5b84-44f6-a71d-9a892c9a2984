<html id="homeTemplate_1-0" class="comp homeTemplate html mntl-html seriouseats no-touchevents is-window-loaded" data-mm-ads-resource-version="1.2.168" data-seriouseats-resource-version="6.85.0" data-mm-video-resource-version="1.4.19" data-mantle-resource-version="4.0.763" data-lifestyle-food-resource-version="6.85.0" data-ab="99,99,99,99,78,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99" data-mm-transactional-resource-version="1.15.19" data-lazy-threshold="100" lang="en" data-tracking-container="true" data-resource-version="6.85.0" data-ddm-standard-tracking="true" data-mm-recipes-resource-version="1.3.13"><!--
    <globe-environment environment="k8s-prod" application="seriouseats" dataCenter="us-west-1"/>
    --><head class="loc head">
    <link rel="preconnect" href="//js-sec.indexww.com">
    <link rel="preconnect" href="//c.amazon-adsystem.com">
    <link rel="preconnect" href="//securepubads.g.doubleclick.net">
    <link rel="dnsprefetch" href="//www.google-analytics.com">
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="robots" content="max-image-preview:large, NOODP, NOYDIR">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="canonical" href="https://www.seriouseats.com/">
    <title>Serious Eats</title>
    <meta name="description" content="Serious Eats is the destination for delicious food, with definitive recipes, trailblazing science, and essential guides to eating and knowing all about the best food, wherever you are." itemprop="description">
    <meta name="emailvertical" content="SERIOUSEATS">
    <!-- Pinterest Pins -->
    <meta itemprop="name" content="Serious Eats">
    <meta property="article:section" content="Serious Eats">
    <!-- Facebook Open Graph Tags -->
    <meta property="og:type" content="article">
    <meta property="og:site_name" content="Serious Eats">
    <meta property="og:url" content="https://www.seriouseats.com/">
    <meta property="og:title" content="Serious Eats">
    <meta property="og:description" content="Serious Eats is the destination for delicious food, with definitive recipes, trailblazing science, and essential guides to eating and knowing all about the best food, wherever you are.">
    <meta property="og:image" content="https://www.seriouseats.com/thmb/2eDheo2F1-nYEbQxZLGUiPjqhRs=/1500x0/filters:no_upscale():max_bytes(150000):strip_icc()/Serious_Eats_Social_Default-72cf0bc11b434461b62c6ffc85b4298f.jpg">
    <meta property="article:author" content="https://www.facebook.com/seriouseats">
    <!-- Twitter Cards -->
    <meta name="twitter:card" content="summary">
    <meta name="twitter:site" content="@seriouseats">
    <meta name="twitter:title" content="Serious Eats">
    <meta name="twitter:description" content="Serious Eats is the destination for delicious food, with definitive recipes, trailblazing science, and essential guides to eating and knowing all about the best food, wherever you are.">
    <meta name="twitter:image" content="https://www.seriouseats.com/thmb/2eDheo2F1-nYEbQxZLGUiPjqhRs=/1500x0/filters:no_upscale():max_bytes(150000):strip_icc()/Serious_Eats_Social_Default-72cf0bc11b434461b62c6ffc85b4298f.jpg">
    <meta property="og:tax0" content="se">
    <link rel="alternate" type="application/rss+xml" href="https://feeds-api.dotdashmeredith.com/v1/rss/google/ad57d421-0ff5-41e7-a5da-f2167b6d7f7a">
    <link type="text/css" rel="stylesheet" data-glb-css="top" href="https://www.seriouseats.com/static/6.85.0/cache/eNqVVt2a4yAIfaH1y0Ps7byEURrZqmTVtJ15-kHz29axnRsFeo4gAmkXk0yoOid9stCpGLtsgT_d8w_o5ABitFKBIash1EAOAmqUvkOv4bYh5oM_LcSdRHpivVPkRvLgU-yCRCsuqIEKoUgtfC_jErMSUu9HHyBsLojjz2u4O-qKegDeDeBg0l8j_QAfGBN4CFVjzdVgqZe2i2ccRSKhyDM0PeoNpgGpiz-p6x68vODARvILRbDlIDZueKBGUrhQS-429YXLA6_NSXBLZRG9JXU-iDX0iYJjP3CNFlKCICIOfhqfLa3b_YJdv1w_pUQ-30hgAveoNwJnRAQZlDmINfhaYwEsRsNs0mKWa-jZ_7r_7H7I1chPsYRwr9ZoDrg9O0sDleWt9jpZuGFvgZushbelcHuSQRfaQX-R_X-TG4VFf96lRp9w6vhdl62G46TyitxxwoGfdulFGIYCfnGbLlV-r7beNIWJ54LoJat3ynsdqSlpGY2YPF4gxMV91VqLIoLKx_D4IgdjHtLR0FXl13uy_JIPkjtXhnWvVmpubD5koQgDge61RhIiJ0ulKYDe5mX5zMxrgzijcpUNeGqEleSNPLnPcouj0kzEIqz7i7Ip861U7yb9XJWKu0FkcZca4WfMvL7x9WIYTRHsJrRKlsdAohDFRno0NKKaoWL-QzDP-GdTM8EnLqjy6hit9PpRb3IT8iRaeAf5vcqG_xOOLtfZs-kbLz98YQ.min.css">
    <style type="text/css">
    #onetrust-banner-sdk .onetrust-vendors-list-handler{cursor:pointer;color:#1f96db;font-size:inherit;font-weight:bold;text-decoration:none;margin-left:5px}#onetrust-banner-sdk .onetrust-vendors-list-handler:hover{color:#1f96db}#onetrust-banner-sdk:focus{outline:2px solid #000;outline-offset:-2px}#onetrust-banner-sdk a:focus{outline:2px solid #000}#onetrust-banner-sdk #onetrust-accept-btn-handler,#onetrust-banner-sdk #onetrust-reject-all-handler,#onetrust-banner-sdk #onetrust-pc-btn-handler{outline-offset:1px}#onetrust-banner-sdk.ot-bnr-w-logo .ot-bnr-logo{height:64px;width:64px}#onetrust-banner-sdk .ot-tcf2-vendor-count.ot-text-bold{font-weight:bold}#onetrust-banner-sdk .ot-close-icon,#onetrust-pc-sdk .ot-close-icon,#ot-sync-ntfy .ot-close-icon{background-size:contain;background-repeat:no-repeat;background-position:center;height:12px;width:12px}#onetrust-banner-sdk .powered-by-logo,#onetrust-banner-sdk .ot-pc-footer-logo a,#onetrust-pc-sdk .powered-by-logo,#onetrust-pc-sdk .ot-pc-footer-logo a,#ot-sync-ntfy .powered-by-logo,#ot-sync-ntfy .ot-pc-footer-logo a{background-size:contain;background-repeat:no-repeat;background-position:center;height:25px;width:152px;display:block;text-d...(line too long; chars omitted)
    #onetrust-consent-sdk #onetrust-banner-sdk {background-color: #FFFFFF;}
    #onetrust-consent-sdk #onetrust-policy-title,
    #onetrust-consent-sdk #onetrust-policy-text,
    #onetrust-consent-sdk .ot-b-addl-desc,
    #onetrust-consent-sdk .ot-dpd-desc,
    #onetrust-consent-sdk .ot-dpd-title,
    #onetrust-consent-sdk #onetrust-policy-text *:not(.onetrust-vendors-list-handler),
    #onetrust-consent-sdk .ot-dpd-desc *:not(.onetrust-vendors-list-handler),
    #onetrust-consent-sdk #onetrust-banner-sdk #banner-options *,
    #onetrust-banner-sdk .ot-cat-header {
    color: #333333;
    }
    #onetrust-consent-sdk #onetrust-banner-sdk .banner-option-details {
    background-color: #E9E9E9;}
    #onetrust-consent-sdk #onetrust-banner-sdk a[href],
    #onetrust-consent-sdk #onetrust-banner-sdk a[href] font,
    #onetrust-consent-sdk #onetrust-banner-sdk .ot-link-btn
    {
    color: #1B4D7A;
    }#onetrust-consent-sdk #onetrust-accept-btn-handler,
    #onetrust-banner-sdk #onetrust-reject-all-handler {
    background-color: #FFD12F;border-color: #FFD12F;
    color: #1B4D7A;
    }
    #onetrust-consent-sdk #onetrust-banner-sdk *:focus,
    #onetrust-consent-sdk #onetrust-banner-sdk:focus {
    outline-color: #000000;
    outline-width: 1px;
    }
    #onetrust-consent-sdk #onetrust-pc-btn-handler,
    #onetrust-consent-sdk #onetrust-pc-btn-handler.cookie-setting-link {
    color: #1B4D7A; border-color: #1B4D7A;
    background-color:
    #FFFFFF;
    } /* EU Banner buttons */
    #onetrust-consent-sdk #onetrust-pc-btn-handler:hover, #onetrust-consent-sdk #onetrust-pc-btn-handler.cookie-setting-link:hover, #onetrust-consent-sdk #onetrust-accept-btn-handler:hover, #onetrust-banner-sdk #onetrust-reject-all-handler:hover {
    opacity: 1;
    text-decoration: underline;
    }
    /* TCF 2.2 Redesign */
    #onetrust-consent-sdk #onetrust-banner-sdk #onetrust-close-btn-container {
    top: 25px;
    right: 25px;
    }
    #onetrust-consent-sdk #onetrust-banner-sdk .onetrust-close-btn-ui {
    width: 12px;
    height: 12px;
    }
    #onetrust-banner-sdk #onetrust-policy-text a.ot-sdk-show-settings {
    margin-left: 0;
    }
    #onetrust-consent-sdk #onetrust-button-group {
    text-align: center;
    }
    #onetrust-pc-sdk .ot-tgl .ot-label-status {
    display: block!important;
    }
    @media only screen and (min-width: 425px) and (max-width: 550px) {
    #onetrust-banner-sdk.ot-iab-2 #onetrust-button-group-parent #onetrust-button-group {
    width: auto;
    }
    }
    @media only screen and (min-width: 426px) and (max-width: 896px) {
    #onetrust-consent-sdk #onetrust-banner-sdk .onetrust-close-btn-ui {
    top: 0;
    right: 0;
    }
    }
    @media (min-width: 896px) {
    #onetrust-banner-sdk .ot-sdk-three.ot-sdk-columns {
    left: auto;
    right: 4%;
    margin-left: 0;
    margin-top: 5%;
    }
    }
    @media only screen and (min-width: 769px) {
    #onetrust-banner-sdk #onetrust-button-group {
    margin-right: 0;
    }
    }
    @media only screen and (min-width: 1024px){
    #onetrust-banner-sdk.ot-iab-2 #onetrust-button-group-parent {
    margin-top: .25em;
    }
    }
    html:has(>body>div#onetrust-consent-sdk.show-banner) {
    overflow-y: hidden;
    }
    html:has(>body>div#onetrust-consent-sdk.show-banner>div.onetrust-pc-dark-filter[style*="none"]) {
    overflow-y: auto;
    }
    /* hides banner after it has already been interacted with */
    #onetrust-consent-sdk:not(.show-banner) #onetrust-banner-sdk {
    display: none;
    }</style>
    <script src="//tru.am/scripts/ta-pagesocial-sdk.js" data-loaded="loaded"></script><script async="" src="https://sb.scorecardresearch.com/cs/6036459/beacon.js" data-loaded="loaded"></script><script async="" defer="" src="https://launchpad.privacymanager.io/latest/launchpad.bundle.js" data-loaded="loaded"></script><script type="text/javascript" async="" src="https://www.googletagmanager.com/gtag/js?id=G-48FDR9J6FP&amp;l=dataLayer&amp;cx=c&amp;gtm=45He4cc1v813527222za200" data-loaded="loaded"></script><script async="" src="//www.googletagmanager.com/gtm.js?id=GTM-5P3SZGS" type="text/javascript" data-loaded="loaded"></script><script type="text/javascript" data-glb-js="top" src="https://www.seriouseats.com/static/6.85.0/cache/eNp9kmFuwyAMhS80xB02Tfs1qZpyAQIOdUNwhE2m9PSDqFvTiUYC5OR9cexnNIsRtHoyUQJoS9NMEaKwDmalLPrCZSl7Bju-6Ef4UqHryniFQlAc0P9Hbk8Be37EMQokBitIsZWXjAPXECzRiMANJQsGlLY2RHUkO5oOdUnGjhj9IbRAdJS0nBNJedUgphKoPoEZZyr935PUConc8_S7sVhOw3Y0fjDnnnN_F8jlAJvxX91r3c-krcESvC-wr2tXuln7Vktvn6dWzo9TV3dLEpM8SDWTzQABWVoUW2Uct5QEpcYM3W0kf8jOovJlJf0sLY-MB7UgfB947GXa7lpAO6rf4f8ACEUwBA.min.js" data-loaded="loaded"></script>
    <script type="text/javascript">
    window.Mntl = window.Mntl || {};
    window.Mntl.csrf = window.Mntl.csrf || window.Mntl.csrfInit('43aa5c57982b972fd3b93057f02c2231', false);window.Mntl = window.Mntl || {};
    Mntl.RTB = Mntl.RTB || {};
    Mntl.RTB.setUseConsentManagement(false);
    Mntl.RTB.setUseLiveRamp(true);
    Mntl.RTB.setUseLiveIntentSignedInUser(true);
    Mntl.RTB.setUseGDPREnforcement(false);
    Mntl.RTB.setUseCommerceAndShoppingAPSTag(false);
    Mntl.RTB.setUseAmazonNcaHookTest(false);
    Mntl.RTB.setUserIdAuctionDelay(300);
    Mntl.RTB.setTaxonomyStampValues({"tax0":"se_root"});
    Mntl.RTB.indexFirstPartyData = 'tax1:na';
    Mntl.RTB.setTimeoutLength([500,800]);
    Mntl.RTB.Plugins.amazon.amazonConfigs = {"mapTaxValues":{"tax1":"null","tax2":"null","si_section":"null","tax0":"se_root","tax3":"null"},"amazonSlotName":false,"mapFBValues":{"adunitid":"mapFBID"}};
    Mntl.RTB.Plugins.prebid.setPriceGranularity({"buckets":[{"max":"20","increment":"0.05"},{"max":"70","increment":"1"}]});
    Mntl.RTB.initVideoBidders([{ type: 'amazon', id: '3222' }, { type: 'prebid', id: 'true' } ]);
    Mntl.RTB.initDisplayAndOutstreamBidders([{ type: "amazon", id: '3222'},{ type: "msg", id: 'true'},{ type: "ixid", id: 'true'},{ type: "prebid", id: 'true'},{ type: "liveConnect", id: 'a-07sk'}]);
    Mntl.RTBTracking.init();
    Mntl.RTB.Plugins.prebid.setConfig({"square-fixed\/tier1":[{"bidder":"ttd","params":{"supplySourceId":"dotdash","publisherId":"1","type":"display"}},{"bidder":"rubicon","params":{"zoneId":2442186,"type":"display","accountId":7499,"siteId":426848}},{"bidder":"appnexus","params":{"placementId":********,"type":"display"}},{"bidder":"pubmatic","params":{"adSlot":"4515050","type":"display","publisherId":"158139"}},{"bidder":"triplelift","params":{"inventoryCode":"SeriousEats_square-fixed_300x250_pbc2s","type":"display"}},{"bidder":"openx","params":{"unit":"*********","type":"display","delDomain":"meredith-d.openx.net"}},{"bidder":"ix","params":{"siteId":"830474","type":"display"}}],"leaderboard-fixed\/tier1":[{"bidder":"ttd","params":{"supplySourceId":"dotdash","publisherId":"1","type":"display"}},{"bidder":"rubicon","params":{"zoneId":2442176,"type":"display","accountId":7499,"siteId":426848}},{"bidder":"appnexus","params":{"placementId":********,"type":"display"}},{"bidder":"pubmatic","params":{"adSlot":"4515355","type":"display","publisherId":"158139"}},{"bidder":"triplelift","params":{"inventoryCode":"SeriousEats_leaderboard-fixed_728x90_pbc2s","type":"display"}},{"bidder":"openx","params":{"unit":"*********","type":"display","delDomain":"meredith-d.openx.net"}},{"bidder":"ix","params":{"siteId":"830467","type":"display"}}],"square-fixed-replies\/tier1":[{"bidder":"ttd","params":{"supplySourceId":"dotdash","publisherId":"1","type":"display"}},{"bidder":"rubicon","params":{"zoneId":3601230,"type":"display","accountId":7499,"siteId":426848}},{"bidder":"appnexus","params":{"placementId":********,"type":"display"}},{"bidder":"pubmatic","params":{"adSlot":"6389421","type":"display","publisherId":"158139"}},{"bidder":"triplelift","params":{"inventoryCode":"seriouseats_square-fixed-replies_300x250_pbc2s","type":"display"}},{"bidder":"openx","params":{"unit":"*********","type":"display","delDomain":"meredith-d.openx.net"}},{"bidder":"ix","params":{"siteId":"1146776","type":"display"}}],"preroll":[{"bidder":"ttd","params":{"type":"instream","supplySourceId":"dotdash","publisherId":"1"}},{"bidder":"rubicon","params":{"type":"instream","zoneId":2442196,"accountId":7499,"siteId":426848}},{"bidder":"appnexus","params":{"type":"instream","placementId":********}},{"bidder":"pubmatic","params":{"type":"instream","adSlot":"4515055","publisherId":"158139"}},{"bidder":"openx","params":{"type":"instream","unit":"*********","delDomain":"meredith-d.openx.net"}},{"bidder":"ix","params":{"type":"instream","siteId":"830485"}}],"square-flex\/tier1":[{"bidder":"ttd","params":{"supplySourceId":"dotdash","publisherId":"1","type":"display"}},{"bidder":"rubicon","params":{"zoneId":2442194,"type":"display","accountId":7499,"siteId":426848}},{"bidder":"appnexus","params":{"placementId":********,"type":"display"}},{"bidder":"pubmatic","params":{"adSlot":"4515054","type":"display","publisherId":"158139"}},{"bidder":"triplelift","params":{"inventoryCode":"SeriousEats_square-flex_300x600_pbc2s","type":"display"}},{"bidder":"openx","params":{"unit":"*********","type":"display","delDomain":"meredith-d.openx.net"}},{"bidder":"ix","params":{"siteId":"830482","type":"display"}}],"square-fixed-comments\/tier1":[{"bidder":"ttd","params":{"supplySourceId":"dotdash","publisherId":"1","type":"display"}},{"bidder":"rubicon","params":{"zoneId":3546544,"type":"display","accountId":7499,"siteId":426848}},{"bidder":"appnexus","params":{"placementId":********,"type":"display"}},{"bidder":"pubmatic","params":{"adSlot":"6234498","type":"display","publisherId":"158139"}},{"bidder":"triplelift","params":{"inventoryCode":"seriouseats_square-fixed-comments_300x250_pbc2s","type":"display"}},{"bidder":"openx","params":{"unit":"*********","type":"display","delDomain":"meredith-d.openx.net"}},{"bidder":"ix","params":{"siteId":"1137140","type":"display"}}],"global":[{"bidder":"ttd","params":{"supplySourceId":"dotdash","publisherId":"1","type":"display"}},{"bidder":"rubicon","params":{"accountId":7499,"siteId":426848,"type":"display"}},{"bidder":"pubmatic","params":{"publisherId":"158139","type":"display"}},{"bidder":"openx","params":{"delDomain":"meredith-d.openx.net","type":"display"}}],"leaderboardac\/tier1":[{"bidder":"ttd","params":{"supplySourceId":"dotdash","publisherId":"1","type":"display"}},{"bidder":"rubicon","params":{"zoneId":2442184,"type":"display","accountId":7499,"siteId":426848}},{"bidder":"appnexus","params":{"placementId":********,"type":"display"}},{"bidder":"pubmatic","params":{"adSlot":"6405112","type":"display","publisherId":"158139"}},{"bidder":"triplelift","params":{"inventoryCode":"SeriousEats_leaderboardac_728x90_pbc2s","type":"display"}},{"bidder":"openx","params":{"unit":"*********","type":"display","delDomain":"meredith-d.openx.net"}},{"bidder":"ix","params":{"siteId":"830468","type":"display"}}],"leaderboardfooter-flex\/tier1":[{"bidder":"ttd","params":{"supplySourceId":"dotdash","publisherId":"1","type":"display"}},{"bidder":"rubicon","params":{"zoneId":2442176,"type":"display","accountId":7499,"siteId":426848}},{"bidder":"appnexus","params":{"placementId":********,"type":"display"}},{"bidder":"pubmatic","params":{"adSlot":"4632082","type":"display","publisherId":"158139"}},{"bidder":"triplelift","params":{"inventoryCode":"SeriousEats_leaderboardfooter-flex_970x250_pbc2s","type":"display"}},{"bidder":"openx","params":{"unit":"*********","type":"display","delDomain":"meredith-d.openx.net"}},{"bidder":"ix","params":{"siteId":"830467","type":"display"}}],"leaderboard-flex\/tier1":[{"bidder":"ttd","params":{"supplySourceId":"dotdash","publisherId":"1","type":"display"}},{"bidder":"rubicon","params":{"zoneId":2442184,"type":"display","accountId":7499,"siteId":426848}},{"bidder":"appnexus","params":{"placementId":********,"type":"display"}},{"bidder":"pubmatic","params":{"adSlot":"4515049","type":"display","publisherId":"158139"}},{"bidder":"triplelift","params":{"inventoryCode":"SeriousEats_leaderboard-flex_970x250_pbc2s","type":"display"}},{"bidder":"openx","params":{"unit":"*********","type":"display","delDomain":"meredith-d.openx.net"}},{"bidder":"ix","params":{"siteId":"830468","type":"display"}}]});
    Mntl.RTB.Plugins.prebid.setSrc('https://www.seriouseats.com/cache/eNqdlttu2zAMhl9oQbC-QbalRYEFK3q6l0XGZSOLAkW58Z5-THa42oWpK8sCP1H-Jf70tmpQituJoSWs26FluDzf67YIDgQ2AKr6520TWfDTdi0kCodrzHokQGFwhKfEH7uoNJMSVgeodZdDWiy47iAURVkPR84Vsx5CDiNONlqPHhPCiLcsd8VBRSFFvoenpSpO6znAoY0j5dGBHMsOnlBmlFcC5PXkCEX2-cgSnZpQqI8KD8KzJXQcg0VnJV2-Uz75tUk0431WW8LPlhAKecJJOpJc334fxRcC9y0tQhFvE7M46qK-BUHwb7YR3HRQmY7Ula6iKeoo91Iynlvt0ZHOXeq3YbpE9rDSBjKP6UFVoQsTKgkTHbWH5oK5S6T41vLJ2VSioAXOuBHMZhcoG5suKSyO_Q7vGPWuBfFcof52MRWHixZ9UUqOko1NTIq4eLGr9k4nq_QTnXnMGT7vCXzEjY9g0eErZzNKX40OdX-2DlDJGrqjRfIUKP-wbGIt6JkfmfXbdW79GidcPlg83_jXwnqO2WMEIVfr4dNDkDC5kwXFkWV5vqySjOF8sH27fkCapP9n_Re5P2Nsl7V_Adq4HRY.js');
    Mntl.RTB.Plugins.prebid.setUserIdConfig({"liveIntentPublisherId":"73300"});
    Mntl.RTB.AdConfigs = Mntl.RTB.AdConfigs || {};
...(about 2154 lines omitted)...
    Mntl.RTB.AdConfigs.rawConfig = {"slots":{"leaderboardac\/tier1":{"prebid":[{"bidder":"rubicon","params":{"zoneId":2442184,"type":"display","accountId":7499,"siteId":426848}},{"bidder":"appnexus","params":{"placementId":********,"type":"display"}},{"bidder":"pubmatic","params":{"adSlot":"6405112","type":"display","publisherId":"158139"}},{"bidder":"triplelift","params":{"inventoryCode":"SeriousEats_leaderboardac_728x90_pbc2s","type":"display"}},{"bidder":"openx","params":{"unit":"*********","type":"display","delDomain":"meredith-d.openx.net"}},{"bidder":"ix","params":{"siteId":"830468","type":"display"}}]},"leaderboard-fixed\/tier1":{"prebid":[{"bidder":"rubicon","params":{"zoneId":2442176,"type":"display","accountId":7499,"siteId":426848}},{"bidder":"appnexus","params":{"placementId":********,"type":"display"}},{"bidder":"pubmatic","params":{"adSlot":"4515355","type":"display","publisherId":"158139"}},{"bidder":"triplelift","params":{"inventoryCode":"SeriousEats_leaderboard-fixed_728x90_pbc2s","type":"display"}},{"bidder":"openx","params":{"unit":"*********","type":"display","delDomain":"meredith-d.openx.net"}},{"bidder":"ix","params":{"siteId":"830467","type":"display"}}]},"leaderboard-flex\/tier1":{"prebid":[{"bidder":"rubicon","params":{"zoneId":2442184,"type":"display","accountId":7499,"siteId":426848}},{"bidder":"appnexus","params":{"placementId":********,"type":"display"}},{"bidder":"pubmatic","params":{"adSlot":"4515049","type":"display","publisherId":"158139"}},{"bidder":"triplelift","params":{"inventoryCode":"SeriousEats_leaderboard-flex_970x250_pbc2s","type":"display"}},{"bidder":"openx","params":{"unit":"*********","type":"display","delDomain":"meredith-d.openx.net"}},{"bidder":"ix","params":{"siteId":"830468","type":"display"}}]},"leaderboardfooter-flex\/tier1":{"prebid":[{"bidder":"rubicon","params":{"zoneId":2442176,"type":"display","accountId":7499,"siteId":426848}},{"bidder":"appnexus","params":{"placementId":********,"type":"display"}},{"bidder":"pubmatic","params":{"adSlot":"4632082","type":"display","publisherId":"158139"}},{"bidder":"triplelift","params":{"inventoryCode":"SeriousEats_leaderboardfooter-flex_970x250_pbc2s","type":"display"}},{"bidder":"openx","params":{"unit":"*********","type":"display","delDomain":"meredith-d.openx.net"}},{"bidder":"ix","params":{"siteId":"830467","type":"display"}}]},"square-fixed\/tier1":{"prebid":[{"bidder":"rubicon","params":{"zoneId":2442186,"type":"display","accountId":7499,"siteId":426848}},{"bidder":"appnexus","params":{"placementId":********,"type":"display"}},{"bidder":"pubmatic","params":{"adSlot":"4515050","type":"display","publisherId":"158139"}},{"bidder":"triplelift","params":{"inventoryCode":"SeriousEats_square-fixed_300x250_pbc2s","type":"display"}},{"bidder":"openx","params":{"unit":"*********","type":"display","delDomain":"meredith-d.openx.net"}},{"bidder":"ix","params":{"siteId":"830474","type":"display"}}]},"square-flex\/tier1":{"prebid":[{"bidder":"rubicon","params":{"zoneId":2442194,"type":"display","accountId":7499,"siteId":426848}},{"bidder":"appnexus","params":{"placementId":********,"type":"display"}},{"bidder":"pubmatic","params":{"adSlot":"4515054","type":"display","publisherId":"158139"}},{"bidder":"triplelift","params":{"inventoryCode":"SeriousEats_square-flex_300x600_pbc2s","type":"display"}},{"bidder":"openx","params":{"unit":"*********","type":"display","delDomain":"meredith-d.openx.net"}},{"bidder":"ix","params":{"siteId":"830482","type":"display"}}]},"preroll":{"prebid":[{"bidder":"ttd","params":{"type":"instream","supplySourceId":"dotdash","publisherId":"1"}},{"bidder":"rubicon","params":{"type":"instream","zoneId":2442196,"accountId":7499,"siteId":426848}},{"bidder":"appnexus","params":{"type":"instream","placementId":********}},{"bidder":"pubmatic","params":{"type":"instream","adSlot":"4515055","publisherId":"158139"}},{"bidder":"openx","params":{"type":"instream","unit":"*********","delDomain":"meredith-d.openx.net"}},{"bidder":"ix","params":{"type":"instream","siteId":"830485"}}]},"square-fixed-comments\/tier1":{"prebid":[{"bidder":"rubicon","params":{"zoneId":3546544,"type":"display","accountId":7499,"siteId":426848}},{"bidder":"appnexus","params":{"placementId":********,"type":"display"}},{"bidder":"pubmatic","params":{"adSlot":"6234498","type":"display","publisherId":"158139"}},{"bidder":"triplelift","params":{"inventoryCode":"seriouseats_square-fixed-comments_300x250_pbc2s","type":"display"}},{"bidder":"openx","params":{"unit":"*********","type":"display","delDomain":"meredith-d.openx.net"}},{"bidder":"ix","params":{"siteId":"1137140","type":"display"}}]},"square-fixed-replies\/tier1":{"prebid":[{"bidder":"rubicon","params":{"zoneId":3601230,"type":"display","accountId":7499,"siteId":426848}},{"bidder":"appnexus","params":{"placementId":********,"type":"display"}},{"bidder":"pubmatic","params":{"adSlot":"6389421","type":"display","publisherId":"158139"}},{"bidder":"triplelift","params":{"inventoryCode":"seriouseats_square-fixed-replies_300x250_pbc2s","type":"display"}},{"bidder":"openx","params":{"unit":"*********","type":"display","delDomain":"meredith-d.openx.net"}},{"bidder":"ix","params":{"siteId":"1146776","type":"display"}}]}},"default":{"prebid":[{"bidder":"ttd","params":{"supplySourceId":"dotdash","publisherId":"1","type":"display"}},{"bidder":"rubicon","params":{"accountId":7499,"siteId":426848,"type":"display"}},{"bidder":"pubmatic","params":{"publisherId":"158139","type":"display"}},{"bidder":"openx","params":{"delDomain":"meredith-d.openx.net","type":"display"}}],"prebidModules":["prebid-core.js","rtdModule.js","adpod.js","allowActivities.js","atsAnalyticsAdapter.js","consentManagement.js","fledgeForGpt.js","criteoIdSystem.js","debugging.js","dfpAdServerVideo.js","gdprEnforcement.js","iasRtdProvider.js","identityLinkIdSystem.js","liveIntentIdSystem.js","paapi.js","pairIdSystem.js","prebidServerBidAdapter.js","priceFloors.js","sharedIdSystem.js","uid2IdSystem.js","unifiedIdSystem.js","userId.js","appnexusBidAdapter.js","ixBidAdapter.js","pubmaticBidAdapter.js","rubiconBidAdapter.js","ttdBidAdapter.js","tripleliftBidAdapter.js","openxBidAdapter.js"]},"ab":[]};
    console.log('✅ Libra is loaded!');
    Mntl.RTB.setLatencyBuffer(0);
    (function() {
    Mntl.utilities.onLoad(function() {
    const geolocationResponse = {};
    const externalJS = {
    src: '\//cdn.cookielaw.org/scripttemplates/otSDKStub.js',
    'data-domain-script': '56ce45ed-3e95-4461-aa63-9ab2fe71f604',
    'data-ignore-ga': true,
    'data-language': 'en',
    async: true,
    charset: 'UTF-8',
    onerror: 'Mntl.CMP.onError()',
    id: 'onetrust-script'
    };
    geolocationResponse.stateCode = 'CA';
    geolocationResponse.countryCode = 'US';
    geolocationResponse.regionCode = 'US';
    if (Object.keys(geolocationResponse).length) {
    window.OneTrust = window.OneTrust || {};
    window.OneTrust.geolocationResponse = geolocationResponse;
    }
    const preferenceCenterTrigger = document.querySelector('.ot-pref-trigger');
    preferenceCenterTrigger.addEventListener('click', (e) => {
    e.preventDefault();
    Mntl.utilities.loadExternalJS(
    externalJS
    , () => {
    Mntl.CMP.onSdkLoaded(() => {
    OneTrust.ToggleInfoDisplay();
    })
    });
    },
    { once: true });
    });
    Mntl.CMP.init({
    isConsentRequired: true,
    oneTrustTemplateName: 'ccpa',
    showConsentBanner: false,
    isCcpaApplicableRequest: true,
    isTcfEnabled: true,
    scriptTimeout: 3000
    });
    })();
    (function(){
    const mantleDependencies = {
    }
    ;
    const pageTargeting = {
    customSeries: ''
    ,abtest: 'lcid1'
    ,tax0: 'se'
    ,rid: 'n40a218489f2649cabda4435c8f5b6f6822'
    ,custom: ''
    ,sid: 'n4c4f3e711ef341e39f43c983724bd55221'
    }
    ;
    const baseSlotTargeting = {
    gtemplate: 'taxonomy'
    ,leaid: '156569'
    ,revenueGroup: ''
    ,docId: '5094396'
    ,viewtype: 'home'
    ,type: 'homepage'
    ,t: '100'
    ,au: '156569'
    ,jny: '0'
    ,leuid: '153185598102756'
    ,dload: '0'
    ,sbj: 'pidse_root'
    ,id: '5094396'
    ,aid: ''
    ,jnyroot: ''
    }
    ;
    const adLazyOffset = {'default': 500,'mob-square-fixed-intro-1': 750};
    pageTargeting.w = Mntl.utilities.getW();
    const initialSlots = [];
    initialSlots.push({
    config: {
    id: 'leaderboard-flex-1',
    sizes: [[728, 90], [970,90], [970, 250]],
    type: 'leaderboard',
    rtb: true,
    timedRefresh: 0,
    waitForThirdParty: false
    },
    targeting: Mntl.fnUtilities.deepExtend({}, {
    pos: 'atf',
    priority: 1
    })
    });
    const testIds = Mntl.GPT.getTestIds();
    pageTargeting.ab = testIds;
    pageTargeting.bts = testIds;
    Mntl.utilities.onLoad(function() {
    Mntl.utilities.loadExternalJS({
    src: '//securepubads.g.doubleclick.net/tag/js/gpt.js',
    async: false
    });
    });
    const options = {
    domain: 'www.seriouseats.com',
    templateName: 'taxonomysc',
    isMobile: false,
    isTablet: false,
    isDesktop: true,
    dfpId: '3865',
    publisherProvidedId: '441bdb9e-1da2-45ac-8700-3c21d36989ec',
    singleRequest: false,
    useLmdFormat: true,
    useOxygen: true,
    restrictLeaderboardFlex1HeightAndOverflow: false,
    prebidConfigApi: false,
    useInfiniteRightRail: true,
    loadAdsAboveViewport: false,
    fiftyPercentAdRefresh: true,
    waitForNextFramePaint: false,
    revenueGroupAllowList: '',
    lmdSiteCode: 'hlt',
    pageTargeting,
    baseSlotTargeting,
    adLazyOffset,
    geo: {
    isInEurope: false,
    isInUsa: true
    },
    initialSlots,
    auctionFloors: {"billboard2":{"id":"22260ce9e15c474a885834cbe3d6939d","floor":"5"},"billboard6":{"id":"4524acfdc0ae43fdad722dc52d862507","floor":"25"},"billboard5":{"id":"421df349c5614d89878c2a32f0011db0","floor":"25"},"billboard4":{"id":"be2ee8f9ff1549bd9afc28946233d6a4","floor":"25"},"billboard3":{"id":"1f5e1f18c01f4d04b6508dd594c28c12","floor":"5"},"billboard9":{"id":"22736bac8ba74609ae79c38bb3d1a863","floor":"25"},"billboard8":{"id":"b1d98163802c487eab47935b3103c065","floor":"105"},"billboard7":{"id":"a4c95ba5cd894e0597d2eea7271d760e","floor":"25"},"billboardfooter":{"id":"b466bd5b8e2b40d699ea330746063bca","floor":"100"},"leaderboardfooter2":{"id":"fad0cb14d4a249d2a0ccb0b88844970f","floor":"400"},"square-flex-2":{"id":"c0de64d276164d38987d845d926fa6c9","floor":"35"},"square-flex-1":{"id":"1034f613802448ca96d11f7395d7fc56","floor":"30"},"adhesive":{"id":"a59ef8677ef9495db2e4a382e3deb32a","floor":"100"},"square-fixed-4":{"id":"a8e17679ce1d4a529a45dd80f5169094","floor":"40"},"square-fixed-3":{"id":"ffa60fd3d7124c92b0d1e08d56f7cf0d","floor":"40"},"square-fixed-2":{"id":"c4414450c3184fbea4d9d6101b12f581","floor":"30"},"square-fixed-1":{"id":"c4f06758b46f4b9c9e76632e8b83a368","floor":"30"},"square-fixed-8":{"id":"4b5b1cba2a4742be99282668439bcbb0","floor":"35"},"mob-square-fixed-8":{"id":"bd4a78a427594a5781446339aab14d78","floor":"25"},"square-fixed-7":{"id":"3240674b8d1b4a07a35e5e43b7ce5e7a","floor":"35"},"mob-square-fixed-9":{"id":"ddbf7310f8b74d19ada5b80a3e5a67df","floor":"25"},"square-fixed-6":{"id":"5720fa7f8e8b47f39eaf4fa6a175642f","floor":"40"},"square-fixed-5":{"id":"cf2e35d812be4448ba2c3019d712ab03","floor":"35"},"billboard":{"id":"93798cd8f699403d8b02c8b67083f183","floor":"5"},"leaderboard":{"id":"12453a1b94934ea1a9da955ff1c17639","floor":"400"},"square-fixed":{"id":"33d4c717567f4456ae503a0903304eab","floor":"5"},"dynamic-inline":{"id":"823a2e2758b0449982e6788dfac58ef4","floor":"25"},"square-fixed-9":{"id":"a73fc97433414f32a3b020c3b16731eb","floor":"35"},"billboard10":{"id":"63bcf9a34f03488eb18bbaa574df5c09","floor":"25"},"billboard11":{"id":"c56bac8d82d449a782ecf0a63303dd6a","floor":"25"},"other":{"id":"6eafe638f69449748cdfd461b25e4f13","floor":"50"},"billboardfooter2":{"id":"dc8b29e7cb284df9a11bb25c3e29f8b7","floor":"100"},"leaderboardac":{"id":"1a7342091e1b4afe92cab3c71d00a10d","floor":"85"},"leaderboard-fixed":{"id":"1a7af217822a4b2395562143f10dfbdc","floor":"25"},"leaderboard-flex-1":{"id":"ecac216151a945c28719a8f84d8d239a","floor":"55"},"leaderboard-flex-2":{"id":"bd5f3ba3308d4a5db5ec69b24d223a93","floor":"5"},"leaderboard-fixed-3":{"id":"59d07d51314645798507271efdb7d0a1","floor":"40"},"leaderboard-fixed-4":{"id":"90d33f6f30f947d48cd9066bb9f36375","floor":"30"},"leaderboard-fixed-1":{"id":"a172e85b69784924994cd61cb029336c","floor":"40"},"leaderboard-fixed-2":{"id":"8cce37f7229044e89ab649c125ee9808","floor":"40"},"leaderboard-fixed-7":{"id":"0c592a0f93c5441294e1a636a9e957c0","floor":"130"},"leaderboard-fixed-8":{"id":"f04cb9d3886d49c9948662cbafb407a7","floor":"5"},"leaderboardfooter-flex-2":{"id":"3d892638351b48fd98ee8cae02bbda89","floor":"110"},"leaderboard-fixed-5":{"id":"a19af943f9b74b98a8c7a1e334c1e2b7","floor":"35"},"leaderboardfooter-flex-1":{"id":"9ba0828ef2f44fe9935a6ec161bf23b8","floor":"70"},"leaderboard-fixed-6":{"id":"997ff418120540ac830eb248d6424156","floor":"100"},"leaderboard-fixed-0":{"id":"16c2d4292af14c3da18649f234f79faa","floor":"35"},"inline":{"id":"00470bedc0324966b980f4922a1988de","floor":"5"},"leaderboardfooter":{"id":"bdd125e5d69b4e5f85aaef539b376593","floor":"20"},"leaderboard-fixed-9":{"id":"9b9f6c7f230a4577ad35ca2187873b7d","floor":"5"},"mob-adhesive-banner-fixed":{"id":"ab9f4a4589c942e1b8e0d75965cbcbae","floor":"40"}},
    utils: {
    buildGptUrl: Lifestyle.GPT.buildGptUrl
    },
    displayOnScroll: false,
    displayOnConsent: true,
    adsToCollapse: ['leaderboard*','square*']
    };
    if (Mntl.AdMetrics) {
    Mntl.AdMetrics.init("5094396", "n40a218489f2649cabda4435c8f5b6f6822", initialSlots.map(slot => slot.config.id), Date.now());
    } else {
    Mntl.AdMetrics = { pushMetrics: () => {} };
    }
    Mntl.GPT.setMantleDependencies(mantleDependencies);
    Mntl.GPT.init(options);
    }());window.addEventListener('readyForThirdPartyTracking', () => {
    // Set a delay for loading the script
    // Specify the delay duration in pushly.xml
    const delay = '8';
    window.setTimeout(() => {
    Mntl.utilities.loadExternalJS({
    src: 'https://cdn.p-n.io/pushly-sdk.min.js?domain_key=M2rJmhNdRnraoHmAGUhCLrvnxJ0xFr6P4gqW',
    async: true
    });
    window.PushlySDK = window.PushlySDK || [];
    // eslint-disable-next-line prefer-rest-params
    function pushly() { window.PushlySDK.push(arguments); }
    pushly('load', {
    domainKey: 'M2rJmhNdRnraoHmAGUhCLrvnxJ0xFr6P4gqW',
    sw: '/pushly-sdk-worker.js'
    });
    }, parseFloat(delay) * 1000);
    });
    window.dataLayer = window.dataLayer || [];
    // moved from gtm.ftl so we can initialize GTM only onLoad. From https://support.google.com/tagmanager/answer/6103696?hl=en
    Mntl.utilities.onLoad(function() {
    (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;defer=true;j.src='//www.googletagmanager.com/gtm.js?id='+i+dl;j.type="text/javascript";f.parentNode.insertBefore(j,f)})(window,document,'script','dataLayer','GTM-5P3SZGS');
    });
    window.dataLayer.push({
    event: 'ab-proctor',
    'abTests-proctor': {
    "99-0"
    : "useOxygen | useOxygen | use the Oxygenated ad unit format and slot names | 1"
    ,
    "99-1"
    : "vanillaJSLazyAdRecipeSC | active | vanillaLazyAd.js enabled | 1"
    ,
    "99-2"
    : "userIdAuctionDelay | ldd | legacy Dotdash domains | 2"
    ,
    "99-3"
    : "useFloorSearch | active | Use Floor Search | 1"
    ,
    "78"
    : "updatedArticleHeader | active | | 1"
    ,
    "99-5"
    : "fiftyPercentAdRefresh | active | Ads refresh when 50% in view | 1"
    ,
    "99-6"
    : "continuousScrollMobSquareFlexOffset | active | Adds offset to mob-square-fixed-1 ad on CS articles | 1"
    ,
    "99-7"
    : "recipescDesktopAdRefresh | active | active, right rail ads timed refresh active | 1"
    ,
    "99-8"
    : "moveEquipmentBlock | control | Control - homepage-equipment block will appear where it original was | 0"
    ,
    "99-9"
    : "gamVideoUsePlcmt | active | Pass plcmt | 1"
    ,
    "99-10"
    : "liveIntentConnectedIdTest | control | LiveIntent ConnectedID is enabled | 0"
    ,
    "99-11"
    : "prebidConfigApi | active | Ad Config API is enabled | 1"
    ,
    "99-12"
    : "useDynamicVideoSizes | active | active | 1"
    ,
    "99-13"
    : "rtbTracking | newevents | | 2"
    ,
    "99-14"
    : "orion | control | Control | 0"
    ,
    "99-15"
    : "prebidVideoUsePlcmt | active | Pass plcmt | 1"
    ,
    "99-16"
    : "useLiveIntentSignedInUser | active | active | 1"
    ,
    "99-17"
    : "removeAdTiers | active | active | 1"
    ,
    "99-18"
    : "useLiveRamp | active | active | 1"
    ,
    "99-19"
    : "defaultLazyLoadOffset | activeDesktop | Active on desktop homepage | 2"
    }
    });
    window.dataLayer.push({
    envData: {
    environment: {
    environment: "k8s-prod",
    application: "seriouseats",
    dataCenter: "us-west-1"
    },
    server: {
    version: "6.85.0",
    title: "seriouseats-launcher"
    },
    client : {
    browserUA: navigator.userAgent,
    serverUA: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36",
    deviceType: "pc",
    usStateCode: "CA"
    },
    mantle: "4.0.763",
    commerce: ""
    }
    });
    (function(fnUtils, CMP) {
    const deferLoadTime = 5000;
    const readyForThirdPartyTrackingEvent = new CustomEvent('readyForThirdPartyTracking', { bubbles: true });
    const readyForThirdPartyTracking = fnUtils.once(function() {
    window.dataLayer.push({event: 'readyForThirdPartyTracking'});
    window.dispatchEvent(readyForThirdPartyTrackingEvent);
    });
    const readyForDeferredScriptsEvent = new CustomEvent('readyForDeferredScripts', { bubbles: true });
    const readyForDeferredScripts = fnUtils.once(function() {
    window.dataLayer.push({event: 'readyForDeferredScripts'});
    window.dispatchEvent(readyForDeferredScriptsEvent);
    });
    const hasTargetingConsentHandler = function() {
    const hasConsent = CMP.hasTargetingConsent();
    if (hasConsent) {
    readyForThirdPartyTracking();
    }
    // Trigger the readyForDeferredScripts if consent is given
    // or if the user has closed the banner(AlertBox) which exists for EU
    // or if the user optin consent is not required, which applies for non gdpr locations
    if (hasConsent || CMP.isAlertBoxClosed() || !CMP.isOptInConsent()) {
    readyForDeferredScripts();
    }
    return hasConsent;
    };
    const purposeOneConsentHandler = async function() {
    const hasPurposeOneConsent = await CMP.hasPurposeOneConsent();
    if (hasPurposeOneConsent) {
    readyForThirdPartyTracking();
    }
    if (hasPurposeOneConsent || CMP.isAlertBoxClosed()) {
    readyForDeferredScripts();
    }
    return hasPurposeOneConsent;
    };
    const onRequiredDomEvent = fnUtils.once(function() {
    if (!CMP) {
    readyForThirdPartyTracking();
    readyForDeferredScripts();
    return;
    }
    const handler = CMP.supportsTCData() ? purposeOneConsentHandler : hasTargetingConsentHandler;
    if (!CMP.isLoading()) {
    handler();
    }
    CMP.onConsentChange(handler);
    });
    [
    ['adRendered', onRequiredDomEvent],
    ['beforeunload', onRequiredDomEvent],
    ['load', function() { setTimeout(onRequiredDomEvent, deferLoadTime); }]
    ].forEach(function(event) {
    window.addEventListener(event[0], event[1], { once: true });
    });
    })(Mntl.fnUtilities || {}, Mntl.CMP);window.dataLayer = window.dataLayer || [];
    (function() {
    var isContinuousScroll = document.querySelector('.mntl-continuous-scroll');
    var pageViewDataAsJSON = {"country":"US","description":"Serious Eats is the destination for delicious food, with definitive recipes, trailblazing science, and essential guides to eating and knowing all about the best food, wherever you are.","revenueGroup":"","contentGroup":"Other","title":"Serious Eats" || document.title || '',"authorNames":"Staff Author","viewType":"HOME","templateId":"100","muid":"441bdb9e-1da2-45ac-8700-3c21d36989ec","lastEditingAuthorId":"156569","lastEditingUserId":"153185598102756","authorId":"156569","documentId":5094396,"templateName":"TAXONOMYSC","fullUrl":"https://www.seriouseats.com/" + location.hash,"experienceType":"single page","entryType":"direct","excludeFromComscore":false,"internalSessionId":"n4c4f3e711ef341e39f43c983724bd55221","internalRequestId":"n40a218489f2649cabda4435c8f5b6f6822","hid":"","experienceTypeName":"","recircDocIdsFooter":"","euTrafficFlag":false,"isGoogleBot":false,"mantleVersion":"4.0.763","commerceVersion":"","primaryTaxonomyNames":"Serious Eats","primaryTaxonomyIds":"5094396"};
    var scrolledPageData = {};
    var scrolledDocOrdinal;
    var scrolledPage;
    pageViewDataAsJSON.breakpointName = Lifestyle.Utilities.getW();
    if (isContinuousScroll) {
    pageViewDataAsJSON.experienceTypeName = 'continuous';
    if (window.dataLayer && window.dataLayer.length) {
    //loop through events and collect previous scrolledDocOrdinal and scrolledPage values
    scrolledPageData = window.dataLayer.reduce( (acc, curr) => {
    if (curr.event == 'unifiedPageview') {
    acc.scrolledDocOrdinal = acc.scrolledDocOrdinal ?
    acc.scrolledDocOrdinal + 1
    : 1;
    acc.scrolledPage = acc.scrolledPage ?
    acc.scrolledPage + " | " + (curr.documentId).toString()
    : (curr.documentId).toString();
    }
    return acc;
    }, {});
    }
    scrolledPage = scrolledPageData.scrolledPage ? scrolledPageData.scrolledPage + ' | ' + (pageViewDataAsJSON.documentId).toString() : (pageViewDataAsJSON.documentId).toString();
    scrolledDocOrdinal = scrolledPageData.scrolledDocOrdinal ? scrolledPageData.scrolledDocOrdinal + 1 : 1;
    pageViewDataAsJSON.scrolledPage = scrolledPage;
    pageViewDataAsJSON.scrolledDocOrdinal = scrolledDocOrdinal;
    }
    Mntl.utilities.onLoad(function() {
    Mntl.PageView.init(pageViewDataAsJSON);
    });
    })();</script>
    <link rel="shortcut icon" href="/favicon.ico">
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="apple-touch-icon-precomposed" sizes="57x57" href="/apple-touch-icon-57x57.png">
    <link rel="apple-touch-icon-precomposed" sizes="60x60" href="/apple-touch-icon-60x60.png">
    <link rel="apple-touch-icon-precomposed" sizes="76x76" href="/apple-touch-icon-76x76.png">
    <link rel="apple-touch-icon-precomposed" sizes="120x120" href="/apple-touch-icon-120x120.png">
    <link rel="apple-touch-icon-precomposed" sizes="152x152" href="/apple-touch-icon-152x152.png">
    <link rel="apple-touch-icon-precomposed" sizes="180x180" href="/apple-touch-icon-180x180.png">
    <meta name="msapplication-TileColor" content="#F4F4F4">
    <meta name="msapplication-TileImage" content="/static/6.85.0/icons/favicons/mstile-144x144.png">
    <meta name="msapplication-square70x70logo" content="/static/6.85.0/icons/favicons/mstile-70x70.png">
    <meta name="msapplication-square150x150logo" content="/static/6.85.0/icons/favicons/mstile-150x150.png">
    <meta name="msapplication-square310x310logo" content="/static/6.85.0/icons/favicons/mstile-310x310.png">
    <meta name="msapplication-wide310x150logo" content="/static/6.85.0/icons/favicons/mstile-310x150.png">
    <meta name="p:domain_verify" content="6cec8d379619467ba6540a1e71e36b79">
    <meta name="fb:pages" content="173536029329712">
    <script type="application/ld+json">
    {
    "@context": "http://schema.org",
    "@type": "WebPage",
    "mainEntityOfPage": {
    "@type": "Webpage",
    "@id": "https://www.seriouseats.com/"
    },
    "headline": "Serious Eats"
    ,"potentialAction": {
    "@type": "SearchAction",
    "target": "https://www.seriouseats.com/search?q={search_term_string}",
    "query-input": "required name=search_term_string'"
    }
    ,"publisher": {
    "@type": "Organization",
    "name": "Serious Eats",
    "url": "https://www.seriouseats.com",
    "logo": {
    "@type": "ImageObject",
    "url": "https://www.seriouseats.com/thmb/YfQU2tc6Ask4mMy6oMJDaulMqqE=/320x320/filters:no_upscale():max_bytes(150000):strip_icc()/Serious_Eats_Schema_Logo-30a63d9c07734d35836c8ff2d368419c.png",
    "width": 320,
    "height": 320
    },
    "brand": "Serious Eats"
    , "publishingPrinciples": "https://www.seriouseats.com/about-us-5120006#toc-editorial-guidelines"
    , "sameAs" : [
    "https://www.facebook.com/seriouseats",
    "https://www.instagram.com/seriouseats",
    "https://www.pinterest.com/seriouseats"
    ]
    , "address": {
    "@type": "PostalAddress",
    "streetAddress": "225 Liberty Street, 4th Floor",
    "addressLocality": "New York",
    "addressRegion": "NY",
    "postalCode": "10281",
    "addressCountry": "USA"
    }
    , "parentOrganization" : {
    "url": "https://www.dotdashmeredith.com",
    "brand": "Dotdash Meredith",
    "name": "Dotdash Meredith",
    "address": {
    "@type": "PostalAddress",
    "streetAddress": "225 Liberty Street, 4th Floor",
    "addressLocality": "New York",
    "addressRegion": "NY",
    "postalCode": "10281",
    "addressCountry": "USA"
    },
    "logo": {
    "@type": "ImageObject",
    "url": "https://www.seriouseats.com/thmb/UrZUYJQsAwN-jGDeycKgTz1FKPg=/234x60/filters:no_upscale():max_bytes(150000):strip_icc()/dotdash-logo-e9cde67f713a45c68ce5def51d3ca409.jpg",
    "width": 234,
    "height": 60
    },
    "sameAs" : [
    "https://en.wikipedia.org/wiki/Dotdash_Meredith",
    "https://www.instagram.com/dotdashmeredith/",
    "https://www.linkedin.com/company/dotdashmeredith/",
    "https://www.facebook.com/dotdashmeredith/"
    ]
    }
    }
    ,"description": "Serious Eats is the destination for delicious food, with definitive recipes, trailblazing science, and essential guides to eating and knowing all about the best food, wherever you are."
    }</script>
    <!-- NLWeb Dropdown Chat CSS -->
    <link rel="stylesheet" href="../common-chat-styles.css">
    <link rel="stylesheet" href="../nlweb-dropdown-chat.css">
    <script type="text/javascript" async="true" src="//c.amazon-adsystem.com/aax2/apstag.js" data-loaded="loaded"></script><script type="text/javascript" async="true" src="https://d30qdagvt44524.cloudfront.net/production/segments?muid=441bdb9e-1da2-45ac-8700-3c21d36989ec" data-loaded="loaded"></script><script type="text/javascript" async="true" src="//js-sec.indexww.com/ht/p/184003-52190608802424.js" data-loaded="loaded"></script><script type="text/javascript" async="true" src="/static/6.85.0/static/cache/eNqdlttu2zAMhl9oQbC-QbalRYEFK3q6l0XGZSOLAkW58Z5-THa42oWpK8sCP1H-Jf70tmpQituJoSWs26FluDzf67YIDgQ2AKr6520TWfDTdi0kCodrzHokQGFwhKfEH7uoNJMSVgeodZdDWiy47iAURVkPR84Vsx5CDiNONlqPHhPCiLcsd8VBRSFFvoenpSpO6znAoY0j5dGBHMsOnlBmlFcC5PXkCEX2-cgSnZpQqI8KD8KzJXQcg0VnJV2-Uz75tUk0431WW8LPlhAKecJJOpJc334fxRcC9y0tQhFvE7M46qK-BUHwb7YR3HRQmY7Ula6iKeoo91Iynlvt0ZHOXeq3YbpE9rDSBjKP6UFVoQsTKgkTHbWH5oK5S6T41vLJ2VSioAXOuBHMZhcoG5suKSyO_Q7vGPWuBfFcof52MRWHixZ9UUqOko1NTIq4eLGr9k4nq_QTnXnMGT7vCXzEjY9g0eErZzNKX40OdX-2DlDJGrqjRfIUKP-wbGIt6JkfmfXbdW79GidcPlg83_jXwnqO2WMEIVfr4dNDkDC5kwXFkWV5vqySjOF8sH27fkCapP9n_Re5P2Nsl7V_Adq4HRY.js" data-loaded="loaded"></script><script type="text/javascript" async="true" src="//b-code.liadm.com/a-07sk.min.js" data-loaded="loaded"></script><script type="text/javascript" src="//securepubads.g.doubleclick.net/tag/js/gpt.js" async="false" data-loaded="loaded"></script><meta http-equiv="origin-trial" content="AlK2UR5SkAlj8jjdEc9p3F3xuFYlF6LYjAML3EOqw1g26eCwWPjdmecULvBH5MVPoqKYrOfPhYVL71xAXI1IBQoAAAB8eyJvcmlnaW4iOiJodHRwczovL2RvdWJsZWNsaWNrLm5ldDo0NDMiLCJmZWF0dXJlIjoiV2ViVmlld1hSZXF1ZXN0ZWRXaXRoRGVwcmVjYXRpb24iLCJleHBpcnkiOjE3NTgwNjcxOTksImlzU3ViZG9tYWluIjp0cnVlfQ=="><meta http-equiv="origin-trial" content="Amm8/NmvvQfhwCib6I7ZsmUxiSCfOxWxHayJwyU1r3gRIItzr7bNQid6O8ZYaE1GSQTa69WwhPC9flq/oYkRBwsAAACCeyJvcmlnaW4iOiJodHRwczovL2dvb2dsZXN5bmRpY2F0aW9uLmNvbTo0NDMiLCJmZWF0dXJlIjoiV2ViVmlld1hSZXF1ZXN0ZWRXaXRoRGVwcmVjYXRpb24iLCJleHBpcnkiOjE3NTgwNjcxOTksImlzU3ViZG9tYWluIjp0cnVlfQ=="><meta http-equiv="origin-trial" content="A9wSqI5i0iwGdf6L1CERNdmsTPgVu44ewj8QxTBYgsv1LCPUVF7YmWOvTappqB1139jAymxUW/RO8zmMqo4zlAAAAACNeyJvcmlnaW4iOiJodHRwczovL2RvdWJsZWNsaWNrLm5ldDo0NDMiLCJmZWF0dXJlIjoiRmxlZGdlQmlkZGluZ0FuZEF1Y3Rpb25TZXJ2ZXIiLCJleHBpcnkiOjE3MzY4MTI4MDAsImlzU3ViZG9tYWluIjp0cnVlLCJpc1RoaXJkUGFydHkiOnRydWV9"><meta http-equiv="origin-trial" content="A+d7vJfYtay4OUbdtRPZA3y7bKQLsxaMEPmxgfhBGqKXNrdkCQeJlUwqa6EBbSfjwFtJWTrWIioXeMW+y8bWAgQAAACTeyJvcmlnaW4iOiJodHRwczovL2dvb2dsZXN5bmRpY2F0aW9uLmNvbTo0NDMiLCJmZWF0dXJlIjoiRmxlZGdlQmlkZGluZ0FuZEF1Y3Rpb25TZXJ2ZXIiLCJleHBpcnkiOjE3MzY4MTI4MDAsImlzU3ViZG9tYWluIjp0cnVlLCJpc1RoaXJkUGFydHkiOnRydWV9"><script src="https://securepubads.g.doubleclick.net/pagead/managed/js/gpt/m202412090101/pubads_impl.js" async="" data-loaded="loaded"></script><link href="https://securepubads.g.doubleclick.net/pagead/managed/dict/m202412050101/gpt" rel="compression-dictionary"><script esp-signal="true" src="https://static.criteo.net/js/ld/publishertag.ids.js" data-loaded="loaded"></script><script esp-signal="true" src="https://oa.openxcdn.net/esp.js" data-loaded="loaded"></script><script src="https://config.aps.amazon-adsystem.com/configs/3446" type="text/javascript" async="async" data-loaded="loaded"></script><script async="async" defer="defer" src="https://launchpad-wrapper.privacymanager.io/************************************/launchpad-liveramp.js" data-loaded="loaded"></script><script src="//secure.cdn.fastclick.net/js/pubcid/latest/pubcid.min.js" data-loaded="loaded"></script><script src="//cdn.id5-sync.com/api/1.0/id5-api.js" data-loaded="loaded"></script><script type="text/javascript" src="https://cdn.p-n.io/pushly-sdk.min.js?domain_key=M2rJmhNdRnraoHmAGUhCLrvnxJ0xFr6P4gqW" async="true" data-loaded="loaded"></script><script type="text/javascript" src="https://www.lightboxcdn.com/vendor/55631c8a-65ef-454b-9544-3e8e173a3af3/lightbox_inline.js" data-loaded="loaded"></script><link type="text/css" rel="stylesheet" media="screen" href="https://www.lightboxcdn.com/static/fb_lightbox.2.1.5.css?cb=20240725"><style type="text/css">.fb_lightbox-margin{margin-right:0px;}</style></head><body><div style="display: none;" id="lightboxjs-lightboxlib"><div><iframe frameborder="0" id="lightboxjs-frame-lightboxlib"></iframe></div></div>
    <!-- resourcesvgftl -->
    <svg class="mntl-svg-resource is-hidden">
    <defs>
    <symbol id="icon-hamburger">
    <svg viewBox="0 0 24 24" fill="inherit" xmlns="http://www.w3.org/2000/svg">
    <circle cx="3" cy="3" r="2.5" fill="inherit" stroke="inherit"></circle>
    <circle cx="3" cy="12" r="2.5" fill="inherit" stroke="inherit"></circle>
    <circle cx="3" cy="21" r="2.5" fill="inherit" stroke="inherit"></circle>
    <circle cx="12" cy="3" r="2.5" fill="inherit" stroke="inherit"></circle>
    <circle cx="12" cy="12" r="2.5" fill="inherit" stroke="inherit"></circle>
    <circle cx="12" cy="21" r="2.5" fill="inherit" stroke="inherit"></circle>
    <circle cx="21" cy="3" r="2.5" fill="inherit" stroke="inherit"></circle>
    <circle cx="21" cy="12" r="2.5" fill="inherit" stroke="inherit"></circle>
    <circle cx="21" cy="21" r="2.5" fill="inherit" stroke="inherit"></circle>
    </svg>
    </symbol>
    <symbol id="icon-search">
    <svg viewBox="0 0 22 29" fill="inherit" xmlns="http://www.w3.org/2000/svg">
    <rect x="11.6139" y="7.83164" width="3.35133" height="20.5944" rx="1.67567" transform="rotate(30 11.6139 7.83164)" fill="#FBF9EE" stroke="#1B4D7A"></rect>
    <circle r="9.5" transform="matrix(-1 8.74228e-08 8.74228e-08 1 12 10)" fill="#FFD12F" stroke="#1B4D7A"></circle>
    <circle r="6.5" transform="matrix(-1 8.74228e-08 8.74228e-08 1 12 10)" fill="inherit" stroke="#1B4D7A"></circle>
    <path d="M15.4648 12.0002C16.5694 10.0871 15.9139 7.6407 14.0007 6.53613" stroke="#068192" stroke-linecap="round"></path>
    </svg> </symbol>
    <symbol id="icon-search--header">
    <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 18.7 25.7">
    <path d="M12.6,15.6l0.5-0.3c3.3-2.2,4.4-6.6,2.4-10C13.4,1.7,8.8,0.4,5.3,2.5C1.7,4.6,0.4,9.2,2.5,12.8c1.9,3.3,6,4.6,9.5,3.1L12.6,15.6z M13.9,16.5l4.6,8c0.2,0.4,0.1,0.8-0.3,1s-0.8,0.1-1-0.3l-4.7-8.1c-4.1,1.8-9.1,0.3-11.4-3.7c-2.5-4.3-1-9.8,3.3-12.3c4.3-2.5,9.8-1,12.3,3.3C19.2,8.7,17.9,13.9,13.9,16.5z"></path>
    </svg>
    </symbol>
    <symbol id="icon-x">
    <svg viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
    <path fill-rule="evenodd" clip-rule="evenodd" d="M0.244078 0.244078C-0.0813592 0.569515 -0.0813592 1.09715 0.244078 1.42259L8.82149 10L0.244078 18.5774C-0.0813592 18.9028 -0.0813592 19.4305 0.244078 19.7559C0.569515 20.0814 1.09715 20.0814 1.42259 19.7559L10 11.1785L18.5774 19.7559C18.9028 20.0814 19.4305 20.0814 19.7559 19.7559C20.0814 19.4305 20.0814 18.9028 19.7559 18.5774L11.1785 10L19.7559 1.42259C20.0814 1.09715 20.0814 0.569515 19.7559 0.244078C19.4305 -0.0813592 18.9028 -0.0813592 18.5774 0.244078L10 8.82149L1.42259 0.244078C1.09715 -0.0813592 0.569515 -0.0813592 0.244078 0.244078Z"></path>
    </svg>
    </symbol>
    <symbol id="icon-x-sticks">
    <svg viewBox="0 0 23 23" fill="inherit" xmlns="http://www.w3.org/2000/svg">
    <rect x="0.707107" width="4" height="25" rx="2" transform="matrix(-0.707107 -0.707107 -0.707107 0.707107 22.754 4.62109)" fill="inherit" stroke="inherit"></rect>
    <rect x="5.96046e-08" y="-0.707107" width="4" height="25" rx="2" transform="matrix(-0.707107 0.707107 0.707107 0.707107 4.62109 1.79289)" fill="inherit" stroke="inherit"></rect>
    </svg>
    </symbol>
    <symbol id="icon-arrow--down">
    <svg viewBox="0 0 12 7" xmlns="http://www.w3.org/2000/svg">
    <g stroke="none" stroke-width="1" fill-rule="evenodd" transform="translate(-281.000000, -18.000000)">
    <path d="M281.198189,23.7332588 C281.316132,23.5566702 281.448049,23.389995 281.592649,23.2348646 C281.959618,22.7641947 282.493449,22.3399471 282.859778,21.8686325 C283.510921,21.1215569 284.213117,20.4213631 284.961219,19.7731847 C285.408743,19.3373315 285.823661,18.8524771 286.298674,18.4501511 C286.449913,18.2970765 286.614928,18.1585038 286.791589,18.0362196 C287.127232,17.8543992 287.571558,18.4063079 287.822171,18.6016681 C288.541405,19.1619586 288.972945,20.0078748 289.694097,20.5617178 C290.371135,21.0833232 290.890262,21.7558007 291.552596,22.2967487 C292.023468,22.7103118 292.463447,23.1583414 292.868954,23.6371906 C293.098469,23.8899338 293.010883,23.9415141 292.727664,24.1155974 C292.466822,24.276141 292.147802,24.8480371 291.861387,24.9595794 C291.617168,25.0543581 291.578809,24.9531318 291.343539,24.8203127 C291.035388,24.6462293 290.954833,24.4592509 290.731072,24.1916783 C290.435067,23.837064 290.114768,23.5011476 289.81301,23.1510467 C289.438745,22.7155408 289.042481,22.2997687 288.625795,21.9053835 C288.299742,21.5971915 287.958346,21.300605 287.652752,20.9711361 C287.51338,20.8209086 287.333092,20.4153589 287.132346,20.3560416 C286.9316,20.2967243 286.597876,20.702274 286.451472,20.8157506 C286.123501,21.0691386 285.773154,21.3012497 285.459888,21.5739803 C285.19521,21.804802 284.972727,22.0865591 284.724032,22.3347891 C284.374522,22.7775383 283.992405,23.1930778 283.58093,23.5778733 C283.276299,23.7599508 283.007558,23.9970987 282.788175,24.2774305 C282.660311,24.4553824 282.521579,24.6204392 282.386043,24.793233 C282.336816,24.8577084 282.272884,24.9827905 282.194248,24.9989093 C282.002452,25.0375945 281.0748,24.035648 281,23.8325508" id="Shape" fill-rule="nonzero" transform="translate(287.000000, 21.500000) scale(1, -1) translate(-287.000000, -21.500000) "></path>
    </g>
    </svg>
    </symbol>
    <symbol id="relish-heart-filled">
    <svg viewBox="0 0 20 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M17.2954 2.21782C15.5027 0.594059 12.6187 0.594059 10.8455 2.21782L9.81269 3.16353L8.77992 2.21782C6.98717 0.594059 4.1032 0.594059 2.32994 2.21782C0.556686 3.84159 0.556686 6.50028 2.32994 8.12405L9.59834 14.7797C9.71526 14.8868 9.91013 14.8868 10.027 14.7797L17.2954 8.14189C19.0882 6.50028 19.0882 3.85943 17.2954 2.21782Z" fill="#E4965F" stroke="#385E83" stroke-width="1.5"></path>
    </svg>
    </symbol>
    <symbol id="icon-navigation-arrow">
    <svg viewBox="0 0 15 15" fill="inherit" xmlns="http://www.w3.org/2000/svg">
    <circle cx="7.5" cy="7.5" r="7.5" transform="rotate(90 7.5 7.5)" fill="inherit" stroke="none"></circle>
    <path d="M12 7.5L7.5 12L3 7.5" stroke="inherit"></path>
    <path d="M7.5 11.9095L7.5 3" stroke="inherit"></path>
    </svg>
    </symbol>
    <symbol id="icon-save-recipe">
    <svg viewBox="0 0 29 22" fill="inherit" xmlns="http://www.w3.org/2000/svg">
    <rect x="28.166" y="0.5" width="19.6667" height="25" transform="rotate(90 28.166 0.5)" fill="#F3EDCF" stroke="#1B4D7A" stroke-linejoin="round"></rect>
    <rect x="25.5" y="3.1665" width="18.3333" height="25" transform="rotate(90 25.5 3.1665)" fill="inherit" stroke="#1B4D7A" stroke-linejoin="round"></rect>
    <rect x="3.66602" y="6.3335" width="9.33333" height="1.33333" rx="0.666667" fill="#FFD12F"></rect>
    <rect x="3.66602" y="9" width="18.6667" height="1.33333" rx="0.666667" fill="#FFD12F"></rect>
    <rect x="3.66602" y="11.6665" width="18.6667" height="1.33333" rx="0.666667" fill="#FFD12F"></rect>
    <rect x="3.66602" y="14.3335" width="18.6667" height="1.33333" rx="0.666667" fill="#FFD12F"></rect>
    <rect x="3.66602" y="17" width="18.6667" height="1.33333" rx="0.666667" fill="#FFD12F"></rect>
    <path d="M18.334 3.1665H17.834V3.6665V12.997C17.834 13.468 18.425 13.6789 18.723 13.3139C18.7231 13.3139 18.7231 13.3139 18.7231 13.3138L20.334 11.3412L21.9448 13.3138C21.9449 13.3138 21.9449 13.3139 21.9449 13.3139C22.243 13.6789 22.834 13.4679 22.834 12.997V3.6665V3.1665H22.334H18.334ZM22.7196 12.6815L22.7195 12.6814L22.7196 12.6815Z" fill="#EF6461" stroke="#1B4D7A"></path>
    </svg>
    </symbol>
    <symbol id="icon-facebook">
    <svg viewBox="0 0 35 35" xmlns="http://www.w3.org/2000/svg">
    <g>
    <circle cx="17.5" cy="17.5" r="17.5" fill="#FBF9EE"></circle>
    <circle cx="17.5" cy="17.5" r="17" stroke="#1B4D7A"></circle>
    </g>
    <path fill-rule="evenodd" clip-rule="evenodd" d="M19.25 10.4453H21.875V7H18.9749C16.5618 7 14.875 8.96024 14.875 11.8552V14H11.375V17.4983H14.875V28H18.3749V17.4983H21.875V14H18.3749V11.1836C18.3749 10.6094 18.9492 10.4453 19.25 10.4453Z" fill="#1B4D7A"></path>
    </svg>
    </symbol>
    <symbol id="icon-pinterest">
    <svg viewBox="0 0 35 35" xmlns="http://www.w3.org/2000/svg">
    <g>
    <circle cx="17.5" cy="17.5" r="17.5" fill="#FBF9EE"></circle>
    <circle cx="17.5" cy="17.5" r="17" stroke="#1B4D7A"></circle>
    </g>
    <path fill-rule="evenodd" clip-rule="evenodd" d="M17.8936 7.875C11.8151 7.875 8.75 11.9838 8.75 15.4102C8.75 17.4849 9.58319 19.3305 11.37 20.0184C11.6631 20.1314 11.9256 20.0224 12.0105 19.7166C12.0696 19.5049 12.2096 18.9709 12.272 18.7486C12.3576 18.4461 12.3244 18.3401 12.0878 18.0764C11.5726 17.5033 11.2434 16.7615 11.2434 15.7107C11.2434 12.6625 13.6624 9.93355 17.5425 9.93355C20.9783 9.93355 22.866 11.9129 22.866 14.5561C22.866 18.0342 21.2333 20.9694 18.8098 20.9694C17.4713 20.9694 16.4695 19.9257 16.7907 18.6459C17.175 17.1178 17.9201 15.4688 17.9201 14.3658C17.9201 13.3784 17.3578 12.5548 16.1943 12.5548C14.8261 12.5548 13.7272 13.8894 13.7272 15.6769C13.7272 16.8157 14.1351 17.5856 14.1351 17.5856C14.1351 17.5856 12.735 23.1791 12.4896 24.1587C12.0006 26.1092 12.416 28.5008 12.451 28.7423C12.4719 28.8856 12.667 28.9195 12.7553 28.8112C12.8814 28.656 14.5111 26.7592 15.0651 24.8642C15.2217 24.3275 15.965 21.5485 15.965 21.5485C16.4095 22.3479 17.7087 23.0519 19.0903 23.0519C23.2032 23.0519 25.9937 19.517 25.9937 14.7852C25.9937 11.2071 22.7793 7.875 17.8936 7.875Z" fill="#1B4D7A"></path>
    </svg>
    </symbol>
    <symbol id="icon-instagram">
    <svg viewBox="0 0 35 35" xmlns="http://www.w3.org/2000/svg">
    <g>
    <circle cx="17.5" cy="17.5" r="17.5" fill="#FBF9EE"></circle>
    <circle cx="17.5" cy="17.5" r="17" stroke="#1B4D7A"></circle>
    </g>
    <path d="M17.498 9.8485C19.9911 9.8485 20.2837 9.85962 21.2691 9.90407C22.1804 9.94482 22.6731 10.0967 23.0028 10.2264C23.4399 10.3968 23.7511 10.5968 24.0771 10.9228C24.4031 11.2488 24.6068 11.56 24.7735 11.9971C24.8995 12.3268 25.055 12.8195 25.0958 13.7308C25.1402 14.7161 25.1514 15.0088 25.1514 17.5019C25.1514 19.995 25.1402 20.2876 25.0958 21.273C25.055 22.1843 24.9032 22.677 24.7735 23.0067C24.6031 23.4438 24.4031 23.755 24.0771 24.081C23.7511 24.4069 23.4399 24.6107 23.0028 24.7774C22.6731 24.9033 22.1804 25.0589 21.2691 25.0997C20.2837 25.1441 19.9911 25.1552 17.498 25.1552C15.0049 25.1552 14.7122 25.1441 13.7269 25.0997C12.8156 25.0589 12.3229 24.907 11.9932 24.7774C11.5561 24.607 11.2449 24.4069 10.9189 24.081C10.5929 23.755 10.3892 23.4438 10.2225 23.0067C10.0965 22.677 9.94093 22.1843 9.90018 21.273C9.85573 20.2876 9.84461 19.995 9.84461 17.5019C9.84461 15.0088 9.85573 14.7161 9.90018 13.7308C9.94093 12.8195 10.0928 12.3268 10.2225 11.9971C10.3929 11.56 10.5929 11.2488 10.9189 10.9228C11.2449 10.5968 11.5561 10.3931 11.9932 10.2264C12.3229 10.1004 12.8156 9.94482 13.7269 9.90407C14.7122 9.85591 15.0049 9.8485 17.498 9.8485ZM17.498 8.16669C14.9641 8.16669 14.6456 8.1778 13.6491 8.22225C12.6563 8.26671 11.9784 8.426 11.3857 8.65567C10.7707 8.89276 10.2521 9.21504 9.73348 9.73366C9.21486 10.2523 8.89628 10.7746 8.65549 11.3858C8.42581 11.9786 8.26652 12.6565 8.22207 13.653C8.17762 14.6457 8.1665 14.9643 8.1665 17.4982C8.1665 20.032 8.17762 20.3506 8.22207 21.3471C8.26652 22.3399 8.42581 23.0178 8.65549 23.6142C8.89257 24.2291 9.21486 24.7478 9.73348 25.2664C10.2521 25.785 10.7744 26.1036 11.3857 26.3444C11.9784 26.574 12.6563 26.7333 13.6528 26.7778C14.6493 26.8222 14.9641 26.8334 17.5017 26.8334C20.0392 26.8334 20.3541 26.8222 21.3506 26.7778C22.3434 26.7333 23.0213 26.574 23.6177 26.3444C24.2327 26.1073 24.7513 25.785 25.2699 25.2664C25.7885 24.7478 26.1071 24.2254 26.3479 23.6142C26.5776 23.0215 26.7369 22.3436 26.7813 21.3471C26.8258 20.3506 26.8369 20.0357 26.8369 17.4982C26.8369 14.9606 26.8258 14.6457 26.7813 13.6493C26.7369 12.6565 26.5776 11.9786 26.3479 11.3821C26.1108 10.7672 25.7885 10.2486 25.2699 9.72996C24.7513 9.21134 24.229 8.89276 23.6177 8.65197C23.025 8.42229 22.3471 8.263 21.3506 8.21855C20.3504 8.1778 20.0318 8.16669 17.498 8.16669Z" fill="#1B4D7A"></path>
    <path d="M17.498 12.7046C14.853 12.7046 12.7044 14.8495 12.7044 17.4982C12.7044 20.1468 14.853 22.2917 17.498 22.2917C20.143 22.2917 22.2915 20.1431 22.2915 17.4982C22.2915 14.8532 20.143 12.7046 17.498 12.7046ZM17.498 20.6099C15.7791 20.6099 14.3863 19.217 14.3863 17.4982C14.3863 15.7793 15.7791 14.3864 17.498 14.3864C19.2168 14.3864 20.6097 15.7793 20.6097 17.4982C20.6097 19.217 19.2168 20.6099 17.498 20.6099Z" fill="#1B4D7A"></path>
    <path d="M22.4805 13.6344C23.0983 13.6344 23.5992 13.1336 23.5992 12.5157C23.5992 11.8978 23.0983 11.397 22.4805 11.397C21.8626 11.397 21.3617 11.8978 21.3617 12.5157C21.3617 13.1336 21.8626 13.6344 22.4805 13.6344Z" fill="#1B4D7A"></path>
    </svg>
    </symbol>
    <symbol id="icon-youtube">
    <svg width="100%" viewBox="0 0 35 35" xmlns="http://www.w3.org/2000/svg">
    <g>
    <circle cx="17.5" cy="17.5" r="17.5" fill="#FBF9EE"></circle>
    <circle cx="17.5" cy="17.5" r="17" stroke="#1B4D7A"></circle>
    </g>
    <path fill-rule="evenodd" clip-rule="evenodd" d="M26.3877 16.3169C26.3815 15.9699 26.354 15.5313 26.306 15.0014C26.2577 14.4713 26.1887 13.9974 26.0988 13.5799C25.996 13.111 25.7726 12.7158 25.429 12.3946C25.0853 12.0733 24.6854 11.887 24.229 11.8356C22.8026 11.675 20.6473 11.5947 17.7627 11.5947C14.8781 11.5947 12.7226 11.675 11.2963 11.8356C10.8401 11.887 10.4417 12.0733 10.1013 12.3946C9.76075 12.7157 9.53909 13.1109 9.4363 13.5799C9.34001 13.9976 9.26762 14.4714 9.21948 15.0014C9.17134 15.5313 9.14402 15.9698 9.13763 16.3169C9.13112 16.6638 9.12793 17.1456 9.12793 17.7624C9.12793 18.3792 9.13112 18.861 9.13763 19.208C9.14402 19.555 9.17134 19.9935 9.21948 20.5234C9.26762 21.0536 9.3367 21.5274 9.42672 21.9449C9.5295 22.4139 9.75271 22.8091 10.0964 23.1303C10.4402 23.4514 10.8402 23.6378 11.2963 23.6892C12.7226 23.8498 14.878 23.9301 17.7627 23.9301C20.6475 23.9301 22.8026 23.8498 24.229 23.6892C24.6853 23.6378 25.0836 23.4514 25.4242 23.1303C25.7647 22.8091 25.9863 22.4138 26.0891 21.9449C26.1853 21.5274 26.2577 21.0534 26.306 20.5234C26.354 19.9935 26.3813 19.5549 26.3877 19.208C26.3942 18.861 26.3974 18.3792 26.3974 17.7624C26.3974 17.1456 26.3942 16.6638 26.3877 16.3169ZM21.1742 18.2829L16.2401 21.3665C16.1501 21.4308 16.0408 21.4628 15.9123 21.4628C15.8161 21.4628 15.7165 21.4372 15.6137 21.3858C15.4017 21.2701 15.2957 21.0904 15.2957 20.8462V14.6786C15.2957 14.4346 15.4017 14.2547 15.6137 14.139C15.8321 14.0232 16.0409 14.0297 16.2401 14.1583L21.1742 17.2421C21.3669 17.3511 21.4632 17.5247 21.4632 17.7624C21.4632 18.0002 21.3669 18.1737 21.1742 18.2829Z" fill="#1B4D7A"></path>
    </svg>
    </symbol>
    <symbol id="icon-twitter">
    <svg viewBox="0 0 35 35" xmlns="http://www.w3.org/2000/svg">
    <g>
    <circle cx="17.5" cy="17.5" r="17.5" fill="#FBF9EE"></circle>
    <circle cx="17.5" cy="17.5" r="17" stroke="#1B4D7A"></circle>
    </g>
    <path d="M18.8038 16.2876L24.5713 9.58333H23.2046L18.1966 15.4046L14.1967 9.58333H9.58337L15.6319 18.3861L9.58337 25.4167H10.9502L16.2388 19.2692L20.4629 25.4167H25.0763L18.8038 16.2876ZM16.9317 18.4637L16.3189 17.5872L11.4427 10.6122H13.542L17.4772 16.2412L18.09 17.1178L23.2053 24.4345H21.1059L16.9317 18.4637Z" fill="#1B4D7A"></path>
    </svg>
    </symbol>
    <symbol id="icon-website">
    <svg viewBox="0 0 35 35" xmlns="http://www.w3.org/2000/svg">
    <g>
    <circle cx="17.5" cy="17.5" r="17.5" fill="#FBF9EE"></circle>
    <circle cx="17.5" cy="17.5" r="17" stroke="#1B4D7A"></circle>
    </g>
    <path fill-rule="evenodd" clip-rule="evenodd" d="M17.5 9C17.2815 9 17.065 9.00824 16.8507 9.02443C12.6739 9.33993 9.33993 12.6739 9.02443 16.8507C9.00824 17.065 9 17.2815 9 17.5C9 17.7185 9.00824 17.935 9.02443 18.1493C9.33993 22.3261 12.6739 25.6601 16.8507 25.9756C17.065 25.9918 17.2815 26 17.5 26C17.7185 26 17.935 25.9918 18.1493 25.9756C22.3261 25.6601 25.6601 22.3261 25.9756 18.1493C25.9918 17.935 26 17.7185 26 17.5C26 17.2815 25.9918 17.065 25.9756 16.8507C25.6601 12.6739 22.3261 9.33993 18.1493 9.02443C17.935 9.00824 17.7185 9 17.5 9ZM16.8507 10.3275C16.3862 10.369 15.9345 10.4546 15.4996 10.5801C15.472 10.645 15.4397 10.7234 15.4037 10.8149C15.2785 11.133 15.1089 11.6093 14.9384 12.2274C14.8885 12.4082 14.8386 12.6012 14.7896 12.8059C15.4419 12.8727 16.1415 12.9246 16.8507 12.9452V10.3275ZM18.1493 12.9452V10.3275C18.6138 10.369 19.0655 10.4546 19.5004 10.5801C19.528 10.645 19.5603 10.7234 19.5963 10.8149C19.7215 11.133 19.8911 11.6093 20.0616 12.2274C20.1115 12.4082 20.1614 12.6012 20.2104 12.8059C19.5581 12.8727 18.8585 12.9246 18.1493 12.9452ZM16.8507 14.2443C16.0457 14.2221 15.2561 14.1615 14.5312 14.0846C14.3962 14.8943 14.2941 15.8211 14.2632 16.8507H16.8507V14.2443ZM18.1493 16.8507V14.2443C18.9543 14.2221 19.7439 14.1615 20.4688 14.0846C20.6038 14.8943 20.7059 15.8211 20.7368 16.8507H18.1493ZM16.8507 18.1493H14.2632C14.2943 19.186 14.3976 20.1185 14.534 20.9322C15.2795 20.8497 16.0767 20.783 16.8507 20.7576V18.1493ZM18.1493 20.7576V18.1493H20.7368C20.7057 19.186 20.6024 20.1185 20.466 20.9322C19.7205 20.8497 18.9233 20.783 18.1493 20.7576ZM16.8507 22.0569C16.1706 22.0806 15.4657 22.1379 14.7935 22.2102C14.8412 22.4089 14.8898 22.5965 14.9384 22.7726C15.1089 23.3907 15.2785 23.867 15.4037 24.1851C15.4397 24.2766 15.472 24.355 15.4996 24.4199C15.9345 24.5454 16.3862 24.631 16.8507 24.6725V22.0569ZM18.1493 24.6725V22.0569C18.8294 22.0806 19.5343 22.1379 20.2065 22.2102C20.1588 22.4089 20.1102 22.5965 20.0616 22.7726C19.8911 23.3907 19.7215 23.867 19.5963 24.1851C19.5603 24.2766 19.528 24.355 19.5004 24.4199C19.0655 24.5454 18.6138 24.631 18.1493 24.6725ZM21.1366 23.717C21.6891 23.3932 22.1945 22.9978 22.6402 22.5436C22.3528 22.4964 22.0208 22.4446 21.6582 22.3928C21.607 22.3855 21.5552 22.3782 21.5029 22.3709C21.4411 22.6372 21.3773 22.8864 21.3134 23.118C21.2541 23.3331 21.1946 23.533 21.1366 23.717ZM21.7554 21.095C21.7844 21.099 21.8132 21.1031 21.8418 21.1072C22.47 21.197 23.0084 21.2867 23.3902 21.3541C23.4525 21.3651 23.5107 21.3755 23.5645 21.3852C24.174 20.4358 24.5667 19.3338 24.6725 18.1493H22.036C22.005 19.2423 21.8983 20.2292 21.7554 21.095ZM22.036 16.8507H24.6725C24.5669 15.6689 24.1758 14.5692 23.5686 13.6212C23.5308 13.6286 23.4908 13.6363 23.4486 13.6443C23.0927 13.7121 22.5858 13.8022 21.978 13.8923C21.9063 13.9029 21.8331 13.9135 21.7586 13.9241C21.8998 14.7853 22.0052 15.7657 22.036 16.8507ZM21.5073 12.6481C21.6029 12.6347 21.6964 12.6212 21.7877 12.6077C22.1078 12.5603 22.3988 12.5129 22.6526 12.469C22.2039 12.0095 21.6942 11.6098 21.1366 11.283C21.1946 11.467 21.2541 11.6669 21.3134 11.882C21.3788 12.1191 21.4441 12.3747 21.5073 12.6481ZM13.8634 23.717C13.8054 23.533 13.7459 23.3331 13.6866 23.118C13.6227 22.8864 13.5589 22.6372 13.4971 22.3709C13.4448 22.3782 13.393 22.3855 13.3418 22.3928C12.9792 22.4446 12.6472 22.4964 12.3598 22.5436C12.8055 22.9978 13.3109 23.3932 13.8634 23.717ZM11.4355 21.3852C11.4893 21.3755 11.5475 21.3651 11.6098 21.3541C11.9916 21.2867 12.53 21.197 13.1582 21.1072C13.1868 21.1031 13.2156 21.099 13.2446 21.095C13.1017 20.2292 12.995 19.2423 12.964 18.1493H10.3275C10.4333 19.3338 10.826 20.4358 11.4355 21.3852ZM10.3275 16.8507H12.964C12.9948 15.7657 13.1002 14.7854 13.2414 13.9241C13.1669 13.9135 13.0937 13.9029 13.022 13.8923C12.4142 13.8022 11.9073 13.7121 11.5514 13.6443C11.5092 13.6363 11.4692 13.6286 11.4314 13.6212C10.8242 14.5692 10.4331 15.6689 10.3275 16.8507ZM12.3474 12.469C12.6012 12.5129 12.8922 12.5603 13.2123 12.6077C13.3036 12.6212 13.3971 12.6347 13.4927 12.6481C13.5559 12.3747 13.6212 12.1191 13.6866 11.882C13.7459 11.6669 13.8054 11.467 13.8634 11.283C13.3058 11.6098 12.7961 12.0095 12.3474 12.469Z" fill="#1B4D7A"></path>
    </svg> </symbol>
    <symbol id="icon-linkedin">
    <svg viewBox="0 0 35 35" xmlns="http://www.w3.org/2000/svg">
    <g>
    <circle cx="17.5" cy="17.5" r="17.5" fill="#FBF9EE"></circle>
    <circle cx="17.5" cy="17.5" r="17" stroke="#1B4D7A"></circle>
    </g>
    <path d="M22.4583 9H12.5417C10.586 9 9 10.586 9 12.5417V22.4583C9 24.414 10.586 26 12.5417 26H22.4583C24.4148 26 26 24.414 26 22.4583V12.5417C26 10.586 24.4148 9 22.4583 9ZM14.6667 22.4583H12.5417V14.6667H14.6667V22.4583ZM13.6042 13.7685C12.9199 13.7685 12.3646 13.2089 12.3646 12.519C12.3646 11.8291 12.9199 11.2695 13.6042 11.2695C14.2884 11.2695 14.8438 11.8291 14.8438 12.519C14.8438 13.2089 14.2891 13.7685 13.6042 13.7685ZM23.1667 22.4583H21.0417V18.4888C21.0417 16.1032 18.2083 16.2838 18.2083 18.4888V22.4583H16.0833V14.6667H18.2083V15.9169C19.1972 14.0851 23.1667 13.9498 23.1667 17.6707V22.4583Z" fill="#1B4D7A"></path>
    </svg> </symbol>
    <symbol id="icon-reddit">
    <svg viewBox="0 0 35 35" xmlns="http://www.w3.org/2000/svg">
    <g>
    <circle cx="17.5" cy="17.5" r="17.5" fill="#FBF9EE"></circle>
    <circle cx="17.5" cy="17.5" r="17" stroke="#1B4D7A"></circle>
    </g>
    <path d="M28 18.1281C28 16.9082 26.9889 15.8408 25.6667 15.8408C25.0444 15.8408 24.5 16.0695 24.1111 16.4507C22.5556 15.3071 20.3778 14.6209 17.9667 14.5446L18.9778 9.74118L22.4 10.4274C22.4778 11.2661 23.1778 11.9523 24.0333 11.9523C24.9667 11.9523 25.6667 11.2661 25.6667 10.3511C25.6667 9.4362 24.9667 8.75 24.0333 8.75C23.4111 8.75 22.8667 9.13122 22.5556 9.66494L18.7444 8.90249C18.6667 8.90249 18.5111 8.90249 18.4333 8.97873C18.3556 8.97873 18.3556 9.05498 18.2778 9.20747L17.1111 14.5446C14.7 14.6209 12.4444 15.3071 10.8889 16.4507C10.5 16.0695 9.95556 15.8408 9.33333 15.8408C8.08889 15.8408 7 16.8319 7 18.1281C7 19.043 7.54444 19.8055 8.32222 20.1867C8.32222 20.4155 8.24444 20.6442 8.24444 20.8729C8.24444 24.3039 12.3667 27.125 17.4222 27.125C22.4778 27.125 26.6 24.3039 26.6 20.8729C26.6 20.6442 26.6 20.4155 26.5222 20.1867C27.4556 19.8055 28 19.043 28 18.1281Z" fill="#1B4D7A"></path>
    </svg>
    </symbol>
    <symbol id="icon-hand">
    <svg viewBox="0 0 16 20" xmlns="http://www.w3.org/2000/svg">
    <path d="M3.83820391,11.4067971 L3.83820391,10.7784427 C3.83820391,8.55047824 3.83464435,6.32259153 3.8399028,4.09462708 C3.84232977,3.06312462 4.41274973,2.3412517 5.34567882,2.16066687 C6.36144865,1.96406805 7.35319142,2.60649297 7.52607292,3.58987574 C7.57501691,3.86856564 7.58003266,4.15611765 7.58472481,4.43993825 C7.59354282,4.96871055 7.58723269,5.4977938 7.58723269,6.05719479 C8.57857097,5.84364914 9.3434725,6.11269955 9.86632376,6.91472011 C11.1731688,6.59638395 11.8967308,6.8782611 12.4861621,7.93945938 C12.5533893,7.93634987 12.632266,7.9409364 12.7078258,7.92779872 C13.9961449,7.70399168 15.0804363,8.40044435 15.1591512,9.65622027 C15.3294439,12.373544 15.318927,15.0911009 14.6387269,17.7622484 C14.5685873,18.0375955 14.4750679,18.3100664 14.3633461,18.5724314 C14.2219344,18.9045271 13.9281896,19.0276638 13.6163233,18.9166542 C13.2767087,18.7958497 13.0996204,18.5006017 13.2155489,18.1739476 C14.1550308,15.5275207 14.0056101,12.7888969 13.9654841,10.0601457 C13.9626526,9.87147615 13.9105536,9.67425543 13.8372589,9.49794617 C13.7231911,9.22337637 13.5083229,9.04574557 13.1770409,9.10194997 C12.8418757,9.15885402 12.6588818,9.38071761 12.6388997,9.696022 C12.6159243,10.0574249 12.6342075,10.4210822 12.6288682,10.7836511 C12.6224772,11.2173501 12.4050202,11.4593478 12.0302953,11.4588814 C11.6602626,11.458415 11.4215292,11.2054563 11.4177269,10.7890928 C11.4118213,10.1284772 11.4190213,9.46786165 11.4149763,8.80724609 C11.4117404,8.26976716 11.1866789,7.99737402 10.7638999,8.00491458 C10.3489681,8.01222193 10.1262528,8.29293301 10.1229359,8.82745791 C10.1188909,9.48807347 10.1259292,10.148689 10.1202662,10.8093046 C10.1167876,11.2193713 9.90588345,11.445666 9.53835857,11.4553832 C9.16242019,11.4652559 8.91414067,11.2196045 8.91074291,10.8016085 C8.90313839,9.86898854 8.91025751,8.93636853 8.90734514,8.00374852 C8.90572716,7.50412788 8.67775333,7.2246606 8.27843509,7.21867479 C7.86795276,7.21261124 7.61708447,7.51135749 7.615062,8.02287201 C7.61166423,8.87775425 7.61603279,9.73271423 7.61328221,10.5875965 C7.61174513,11.0708922 7.41993323,11.2870809 7.00726662,11.2841269 C6.61450121,11.2813283 6.40214092,11.0400303 6.40157463,10.5713493 C6.39882406,8.47297369 6.40044204,6.37452036 6.39995665,4.27614477 C6.39995665,4.14671138 6.40675217,4.01580098 6.38960155,3.88815556 C6.34753399,3.57386176 6.06924089,3.33201957 5.75365327,3.35293102 C5.42115773,3.37493081 5.20086933,3.54774187 5.14723319,3.87494014 C5.1264421,4.00173044 5.13412752,4.13326275 5.13404662,4.26261839 C5.13348033,7.28070953 5.13477472,10.2988007 5.13202414,13.3168141 C5.13170055,13.6760403 5.18363781,14.0754569 4.70261141,14.2178725 C4.32861461,14.3285711 4.09141828,14.1711521 3.77631606,13.6474328 C3.2439996,12.7626215 2.71467641,11.8759445 2.17297565,10.9964194 C2.08770794,10.8581239 1.96546931,10.7154751 1.82292499,10.6403804 C1.51987675,10.4806293 1.17937219,10.6755956 1.1690171,11.0063698 C1.16230247,11.2227918 1.20040597,11.4359487 1.27677478,11.6351129 C2.05494378,13.6656234 2.9268749,15.6543888 4.23007943,17.4380041 C4.41517671,17.6912738 4.63417073,17.9225436 4.84717821,18.1558347 C5.14796128,18.4852096 5.17328272,18.8321533 4.90259413,19.0855784 C4.64298874,19.3286644 4.20378721,19.3131946 3.94863126,18.9668728 C3.25831875,18.0295885 2.52148922,17.1086292 1.96749179,16.0978827 C1.27992985,14.8435061 0.72140206,13.5190877 0.183908055,12.1960686 C0.00180405122,11.7477549 -0.0240027801,11.2059227 0.0166894963,10.7174963 C0.0736425034,10.0337149 0.58977913,9.58081463 1.29157932,9.43303513 C1.95430523,9.29349584 2.66540882,9.57863797 3.07297878,10.1726323 C3.34698423,10.5720489 3.57795132,10.9987515 3.83820391,11.4067971"></path>
    <path d="M9.63946634,3.76392283 C9.63946634,1.68187217 7.88816137,1.55475529e-05 5.72783026,1.55475529e-05 C3.56749915,1.55475529e-05 1.81627508,1.6788404 1.81627508,3.76089106 C1.81627508,4.13302176 1.87298539,4.5392016 1.97726441,4.82706456 C2.02734098,5.08087837 2.25846988,5.27289066 2.53643939,5.27289066 C2.77258403,5.27289066 2.97442743,5.13412875 3.06066593,4.93675255 C3.09901213,4.82247803 3.05484119,4.56089044 3.02126804,4.39989552 C3.01099384,4.35815034 3.00096235,4.31617194 2.99262974,4.27310522 C2.99141625,4.26813 2.99068816,4.26502049 2.99068816,4.26502049 L2.99117355,4.26408764 C2.96035097,4.10146023 2.94320035,3.9316032 2.94320035,3.76392283 C2.94320035,2.2816968 4.18993727,1.08616765 5.72783026,1.08616765 C7.26572325,1.08616765 8.51246017,2.27866502 8.51246017,3.76089106 C8.51246017,3.89514419 8.50089159,4.03095207 8.4809095,4.16372818 L8.48188029,4.16427234 C8.48188029,4.16427234 8.48058591,4.16955851 8.47872522,4.1769436 C8.46367798,4.27372712 8.44474758,4.36903363 8.41999244,4.4597536 C8.41877895,4.46216347 8.41813176,4.46472882 8.41699917,4.46713869 C8.39167773,4.60846595 8.37193834,4.76479661 8.38698558,4.85481694 C8.45704425,5.07590316 8.67029443,5.2367426 8.9226189,5.2367426 C9.01225517,5.2367426 9.09655209,5.21567567 9.17162651,5.17960534 L9.17146471,5.18053819 C9.17146471,5.18053819 9.20673675,5.16561254 9.25479085,5.13117471 C9.25818861,5.12876484 9.26134368,5.12619949 9.26466054,5.12371189 C9.28342915,5.10971909 9.30365394,5.09284999 9.32444502,5.07287138 C9.32452592,5.07287138 9.32460682,5.07279365 9.32460682,5.07271591 C9.39094413,5.00904868 9.4611646,4.91413086 9.49570854,4.77917809 C9.49627483,4.77707917 9.49635573,4.77536894 9.49692202,4.77327002 C9.58930886,4.49030455 9.63946634,4.11343184 9.63946634,3.76392283"></path>
    </svg> </symbol>
    <symbol id="icon-circle-arrow">
    <svg viewBox="0 0 16 16" fill="inherit" xmlns="http://www.w3.org/2000/svg">
    <circle cx="8" cy="8" r="8" fill="inherit" stroke="none"></circle>
    <path d="M10.666 11.4075L4.74009 11.4075L4.74009 5.48157" stroke="inherit"></path>
    <path d="M4.73958 11.4076L11.4062 4.74097" stroke="inherit"></path>
    </svg>
    </symbol>
    <symbol id="newsletter-success-image">
    <svg class="newsletter-signup__success-image" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 413.769 76.554">
    <path d="M372.861,53.9c3.99,1.519,8.193,2.529,12.469,3.72,1.016.425,4.466,2.165,5.293,1.615a9.228,9.228,0,0,0,1.062-2.54c3.587-7.154,7.026-14.668,11.047-21.79-5.943,2.44-11.812,5.059-17.935,7.573-7.023,2.883-13.073,5.577-20.276,8.534a22.041,22.041,0,0,0,3.08.84C369.559,52.519,371.157,53.336,372.861,53.9Z" fill="#a0c3cc"></path><path d="M405.817,60.362c-1.63-.383-3.154-1.02-4.785-1.4a20.625,20.625,0,0,0-2.9-.914,7.2,7.2,0,0,1-2.351-.087,35.8,35.8,0,0,1-2.705,4.688c-1.039,1.058-2.415.781-3.717.684-.688-.138-1.7-.563-2.392-.7a11.17,11.17,0,0,0-1.556-.2c-.54.222-.785,1.164-.891,1.418a23.336,23.336,0,0,1-1.348,2.868L380.5,70.966a40.659,40.659,0,0,0-2.483,5.228l.148.36,1.8-.739,3.062-1.257c5.837-2.186,11.6-4.551,17.108-7.023,2.087-1.067,4.248-1.954,6.409-2.841a27.361,27.361,0,0,0,5.223-2.144,4.548,4.548,0,0,0-1.778-.743C408.537,61.35,407.267,60.819,405.817,60.362Z" fill="#a0c3cc"></path><path d="M381.24,60.982a9.228,9.228,0,0,0-2.54-1.062c-2.032-.849-3.99-1.518-5.948-2.188a37.37,37.37,0,0,1-3.736-1.412,9.748,9.748,0,0,1-1.958-.669c-.18.074-.254-.106-.508-.212l-.18.074.148.36a20.582,20.582,0,0,0,1.932,5.731c.559,1.875,1.372,3.856,2.186,5.837,1.183,2.881,2.694,6.049,3.876,8.93a15.559,15.559,0,0,0,1.38-3.3,17.551,17.551,0,0,1,.882-2.466c.743-1.778,1.74-3.45,2.3-5.154C379.605,64.178,381.069,62.1,381.24,60.982Z" fill="#a0c3cc"></path><path d="M399.41,51.419a9.227,9.227,0,0,0-1.062,2.54c.91.679,3.228,1.2,4.5,1.731,1.6.817,3.482,1.306,5.26,2.049a17.548,17.548,0,0,1,2.36,1.136,15.56,15.56,0,0,0,3.3,1.38c-1.183-2.881-2.333-6.2-3.516-9.078-.813-1.981-1.626-3.962-2.546-5.689a31.449,31.449,0,0,0-2.652-5.435l-.148-.36-.106.254c-.106.254-.032.434-.212.508l-.743,1.778a37.373,37.373,0,0,1-1.666,3.63C401.256,47.714,400.259,49.386,399.41,51.419Z" fill="#a0c3cc"></path><path d="M345.135,5.476c1.214,2.686,2.78,5.227,4.294,7.892.291.7,1.133,3.234,1.8,3.368a6.374,6.374,0,0,0,1.757-.727c5.239-1.756,10.582-3.761,16.018-5.29-4.1-1.7-8.246-3.274-12.468-5.025C351.7,3.686,347.425,2.06,342.458,0a15.208,15.208,0,0,0,1.1,1.911C344.19,3.192,344.575,4.37,345.135,5.476Z" fill="#a0c3cc"></path><path d="M358.123,24.686c-.612-.981-1.048-2.035-1.659-3.016a14.235,14.235,0,0,0-.975-1.859,4.968,4.968,0,0,1-1.108-1.187,24.727,24.727,0,0,1-3.607.978c-1.024.012-1.563-.794-2.153-1.475-.27-.4-.56-1.105-.83-1.508a7.7,7.7,0,0,0-.663-.857c-.372-.154-.951.188-1.127.26a16.115,16.115,0,0,1-2.056.748l-3.38.781A28.081,28.081,0,0,0,336.8,18.9l-.1.248,1.242.515,2.111.875c3.922,1.772,7.9,3.419,11.8,4.891,1.541.494,3.031,1.112,4.521,1.729a18.9,18.9,0,0,0,3.6,1.493,3.139,3.139,0,0,0-.509-1.23C358.974,26.494,358.611,25.615,358.123,24.686Z" fill="#a0c3cc"></path><path d="M338.917,15.557c1.23-.509,2.532-.842,3.637-1.4a21.821,21.821,0,0,0,3.234-1.133,6.37,6.37,0,0,0-.727-1.757c-.581-1.405-1.214-2.686-1.847-3.967a25.815,25.815,0,0,1-1.142-2.511,6.737,6.737,0,0,1-.633-1.281c-.124-.052-.073-.176-.145-.351l-.124-.052-.1.248A14.209,14.209,0,0,0,339.223,7.1c-.639,1.19-1.205,2.556-1.772,3.922-.824,1.987-1.626,4.273-2.45,6.26a10.743,10.743,0,0,0,2.283-.945A12.116,12.116,0,0,1,338.917,15.557Z" fill="#a0c3cc"></path><path d="M366.291,18.323a21.715,21.715,0,0,0,1.348-3.952l.1-.248-.176.073c-.176.073-.227.2-.351.145l-1.23.509a25.813,25.813,0,0,1-2.583.966c-1.354.457-2.656.79-4.061,1.372a6.372,6.372,0,0,0-1.757.727,20,20,0,0,0,1.36,3.038c.385,1.178,1.069,2.335,1.578,3.565a12.114,12.114,0,0,1,.6,1.705,10.747,10.747,0,0,0,.945,2.284c.824-1.987,1.875-4.17,2.7-6.157C365.334,20.982,365.9,19.616,366.291,18.323Z" fill="#a0c3cc"></path><path d="M277.96,35.929c1.935,1.618,4.087,2.994,6.246,4.484.485.433,2.076,2.071,2.644,1.923.338-.134.867-.971,1.083-1.214,3.384-3.3,6.754-6.836,10.374-10.037-3.79.215-7.573.546-11.477.768-4.479.255-8.377.591-12.97.852a13.024,13.024,0,0,0,1.558,1.064C276.383,34.521,277.117,35.285,277.96,35.929Z" fill="#a0c3cc"></path><path d="M297.167,47.51c-.722-.535-1.328-1.077-2.05-1.612-.836-.529-1.565-1.178-2.4-1.707a12.18,12.18,0,0,0-1.443-1.07,4.252,4.252,0,0,1-1.3-.5,21.159,21.159,0,0,1-2.416,2.1c-.784.39-1.5-.03-2.208-.335-.358-.21-.843-.643-1.2-.853a6.6,6.6,0,0,0-.83-.414c-.345.02-.663.5-.771.62a13.782,13.782,0,0,1-1.306,1.342l-2.314,1.86a24.024,24.024,0,0,0-2.4,2.44l.013.23,1.148-.065,1.952-.111c3.681-.094,7.356-.3,10.91-.62,1.372-.193,2.75-.272,4.128-.35a16.166,16.166,0,0,0,3.33-.189A2.687,2.687,0,0,0,297.167,47.51Z" fill="#a0c3cc"></path><path d="M279.2,43.577a18.677,18.677,0,0,0,2.071-2.076c-.134-.338-.971-.866-1.214-1.083-.971-.867-1.935-1.618-2.9-2.37a22.077,22.077,0,0,1-1.814-1.51,5.762,5.762,0,0,1-.964-.752c-.115.007-.121-.108-.243-.217l-.115.007.013.23a12.159,12.159,0,0,0-.027,3.573c-.05,1.155.022,2.418.094,3.681.1,1.837.337,3.9.441,5.735a9.2,9.2,0,0,0,1.408-1.578,10.368,10.368,0,0,1,.968-1.207C277.679,45.161,278.558,44.42,279.2,43.577Z" fill="#a0c3cc"></path><path d="M299.044,37.956a18.576,18.576,0,0,0-.432-3.547l-.013-.23-.108.121c-.108.121-.1.236-.217.243l-.758.85a22.076,22.076,0,0,1-1.631,1.706c-.873.856-1.753,1.6-2.619,2.568-.217.243-.988.863-1.083,1.214a17.116,17.116,0,0,0,2.178,1.835c.735.765,1.693,1.4,2.542,2.16a10.379,10.379,0,0,1,1.1,1.09,9.192,9.192,0,0,0,1.578,1.408c-.1-1.837-.107-3.911-.212-5.748C299.3,40.361,299.224,39.1,299.044,37.956Z" fill="#a0c3cc"></path><path d="M353.084,44.463c1.221,1.369,1.826,2.218,1.833,2.227l1.632-1.156a30.357,30.357,0,0,0-1.973-2.4Z" fill="#a0c3cc"></path><path d="M118.2,75.945a53.741,53.741,0,0,0,6.116.388l.016-2a52.008,52.008,0,0,1-5.888-.372Z" fill="#a0c3cc"></path><path d="M78.241,34.4c.634,1.834,1.3,3.752,2.029,5.714l1.876-.694c-.722-1.948-1.385-3.853-2.015-5.673Z" fill="#a0c3cc"></path><path d="M73.966,23.251a52.1,52.1,0,0,1,2.28,5.466l1.878-.688a53.845,53.845,0,0,0-2.373-5.679Z" fill="#a0c3cc"></path><path d="M96,66.078a38.639,38.639,0,0,0,4.891,3.773l1.089-1.678A36.535,36.535,0,0,1,97.348,64.6Z" fill="#a0c3cc"></path><path d="M106.334,72.778a39.931,39.931,0,0,0,5.825,2.019l.51-1.934a37.982,37.982,0,0,1-5.533-1.917Z" fill="#a0c3cc"></path><path d="M65.97,14.994c1.531,1.165,3.065,2.446,4.561,3.811l1.348-1.478c-1.539-1.4-3.119-2.725-4.7-3.925Z" fill="#a0c3cc"></path><path d="M88.14,56.632a51.186,51.186,0,0,0,3.613,4.977l1.542-1.273a49.19,49.19,0,0,1-3.47-4.781Z" fill="#a0c3cc"></path><path d="M82.51,45.778c.85,1.994,1.695,3.805,2.584,5.536l1.779-.914c-.867-1.688-1.692-3.457-2.523-5.406Z" fill="#a0c3cc"></path><path d="M55.87,8.823a55.305,55.305,0,0,1,5.208,2.819l1.045-1.705a57.175,57.175,0,0,0-5.4-2.921Z" fill="#a0c3cc"></path><path d="M282.134,20.47l-.152-1.994c-1.993.152-4.027.355-6.048.6l.244,1.985C278.167,20.819,280.172,20.619,282.134,20.47Z" fill="#a0c3cc"></path><path d="M294.089,20.153l.051-2q-2.963-.076-6.084.006l.053,2Q291.176,20.078,294.089,20.153Z" fill="#a0c3cc"></path><path d="M8.852,4.676,9.37,6.608c1.953-.523,3.909-.986,5.813-1.377l-.4-1.959C12.837,3.67,10.843,4.143,8.852,4.676Z" fill="#a0c3cc"></path><path d="M44.693,4.971A54.826,54.826,0,0,1,50.388,6.59L51.039,4.7a56.73,56.73,0,0,0-5.9-1.678Z" fill="#a0c3cc"></path><path d="M32.934,3.533c2,.051,3.992.2,5.919.429l.24-1.985c-1.99-.241-4.045-.39-6.108-.442Z" fill="#a0c3cc"></path><path d="M20.792,2.242l.271,1.981c2-.272,3.991-.469,5.927-.585l-.119-2C24.883,1.761,22.838,1.962,20.792,2.242Z" fill="#a0c3cc"></path><path d="M252.17,23.97l.547,1.924c1.932-.548,3.883-1.066,5.8-1.539l-.479-1.941C256.1,22.891,254.125,23.415,252.17,23.97Z" fill="#a0c3cc"></path><path d="M229.306,32l.768,1.847c1.854-.771,3.729-1.521,5.576-2.231l-.719-1.866C233.069,30.467,231.176,31.224,229.306,32Z" fill="#a0c3cc"></path><path d="M311.873,22.158c1.953.4,3.9.875,5.8,1.4l.534-1.928c-1.937-.537-3.931-1.018-5.928-1.431Z" fill="#a0c3cc"></path><path d="M300.055,20.472c1.992.163,3.989.386,5.936.662l.281-1.98c-1.985-.282-4.022-.509-6.055-.675Z" fill="#a0c3cc"></path><path d="M240.618,27.653l.666,1.886c1.9-.67,3.815-1.313,5.688-1.912l-.607-1.905C244.472,26.326,242.539,26.975,240.618,27.653Z" fill="#a0c3cc"></path><path d="M263.954,21.067l.406,1.958c1.966-.409,3.947-.78,5.89-1.1l-.328-1.973C267.953,20.277,265.945,20.653,263.954,21.067Z" fill="#a0c3cc"></path><path d="M344.314,36.491c1.606,1.212,3.148,2.485,4.585,3.784l1.342-1.483c-1.48-1.338-3.068-2.649-4.722-3.9Z" fill="#a0c3cc"></path><path d="M334.269,30.118c1.757.929,3.49,1.934,5.151,2.987l1.07-1.689c-1.705-1.081-3.483-2.112-5.286-3.065Z" fill="#a0c3cc"></path><path d="M323.357,25.346c1.881.665,3.746,1.4,5.544,2.186l.8-1.833c-1.842-.8-3.753-1.558-5.679-2.238Z" fill="#a0c3cc"></path><path d="M164.709,64.579l.777,1.842c1.813-.765,3.681-1.586,5.549-2.439l-.831-1.818C168.354,63.008,166.5,63.822,164.709,64.579Z" fill="#a0c3cc"></path><path d="M175.637,59.6l.877,1.8c1.767-.862,3.587-1.776,5.41-2.718l-.917-1.777C179.2,57.832,177.39,58.74,175.637,59.6Z" fill="#a0c3cc"></path><path d="M153.508,68.858l.643,1.895c1.865-.633,3.786-1.327,5.709-2.064l-.716-1.867C157.246,67.548,155.349,68.234,153.508,68.858Z" fill="#a0c3cc"></path><path d="M130.259,74.091l.173,1.992c1.951-.169,3.99-.428,6.059-.771l-.326-1.973C134.146,73.674,132.159,73.926,130.259,74.091Z" fill="#a0c3cc"></path><path d="M142.015,72.172l.453,1.947c1.913-.444,3.893-.963,5.887-1.54l-.557-1.922C145.839,71.226,143.893,71.735,142.015,72.172Z" fill="#a0c3cc"></path><path d="M207.408,42.327l.93,1.771q2.7-1.422,5.351-2.743l-.895-1.789Q210.129,40.9,207.408,42.327Z" fill="#a0c3cc"></path><path d="M196.8,48.161l1,1.734q2.646-1.52,5.242-2.949l-.965-1.752Q199.465,46.632,196.8,48.161Z" fill="#a0c3cc"></path><path d="M186.32,54.084l.955,1.758q2.6-1.414,5.291-2.936l-.986-1.74Q188.906,52.681,186.32,54.084Z" fill="#a0c3cc"></path><path d="M218.239,36.919l.855,1.808c1.8-.854,3.642-1.7,5.461-2.505l-.812-1.827C221.91,35.209,220.058,36.058,218.239,36.919Z" fill="#a0c3cc"></path><path d="M0,7.425.664,9.312q1.425-.5,2.819-.956l-.62-1.9Q1.448,6.916,0,7.425Z" fill="#a0c3cc"></path>
    </svg>
    </symbol>
    <symbol id="icon-save-recipe--hover">
    <svg viewBox="0 0 29 22" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="28.166" y="0.5" width="19.6667" height="25" transform="rotate(90 28.166 0.5)" fill="#F3EDCF" stroke="#1B4D7A" stroke-linejoin="round"></rect>
    <rect x="25.5" y="3.1665" width="18.3333" height="25" transform="rotate(90 25.5 3.1665)" fill="#FFD12F" stroke="#1B4D7A" stroke-linejoin="round"></rect>
    <rect x="3.66602" y="6.3335" width="9.33333" height="1.33333" rx="0.666667" fill="#FFD12F"></rect>
    <rect x="3.66602" y="9" width="18.6667" height="1.33333" rx="0.666667" fill="#FFD12F"></rect>
    <rect x="3.66602" y="11.6665" width="18.6667" height="1.33333" rx="0.666667" fill="#FFD12F"></rect>
    <rect x="3.66602" y="14.3335" width="18.6667" height="1.33333" rx="0.666667" fill="#FFD12F"></rect>
    <rect x="3.66602" y="17" width="18.6667" height="1.33333" rx="0.666667" fill="#FFD12F"></rect>
    <path d="M18.334 3.1665H17.834V3.6665V12.997C17.834 13.468 18.425 13.6789 18.723 13.3139C18.7231 13.3139 18.7231 13.3139 18.7231 13.3138L20.334 11.3412L21.9448 13.3138C21.9449 13.3138 21.9449 13.3139 21.9449 13.3139C22.243 13.6789 22.834 13.4679 22.834 12.997V3.6665V3.1665H22.334H18.334ZM22.7196 12.6815L22.7195 12.6814L22.7196 12.6815Z" fill="#EF6461" stroke="#1B4D7A"></path>
    </svg>
    </symbol>
    <symbol id="iconClose">
    <svg viewBox="0 0 23 23" fill="inherit" xmlns="http://www.w3.org/2000/svg">
    <rect x="0.707107" width="4" height="25" rx="2" transform="matrix(-0.707107 -0.707107 -0.707107 0.707107 22.754 4.62109)" fill="inherit" stroke="inherit"></rect>
    <rect x="5.96046e-08" y="-0.707107" width="4" height="25" rx="2" transform="matrix(-0.707107 0.707107 0.707107 0.707107 4.62109 1.79289)" fill="inherit" stroke="inherit"></rect>
    </svg>
    </symbol>
    <symbol id="thespruceeats-logo-text">
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 179.7 61.5">
    <path d="M1.3 23c-.1.7.2 1.4.7 1.9.7.5 1.5.8 2.3.7 1.1 0 2.1-.7 2.6-1.7l-.1-.1c-.4.6-1.1 1-1.8 1-1 0-1.4-.6-1.4-1.5l.1-8.6h2.6v-.4H3.7V9.7h-.2c-.9 3.5-2 4.5-3.6 4.8v.1c.4.1 1.1 0 1.5.1 0 1.3-.1 7.8-.1 8.3zM8.1 25.2h-.2.2zM8.1 25.2c.1 0 .3-.1.4-.1l-.4.1z" fill="#434343"></path>
    <path d="M9.4 24.1c-.1.7-.5.8-.9 1h5V25c-.2 0-.4 0-.6-.1-.5-.1-.8-.5-.9-1-.1-.7-.1-1.4-.1-2.1v-2c0-.7 0-1.3.1-2 .1-1.6 1.3-2.9 2.8-3.1.6 0 1.1.3 1.4.7.4.6.5 1.4.4 2.1v4.4c0 .7 0 1.4-.1 2.1-.2.7-.4.9-.9 1.1-.2 0-.4.1-.6.1v.1h5.4v-.1c-.2 0-.4 0-.6-.1-.5-.2-.8-.6-.9-1.1-.1-.7-.1-1.4-.1-2.1v-4.3c0-.9-.2-1.8-.7-2.5-.6-.7-1.6-1-2.5-.9-.7 0-1.5.2-2.1.6-.8.5-1.3 1.2-1.6 2.1h-.1v-6c0-1.1.1-2.6.1-3.1 0-.1-.1-.2-.2-.2H7.9v.1c.4 0 .7.1 1 .3.3.2.5.5.5.8.1.6.1 1.1.1 1.7v11.4c.1.7 0 1.5-.1 2.2zM26.4 25.6c2.2 0 4-1.6 4.2-3.7l-.3-.1c-.5 1.7-1.3 3.1-3.5 3.1-.7.1-1.4-.2-1.9-.8-.8-.9-1.2-2.6-1.2-5h7v-.7c0-2-1.5-4.2-4.7-4.2-2.7 0-4.8 2.3-4.8 5.8-.1 1.6.5 3.1 1.5 4.3 1.1.9 2.4 1.4 3.7 1.3zm-1.6-10.7c.4-.3.8-.5 1.3-.5s1 .1 1.4.5c.6.8.9 1.7.9 2.7 0 .8-.3 1.1-1 1.1h-3.6c0-1.9.4-3.3 1-3.8zM8 38.8c-2.6-1.1-4.5-2.2-4.5-4.6-.2-1.6 1-3 2.6-3.2h.4c1.3 0 2.6.6 3.6 1.5 1.2 1.1 1.9 2.5 2.2 4.1h.2l-.2-5.9h-.1L11 31.9h-.1c-1.2-1-2.7-1.5-4.3-1.5-3.3-.2-6.1 2.3-6.3 5.5v.3c0 3.6 2.5 5.3 5.5 6.6 3 1.4 5 2.8 5 4.7 0 2.8-1.2 3.6-3.5 3.6-3.1 0-6.4-3.3-7-7.4H.1l.1 7.5h.2l1.4-1.3H2c1.5 1.2 3.4 1.8 5.3 1.8 3.7 0 6.8-2 6.7-6.5 0-3.4-2.6-5-6-6.4zM27.9 30.5c-2.5.1-4.8 1.7-5.8 4h-.3l.1-3.1c0-.2-.1-.4-.3-.4h-6.8v.2c.8 0 1.6.3 2.2.7.6.3.9.9.9 1.6.1.8.1 2.4.1 3.3v18.4c0 1.3 0 2.5-.2 3.8-.1 1-.8 1.8-1.7 2-.3.2-.7.2-1 .2v.2h9.8v-.2c-.3-.1-.6-.1-1-.2-1-.5-1.4-.7-1.7-2-.2-1.3-.2-2.5-.2-3.8v-5.4h.1c1.3 1.3 3.2 2 5.1 1.9 5.1 0 8.8-3.8 8.9-10.6 0-7.3-3.8-10.6-8.2-10.6zm-1 20.5c-1.4 0-2.7-.6-3.6-1.7-1-1.6-1.5-3.4-1.4-5.2v-6c0-3.9 2.5-6.8 5.1-6.8 2.4 0 4.4 2.1 4.4 9.7s-1.9 10-4.5 10zM50 30.7c-1.2 0-2.3.5-3.2 1.2-1.2 1.2-2.1 2.8-2.6 4.5v-5c0-.2-.1-.3-.3-.3h-6.5v.2c.6 0 1.1.1 1.6.4.5.4.9 1 .9 1.6.1.7.1 2.9.1 3.8v8c0 1.3 0 2.6-.2 3.9-.3 1.3-.6 1.5-1.6 1.9-.4.1-.7.2-1.1.2v.2H47v-.2c-.4 0-.7-.1-1.1-.2-1-.4-1.3-.6-1.6-1.9-.2-1.3-.2-2.6-.2-3.9v-3.3c0-1.2.1-2.5.1-2.9.3-3.7 2.2-6.1 3.1-6.1.5 0 .8.2 1.1 1 .3 1 1.2 1.7 2.2 1.8 1.2 0 2.2-.9 2.3-2.1.1-.7-.1-1.4-.6-2-.6-.5-1.4-.8-2.3-.8zM72.4 49c-.1-1-.1-2-.1-3V31.5c0-.3-.1-.4-.3-.4h-6.6v.2c.6.1 1.2.3 1.8.5.6.4.9 1.1.9 1.8.1 1 .1 2 .1 3v5.5c0 1.4 0 2-.1 3.2-.4 3.2-2.3 5.6-4.3 5.6-1 .1-2-.4-2.6-1.2-.5-1-.8-2.2-.7-3.4V31.4c0-.4-.2-.4-.3-.4h-6.7v.2c.6 0 1.2.2 1.8.4.6.5.9 1.2.9 1.9.1 1 .1 2 .1 3v9.1c0 1.6.4 3.1 1.2 4.4 1.2 1.3 2.9 1.9 4.6 1.8 1.4.1 2.7-.4 3.8-1.2 1-.8 1.8-1.9 2.2-3.1h.1v3.8c0 .1.1.2.2.2.2 0 6-.2 6.8 0v-.2c-.6 0-1.2-.1-1.8-.4-.6-.4-.9-1.1-1-1.9zM85.6 50.8c-1.2.1-2.4-.5-3-1.6-1.2-1.8-1.9-4.8-1.9-9.1 0-3.7.6-6.9 1.8-8 .8-.6 1.7-1 2.7-1 1.9 0 2.4 1 2.8 3.4.2 1.5.7 2.5 2.4 2.5 1.1 0 1.9-.9 1.9-2v-.1c0-.8-.4-1.6-1-2.2-.9-1.1-3.1-2.2-6-2.2-4.3 0-9.2 3.9-9.2 10.4 0 3.5.7 6.3 2.7 8.3 1.7 1.6 4 2.5 6.4 2.5 4 0 6.5-3.1 7.4-6.8l-.6-.2c-.7 2.5-2.2 6.1-6.4 6.1zM105.5 50.4c-1.4.1-2.7-.4-3.6-1.4-1.4-1.6-2.2-4.7-2.2-9.2h12.9c0-.6 0-1.1.1-1.7 0-3.7-2.7-7.7-8.6-7.7-5.1 0-8.9 4.4-8.9 10.8 0 3.4.8 5.9 2.7 8 1.9 1.7 4.3 2.5 6.8 2.4 1.2 0 2.4-.3 3.5-.8 0-.3.1-.7.2-1-.9.4-1.9.6-2.9.6zm-3.8-18.3c.6-.7 1.5-1.1 2.4-1 1 0 1.9.3 2.6 1 .9.9 1.5 2.5 1.6 5 0 1.6-.5 2.1-1.8 2.1h-6.7c.1-3.5.8-6.1 1.9-7.1z" fill="#434343"></path>
    <path d="M169.8 32.1c1.7-.7 3.4-1.1 5.2-1.2 1 0 1.9.1 1.9.7 0 .3-.1.6-.1 1s.1.6.7.6c.9 0 2.1-1.4 2.1-2.7 0-1.4-1.2-2.2-3.6-2.2-1.5 0-2.9.3-4.4.7 2.3-3.9 2.7-4.8 3.8-5.8 1.3-1 1.4-1.6 1.4-2.4s-.5-1.2-1.4-1.2-1.6.7-2.8 2.3c-1.8 2.6-3.4 5.4-4.9 8.2-.7.2-1.4.2-2.1.3-.8 0-1-.5-2-.5-.9 0-1.7.7-1.8 1.6v.1c0 1 .8 1.4 2.9 1.4.5 0 1 0 1.4-.1-1.1 2.1-2.2 4.4-3.1 6.6-.2.2-.3.4-.5.6-2.5 3.3-6.6 7.6-9 7.6-.6 0-.9-.7-.9-1.6.1-1.4.5-2.7 1.2-3.9 1.4-2.7 3.2-5 3.2-5.9 0-.8-.7-1.4-1.8-1.4-1.2 0-1.5 1.4-2 1.4-.3 0-1.5-1.6-2.8-1.6-3.9 0-8.6 3.3-11.2 7.6-6.1 7.2-14.1 14.4-20.6 14.4-4.2 0-5.5-2.9-5.5-5.1 0-5.9 8.1-13.1 12.7-15.6 4.7-2.5 7.5-2.2 7.5-3.9s-.7-1.9-1.6-1.9c-.9 0-3.4 1.2-5.5 1.2s-3.5-.8-3.5-2.2c0-4.7 11.8-13.3 18.6-13.3.6-.1 1.1.3 1.1.9v.1c0 2-2.4 4.6-4.6 5.7-1.8.9-2.2 1.4-2.2 2.2 0 .7.5 1.3 1.8 1.3 2.9 0 8.2-6.5 8.2-9 0-2.7-1.6-3.8-3.8-3.8-8.7 0-22.8 9.7-22.8 16.8 0 2.4 2.7 3.9 4.5 3.9-5.2 3.6-11.8 9.2-13.2 15.5-.1.3-.1.6-.2 1-.1.5-.1 1-.1 1.5 0 4.1 2.9 7.5 8.2 7.5 6.6 0 13.2-5.5 19.3-11.9 0 .3-.1.6-.1.8 0 1.3.8 2.4 2 2.7 1.6 0 3.7-1.4 6.9-4.1 1.5-1.4 2.6-2.7 3-2.7.1 0 .1.2.1.6 0 .6-.1 1.6-.1 2.3-.1 1.7 1.2 3.1 2.8 3.2h.4c2.3 0 5.4-2.3 8.2-5-.6 1.7-1 3.5-1.1 5.3 0 1.7.7 2.7 1.7 2.7.8 0 1.4-.6 1.4-1.4 0-.6-.4-1-.4-1.8.1-3.3 3.9-11.5 7.5-18.1zm-28.3 16c-.4 0-.8-.3-.8-.7v-.1c0-3.3 6-9.7 8.9-9.7.4 0 .7.3.8.7v.1c0 .7 1.2.5 1.2.9-.1 1.1-9 8.8-10.1 8.8z" fill="#4B6E8F"></path>
    <path d="M176.5 34.4c-3 0-8.2 3.3-8.2 6.8 0 3.1 4.8 2.9 4.8 5.6.1 1.3-1 2.4-2.3 2.5h-.1c-.9.1-1.8-.5-1.9-1.4v-.4c0-.6-.6-1.1-1.2-1h-.1c-.9.1-1.6.8-1.5 1.7 0 1.6 1.8 3.3 4.2 3.3 3.9 0 5.9-2.4 5.9-4.7 0-4.4-4.9-4.1-4.9-6 0-2 3.2-3.8 4.1-3.8.5 0 .9.4 1 .9 0 .3-1 .5-1 1.6 0 .7.7 1.2 1.4 1.2 1.4 0 2.2-2.4 2.2-3.8.1-1.4-1.1-2.5-2.4-2.5z" fill="#4B6E8F"></path>
    </svg>
    </symbol>
    <symbol id="seriouseats-logo">
    <svg viewBox="0 0 242 33" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M30.444 33L7.44972 33C4.78079 33 2.61721 30.7395 2.61721 27.951L2.61721 11.3577H0.303703C0.135973 11.3577 0 11.2156 0 11.0404V9.77113C0 9.59588 0.135972 9.45382 0.303702 9.45382H2.61721L2.61721 6.8034C2.61721 6.62815 2.75318 6.48609 2.92091 6.48609L34.9728 6.48609C35.1406 6.48609 35.2765 6.62816 35.2765 6.8034V9.45382H37.7166C37.8843 9.45382 38.0203 9.59588 38.0203 9.77113V11.0404C38.0203 11.2156 37.8843 11.3577 37.7166 11.3577H35.2765L35.2765 27.951C35.2765 30.7395 33.1129 33 30.444 33Z" fill="#26B7C5"></path>
    <path d="M15.607 0C15.4393 0 15.3033 0.142064 15.3033 0.317308V1.58654C15.3033 1.76178 15.4393 1.90385 15.607 1.90385H16.8218C17.325 1.90385 17.733 2.33004 17.733 2.85577V3.33762H2.85155C2.68382 3.33762 2.54785 3.47969 2.54785 3.65493V4.92416C2.54785 5.09941 2.68382 5.24147 2.85155 5.24147H34.9043C35.072 5.24147 35.208 5.09941 35.208 4.92416V3.65493C35.208 3.47969 35.072 3.33762 34.9043 3.33762H20.1626V2.85577C20.1626 2.33004 20.5705 1.90385 21.0737 1.90385H22.2885C22.4562 1.90385 22.5922 1.76179 22.5922 1.58654V0.317308C22.5922 0.142064 22.4562 0 22.2885 0H15.607Z" fill="#26B7C5"></path>
    <path d="M54.9396 9.52148C59.0581 9.52148 62.0103 11.4636 64.0513 13.8626L61.3178 16.3378C59.3132 13.7484 57.09 12.9106 55.1218 12.9106C53.1537 12.9106 52.0238 13.7484 52.0238 15.0431C52.0238 16.2997 53.1537 16.909 55.0489 17.2517L57.2357 17.6325C61.0991 18.3179 64.1242 19.6507 64.1242 23.3064C64.1242 27.0002 60.9533 29.3611 56.6526 29.3611C52.1696 29.3611 48.7071 27.0763 46.8848 23.8015L50.0921 21.8213C51.6958 25.02 54.2471 25.9339 56.5797 25.9339C58.8759 25.9339 60.2244 24.9439 60.2244 23.573C60.2244 22.2021 59.0581 21.6309 57.0171 21.2501L54.8302 20.8693C50.7117 20.1839 48.124 18.5845 48.124 15.1573C48.124 11.7301 50.8939 9.52148 54.9396 9.52148Z" fill="#1B4D7A"></path>
    <path d="M74.4022 9.4834C79.5776 9.4834 83.113 13.2152 83.113 18.8891V20.6408H69.5182C69.7005 23.6111 71.6322 25.7435 74.5844 25.7435C77.3179 25.7435 78.7394 24.1823 79.4319 22.621L82.7485 24.5631C81.5458 26.6955 79.4319 29.3611 74.6208 29.3611C69.1902 29.3611 65.3633 25.0962 65.3633 19.2699C65.3633 13.6341 69.1173 9.4834 74.4022 9.4834ZM78.9945 17.5944C78.7758 14.8146 77.0264 13.101 74.3657 13.101C71.5228 13.101 69.8463 14.8908 69.5182 17.5944H78.9945Z" fill="#1B4D7A"></path>
    <path d="M89.1635 12.6817H89.6738C90.4027 10.7015 91.8606 9.97803 94.1932 9.97803H96.1249V13.6337H93.3549C90.9494 13.6337 89.2364 15.0046 89.2364 17.8225V28.8276H85.2637V10.0542H89.1635V12.6817Z" fill="#1B4D7A"></path>
    <path d="M100.756 2.59082C102.031 2.59082 103.016 3.50474 103.016 4.87562C103.016 6.20841 102.031 7.16041 100.756 7.16041C99.4802 7.16041 98.4961 6.24649 98.4961 4.87562C98.4961 3.50474 99.4802 2.59082 100.756 2.59082Z" fill="#1B4D7A"></path>
    <path d="M102.758 10.0547H98.7852V28.8281H102.758V10.0547Z" fill="#1B4D7A"></path>
    <path d="M114.424 9.52148C119.745 9.52148 123.754 13.558 123.754 19.4223C123.754 25.3247 119.745 29.3611 114.424 29.3611C109.066 29.3611 105.057 25.3247 105.057 19.4223C105.057 13.558 109.066 9.52148 114.424 9.52148ZM114.424 25.6293C117.631 25.6293 119.745 23.116 119.745 19.4223C119.745 15.7285 117.631 13.2533 114.424 13.2533C111.18 13.2533 109.066 15.7666 109.066 19.4223C109.066 23.1541 111.18 25.6293 114.424 25.6293Z" fill="#1B4D7A"></path>
    <path d="M139.277 28.8281V25.6675H138.767C137.965 27.4953 136.252 29.1327 132.862 29.1327C128.89 29.1327 125.828 26.2767 125.828 21.3263V10.0547H129.801V20.9075C129.801 23.8777 131.441 25.4771 134.211 25.4771C137.345 25.4771 139.204 23.4207 139.204 19.6889V10.0547H143.177V28.8281H139.277Z" fill="#1B4D7A"></path>
    <path d="M152.873 9.52148C156.992 9.52148 159.944 11.4636 161.985 13.8626L159.251 16.3378C157.247 13.7484 155.024 12.9106 153.055 12.9106C151.087 12.9106 149.957 13.7484 149.957 15.0431C149.957 16.2997 151.087 16.909 152.983 17.2517L155.169 17.6325C159.033 18.3179 162.058 19.6507 162.058 23.3064C162.058 27.0002 158.887 29.3611 154.586 29.3611C150.103 29.3611 146.641 27.0763 144.818 23.8015L148.026 21.8213C149.629 25.02 152.181 25.9339 154.513 25.9339C156.809 25.9339 158.158 24.9439 158.158 23.573C158.158 22.2021 156.992 21.6309 154.951 21.2501L152.764 20.8693C148.645 20.1839 146.058 18.5845 146.058 15.1573C146.058 11.7301 148.828 9.52148 152.873 9.52148Z" fill="#1B4D7A"></path>
    <path d="M181.551 9.4834C186.726 9.4834 190.261 13.2152 190.261 18.8891V20.6408H176.667C176.849 23.6111 178.781 25.7435 181.733 25.7435C184.466 25.7435 185.888 24.1823 186.58 22.621L189.897 24.5631C188.694 26.6955 186.58 29.3611 181.769 29.3611C176.339 29.3611 172.512 25.0962 172.512 19.2699C172.512 13.6341 176.266 9.4834 181.551 9.4834ZM186.143 17.5944C185.924 14.8146 184.175 13.101 181.514 13.101C178.671 13.101 176.995 14.8908 176.667 17.5944H186.143Z" fill="#1B4D7A"></path>
    <path d="M200.793 9.4834C205.568 9.4834 208.775 12.4156 208.775 16.9471V28.828H204.839V26.0101H204.329C203.563 27.4952 202.105 29.3611 198.315 29.3611C194.269 29.3611 191.572 27.1144 191.572 23.573C191.572 20.3362 194.051 18.4322 197.55 18.0895L204.766 17.2898V16.6424C204.766 14.5861 203.345 12.9106 200.611 12.9106C197.878 12.9106 196.274 14.5861 195.29 16.7947L192.01 14.7765C193.395 12.4536 195.982 9.4834 200.793 9.4834ZM199.226 25.8578C202.652 25.8578 204.766 23.7634 204.766 20.6789V20.3362L198.643 21.0216C196.748 21.2501 195.727 22.0879 195.727 23.4207C195.727 24.8677 197.003 25.8578 199.226 25.8578Z" fill="#1B4D7A"></path>
    <path d="M214.498 10.0542V3.92334H218.471V10.0542H223.756V13.5576H218.471V24.1438C218.471 24.9054 218.836 25.2862 219.492 25.2862H222.736V28.8276H217.779C215.883 28.8276 214.498 27.4187 214.498 25.4004V13.5576H209.578V10.0542H214.498Z" fill="#1B4D7A"></path>
    <path d="M232.287 9.52148C236.406 9.52148 239.358 11.4636 241.399 13.8626L238.665 16.3378C236.661 13.7484 234.438 12.9106 232.469 12.9106C230.501 12.9106 229.371 13.7484 229.371 15.0431C229.371 16.2997 230.501 16.909 232.397 17.2517L234.583 17.6325C238.447 18.3179 241.472 19.6507 241.472 23.3064C241.472 27.0002 238.301 29.3611 234 29.3611C229.517 29.3611 226.055 27.0763 224.232 23.8015L227.44 21.8213C229.043 25.02 231.595 25.9339 233.927 25.9339C236.224 25.9339 237.572 24.9439 237.572 23.573C237.572 22.2021 236.406 21.6309 234.365 21.2501L232.178 20.8693C228.059 20.1839 225.472 18.5845 225.472 15.1573C225.472 11.7301 228.242 9.52148 232.287 9.52148Z" fill="#1B4D7A"></path>
    </svg>
    </symbol>
    <symbol id="icon-privacy-options">
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 30 14">
    <path fill="#FFF" d="M7.4 12.8h6.8l3.1-11.6H7.4C4.2 1.2 1.6 3.8 1.6 7s2.6 5.8 5.8 5.8z"></path>
    <path fill="#06f" d="M22.6 0H7.4c-3.9 0-7 3.1-7 7s3.1 7 7 7h15.2c3.9 0 7-3.1 7-7s-3.2-7-7-7zm-21 7c0-3.2 2.6-5.8 5.8-5.8h9.9l-3.1 11.6H7.4c-3.2 0-5.8-2.6-5.8-5.8z"></path>
    <path fill="#FFF" d="M24.6 4c.2.2.2.6 0 .8L22.5 7l2.2 2.2c.2.2.2.6 0 .8-.2.2-.6.2-.8 0l-2.2-2.2-2.2 2.2c-.2.2-.6.2-.8 0-.2-.2-.2-.6 0-.8L20.8 7l-2.2-2.2c-.2-.2-.2-.6 0-.8.2-.2.6-.2.8 0l2.2 2.2L23.8 4c.2-.2.6-.2.8 0z"></path>
    <path fill="#06f" d="M12.7 4.1c.2.2.3.6.1.8L8.6 9.8c-.1.1-.2.2-.3.2-.2.1-.5.1-.7-.1L5.4 7.7c-.2-.2-.2-.6 0-.8.2-.2.6-.2.8 0L8 8.6l3.8-4.5c.2-.2.6-.2.9 0z"></path>
    ​</svg> </symbol>
    <symbol id="mntl-dotdash-universal-nav__logo">
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="5 2.8 290 69"><circle cx="39.6" cy="37.4" r="34.3" fill="#f44b34"></circle><path fill="#fff" d="M50.4 17.9c-1.7-1.8-4-2.6-7-2.6-9.1-.1-12.4-.1-13.2-.1L27.3 22h8.1L30 28v21h-.1l.1.1v6.7l-3.6 4h18.5L53 51V25c0-.1.1-4.3-2.6-7.1zm-5.2 19.6-7.4 7.8V41l7.4-7.8v4.3zm-7.4-15.4h3c.9 0 2.1-.1 3.1.9s1.4 2.5 1.4 4.9v3.6l-7.4 7.8-.1-17.2zm-4.9 30.1 3.3-3.6 9-9.5v13.1c0 .1-12.3.1-12.3 0z"></path><path fill="#009ad9" d="m99.645 22.637 6.505-6.506 4.172 4.172-6.505 6.506zM121 44l4.2 4.2 6.5-6.5-4.2-4.2zm-3.2-31.2c1.8-1.8 4.7-1.8 6.5 0 1.9 1.9 1.7 4.8 0 6.5l-3.3 3.3 4.2 4.2 3.3-3.3c2-2 4.9-1.6 6.5 0 2 2 1.6 4.9 0 6.5l-3.3 3.3 4.2 4.2 3.3-3.3c4.2-4.2 4-10.8 0-14.8-2.8-2.8-6.1-3.1-7.6-3.1.1-1.5-.3-4.8-3.1-7.6-3.9-3.9-10.6-4.2-14.8 0l-3.3 3.3 4.2 4.2 3.2-3.4zm-7.5 20.6 4.2 4.1L121 31l-4.2-4.1-6.5 6.5z"></path><path fill="#6ebd44" d="m99.6 52.4 6.6 6.5 4.1-4.2-6.5-6.5zm36.3-14.9-4.2 4.2L135 45c1.6 1.6 2 4.5 0 6.5-1.6 1.6-4.5 2.1-6.5 0l-3.3-3.3-4.2 4.2 3.3 3.3c1.7 1.7 1.9 4.6 0 6.5-1.8 1.8-4.7 1.8-6.5 0l-3.3-3.3-4.2 4.2 3.3 3.3c4.2 4.2 10.9 3.9 14.8 0 2.8-2.8 3.1-6.1 3.1-7.6 1.5 0 4.8-.3 7.6-3.1 4-4 4.2-10.6 0-14.8l-3.2-3.4zm-25.6 4.2 6.5 6.5L121 44l-6.5-6.5zm14.9-14.8L121 31l6.5 6.5 4.2-4.1-6.5-6.5z"></path><path fill="#f68f1e" d="m110.332 54.607 6.506-6.506 4.172 4.172-6.506 6.505zM102.8 62.2c-1.6 1.6-4.5 2-6.5 0-1.6-1.6-2.1-4.5 0-6.5l3.3-3.3-4.2-4.2-3.3 3.3c-1.7 1.7-4.6 1.9-6.5 0-1.8-1.8-1.8-4.7 0-6.5l3.3-3.3-4.2-4.2-3.3 3.3c-4.2 4.2-3.9 10.9 0 14.8 2.8 2.8 6.1 3.1 7.6 3.1 0 1.5.3 4.8 3.1 7.6 4 4 10.6 4.2 14.8 0l3.3-3.3-4.2-4.2-3.2 3.4zm3.4-24.7L99.6 44l4.2 4.2 6.5-6.5zM95.5 26.9 89 33.4l4.1 4.1 6.5-6.5z"></path><path fill="#ec174c" d="m99.6 44-6.5-6.5-4.1 4.2 6.5 6.5zm-14-14c-1.8-1.8-1.8-4.7 0-6.5 1.9-1.9 4.8-1.7 6.5 0l3.3 3.3 4.2-4.2-3.3-3.3c-2-2-1.6-4.9 0-6.5 2-2 4.9-1.6 6.5 0l3.3 3.3 4.2-4.2-3.3-3.2c-4.2-4.2-10.8-4-14.8 0-2.8 2.8-3.1 6.1-3.1 7.6-1.5-.1-4.9.3-7.6 3.1-3.9 3.9-4.2 10.6 0 14.8l3.3 3.3 4.2-4.2-3.4-3.3zm24.73-9.682 4.172-4.172 6.505 6.505-4.172 4.172zM106.2 37.5l4.1-4.1-6.5-6.5-4.2 4.1 6.6 6.5z"></path><path fill="#573357" d="m106.142 16.042 4.171-4.172 4.172 4.172-4.171 4.172z"></path><path fill="#be272d" d="m84.81 37.543 4.172-4.172 4.172 4.172-4.172 4.172z"></path><path fill="#008e4c" d="m127.497 37.555 4.171-4.172 4.172 4.172-4.171 4.172z"></path><path fill="#7a773e" d="m106.172 58.866 4.172-4.172 4.172 4.172-4.172 4.172z"></path><path fill="#443639" d="m116.856 26.74 4.172-4.172 4.172 4.172-4.172 4.172z"></path><path fill="#803136" d="m95.455 48.169 4.172-4.172 4.172 4.172-4.172 4.172z"></path><path fill="#593438" d="m95.446 26.753 4.172-4.172 4.172 4.173-4.172 4.171z"></path><path fill="#443639" d="m106.155 37.456 4.172-4.172 4.172 4.171-4.172 4.172z"></path><path fill="#2a6442" d="m116.871 48.153 4.172-4.172 4.172 4.172-4.172 4.172z"></path><path d="M170.4 13.5c-2.5-2.2-5.7-3.3-9.6-3.3h-7.2v23.6h7.6c3.8 0 7-1.1 9.4-3.3s3.6-5 3.6-8.5-1.3-6.3-3.8-8.5zm.1 8.5c0 2.6-.9 4.7-2.6 6.2-1.7 1.6-4.1 2.3-7 2.3h-3.8V13.4h3.8c2.9 0 5.2.8 7 2.3 1.7 1.5 2.6 3.7 2.6 6.3zm14.6-6.3c-2.5 0-4.7.9-6.4 2.7s-2.6 4-2.6 6.6.9 4.8 2.6 6.6c1.7 1.8 3.9 2.7 6.4 2.7h.1v-.1c2.5 0 4.7-.9 6.4-2.7s2.6-4 2.6-6.6c0-2.5-.9-4.8-2.6-6.6-1.8-1.7-3.9-2.6-6.5-2.6zm4.1 13.7c-.5.6-1.1 1-1.8 1.3s-1.4.3-2.2.3c-1.7 0-3-.6-4.1-1.8-1.1-1.2-1.6-2.7-1.6-4.4 0-1.7.6-3.2 1.6-4.3 1.1-1.2 2.4-1.7 4.1-1.7 1.6 0 3 .6 4 1.7 1.1 1.2 1.6 2.6 1.6 4.3 0 1.9-.5 3.4-1.6 4.6zm16.9.8c-.5.6-1.3.9-2.3.9-1.6 0-2.4-.9-2.4-2.7v-9.2h5v-3h-5v-4.4h-3.3v4.4h-2.9l.1 3h2.9v9.3c0 1.9.5 3.3 1.4 4.3s2.2 1.5 3.9 1.5c1.8 0 3.2-.5 4.2-1.5l-1.6-2.6zm38.1-11.6c-1.5-2-3.5-3-6.1-3-2.5 0-4.5.9-6.1 2.7-1.6 1.8-2.4 4-2.4 6.6 0 2.7.8 4.9 2.5 6.6 1.7 1.8 3.7 2.7 6.1 2.7 2.6 0 4.6-1 6.1-3.1v2.6h3.4V16.1h-3.4v2.5h-.1zm-1.6 2c1.1 1.2 1.6 2.6 1.6 4.3s-.5 3.2-1.6 4.4c-.5.6-1.1 1-1.8 1.3s-1.4.4-2.2.4c-1.7 0-3-.6-4.1-1.8-1.1-1.2-1.6-2.7-1.6-4.4 0-1.7.6-3.2 1.6-4.3 1.1-1.2 2.4-1.7 4.1-1.7 1.6.1 2.9.6 4 1.8zm17.9 3.6c-.9-.3-1.9-.6-3-.9-1-.3-1.9-.6-2.5-1-.7-.4-1-1-1-1.6l.1-.1c0-.7.3-1.2.9-1.6.6-.4 1.4-.6 2.3-.6 2 0 3.4.7 4.5 2l2-2c-1.3-1.8-3.5-2.7-6.6-2.7-1.9 0-3.5.5-4.7 1.4-1.2 1-1.7 2.1-1.7 3.5 0 1.2.3 2.1 1 2.9s1.5 1.3 2.6 1.7c1.1.4 2 .7 2.9 1 1 .3 1.8.7 2.4 1.1.7.5 1 1 1 1.8s-.3 1.4-.9 1.8c-.6.5-1.4.7-2.5.7-2.3 0-4.1-.8-5.3-2.3l-2 2.2c1.5 1.9 3.9 2.9 7.1 2.9 2.2 0 3.9-.5 5.1-1.5s1.8-2.3 1.8-3.8c0-1.2-.3-2.2-1-3-.6-.9-1.5-1.4-2.5-1.9zm14.9-8.6c-2.3 0-4.1.8-5.2 2.5V8.4h-3.4v25.4h3.4l.1-10.3c0-1.4.4-2.6 1.2-3.4.8-.8 1.8-1.2 3.2-1.2 1.3 0 2.4.4 3.2 1.3.8.8 1.2 2 1.2 3.4v10.3h3.4V23c0-2.3-.6-4-1.9-5.4-1.4-1.4-3.1-2-5.2-2zm-52.2 3c-1.5-2-3.5-3-6.1-3-2.5 0-4.5.9-6.1 2.7-1.6 1.8-2.4 4-2.4 6.6 0 2.7.8 4.9 2.5 6.6 1.7 1.8 3.7 2.7 6.1 2.7 2.6 0 4.6-1 6.1-3.1v2.6h3.4V8.4h-3.4v10.2h-.1zm-1.6 2c1.1 1.2 1.6 2.6 1.6 4.3s-.5 3.2-1.6 4.4c-1.1 1.2-2.4 1.8-4 1.8-1.7 0-3-.6-4.1-1.8-1.1-1.2-1.6-2.7-1.6-4.4 0-1.7.6-3.2 1.6-4.3 1.1-1.2 2.4-1.7 4.1-1.7 1.6 0 2.9.5 4 1.7zm48.8 19.9h-3.6v19.1c0 2.4.7 4.1 2.1 5.1 1.2.9 3.1 1.4 5.6 1.4h.1v-3h-.1c-1.2 0-2.1-.2-2.7-.6-.9-.6-1.4-1.7-1.4-3.3V51h4.1v-3h-4.1v-7.5zm-96.2 7c-1.4 0-2.6.3-3.7 1-1 .6-1.7 1.5-2.3 2.6-1.2-2.4-3.2-3.6-6-3.6-2.2 0-4 .9-5.2 2.6V48h-3.5v18.2h3.5l.2-10.7c0-1.5.4-2.6 1.1-3.5.7-.9 1.8-1.3 3.1-1.3s2.3.4 3.1 1.3c.8.9 1.2 2 1.2 3.5v10.6h3.4V55.5c0-1.5.4-2.6 1.2-3.5s1.8-1.3 3.2-1.3c1.3 0 2.3.4 3.1 1.3.7.9 1.1 2 1.1 3.5v10.6h3.5V55c0-2.3-.6-4.1-1.9-5.5-1.3-1.3-3-2-5.1-2zm18.9 0c-2.5 0-4.7.9-6.4 2.8-1.7 1.9-2.6 4.1-2.6 6.8s.9 5 2.7 6.8c1.8 1.8 4.1 2.8 6.8 2.8h.1v-.1c1.4 0 2.7-.3 3.9-.7 1.2-.5 2.2-1.1 3-1.9l-1.8-2.4c-1.3 1.3-3 2-5 2-1.7 0-3.1-.5-4.2-1.6-1.1-1-1.8-2.4-2-4h14.3c.1-.3.1-.7.1-1.2 0-2.5-.9-4.7-2.6-6.5-1.6-1.8-3.8-2.8-6.3-2.8zm.1 3c1.4 0 2.6.5 3.6 1.3 1 .9 1.6 2.1 1.8 3.8h-10.8c.3-1.5.9-2.7 1.8-3.7 1-.9 2.2-1.4 3.6-1.4zm22.5-2.3c-.6-.5-1.6-.8-2.8-.8-1.9 0-3.4.9-4.5 2.6v-2H205v18.2h3.5l-.1-10.5c0-1.5.3-2.7.9-3.6.6-.9 1.5-1.3 2.6-1.3.9 0 1.7.2 2.3.7h.1l1.4-3.3zm9-.7c-2.5 0-4.7.9-6.4 2.8-1.7 1.9-2.6 4.1-2.6 6.8s.9 5 2.7 6.8c1.8 1.8 4.1 2.8 6.8 2.8h.1v-.1c1.4 0 2.7-.3 3.9-.7 1.2-.5 2.2-1.1 3-1.9l-1.8-2.4c-1.3 1.3-3 2-5 2-1.7 0-3.1-.5-4.2-1.6-1.1-1-1.8-2.4-2-4h14.3c.1-.3.1-.7.1-1.2 0-2.5-.9-4.7-2.6-6.5-1.6-1.8-3.8-2.8-6.3-2.8zm.1 3c1.4 0 2.6.5 3.6 1.3 1 .9 1.6 2.1 1.8 3.8h-10.8c.3-1.5.9-2.7 1.8-3.7.9-.9 2.1-1.4 3.6-1.4zm26.1 0c-1.5-2-3.6-3.1-6.3-3.1-2.5 0-4.6.9-6.3 2.7s-2.5 4.1-2.5 6.8.9 5 2.5 6.8c1.7 1.8 3.8 2.7 6.3 2.7 2.6 0 4.7-1.1 6.3-3.2v2.7h3.5V40.5h-3.5v10zm-1.7 2c1.1 1.2 1.6 2.7 1.6 4.5 0 1.7-.6 3.3-1.6 4.5-1.1 1.2-2.4 1.8-4.2 1.8-1.7 0-3.1-.6-4.2-1.8-1.1-1.2-1.7-2.7-1.7-4.5s.6-3.3 1.7-4.5c1.1-1.2 2.5-1.8 4.2-1.8 1.8.1 3.1.7 4.2 1.8zm43.5-3c-1.3-1.4-3-2.1-5.2-2.1-2.4 0-4.2.9-5.4 2.6v-9.6h-3.5v25.7h3.5l.1-10.5c0-1.5.4-2.6 1.2-3.5.8-.9 1.8-1.3 3.2-1.3 1.4 0 2.5.4 3.3 1.3.8.9 1.2 2 1.2 3.5v10.6h3.5V55c0-2.3-.6-4.1-1.9-5.5zM259.1 48h3.5v18.2h-3.5zm0-7.5h3.4v3.4h-3.4z"></path></svg> </symbol>
    <symbol id="icon-arrow">
    <svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <circle cx="8" cy="8" r="8" fill="inherit"></circle>
    <path d="M10.666 11.4075L4.74009 11.4075L4.74009 5.48157" stroke="#1B4D7A"></path>
    <path d="M4.73958 11.4076L11.4062 4.74097" stroke="#1B4D7A"></path>
    </svg> </symbol>
    <symbol id="arrow-circle-right">
    <svg viewBox="0 0 25 25" fill="inherit" xmlns="http://www.w3.org/2000/svg">
    <circle cx="12.5" cy="12.5" r="11.75" fill="inherit" stroke-width="1.5"></circle>
    <path d="M13.0397 6.08821L19.1966 12.2451L13.0397 18.402" stroke="inherit" stroke-width="1.5"></path>
    <path d="M18.9803 12.0378L5.24219 12.0371" stroke="inherit" stroke-width="1.5"></path>
    </svg> </symbol>
    <symbol id="icon-arrow--circle">
    <svg viewBox="0 0 26 26" xmlns="http://www.w3.org/2000/svg">
    <circle cx="13" cy="13" r="13" fill="inherit"></circle>
    <path d="M12 7.85933L17.1407 13L12 18.1407" stroke="#385E83"></path>
    </svg> </symbol>
    <symbol id="mntl-carousel__arrow-icon">
    <svg viewBox="0 0 25 25" fill="inherit" xmlns="http://www.w3.org/2000/svg">
    <circle cx="12.5" cy="12.5" r="11.75" fill="inherit" stroke-width="1.5"></circle>
    <path d="M13.0397 6.08821L19.1966 12.2451L13.0397 18.402" stroke="inherit" stroke-width="1.5"></path>
    <path d="M18.9803 12.0378L5.24219 12.0371" stroke="inherit" stroke-width="1.5"></path>
    </svg> </symbol>
    <symbol id="our-picks">
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100">
    <style type="text/css">
    .st0{fill:#99F2FB;}
    .st1{fill:#333333;}
    </style>
    <path class="st0" d="M50,100c27.6,0,50-22.4,50-50S77.6,0,50,0S0,22.4,0,50S22.4,100,50,100z"></path>
    <path class="st1" d="m61.7 7c2.1 0.6 3.1 2.2 2.5 4.4l-0.5 1.8c-0.6 2.2-2.2 3.1-4.4 2.6-2.1-0.6-3.1-2.2-2.5-4.4l0.5-1.8c0.7-2.2 2.3-3.2 4.4-2.6zm-3.4 4.8c-0.4 1.3 0.1 2.4 1.4 2.7 1.4 0.4 2.3-0.3 2.6-1.6l0.5-1.9c0.4-1.3-0.2-2.4-1.4-2.7-1.3-0.4-2.3 0.4-2.6 1.6l-0.5 1.9z"></path>
    <path class="st1" d="m69.6 10.2-2.8 5.1c-0.6 1-0.3 2 0.8 2.6s2 0.3 2.6-0.7l2.8-5.2 1.3 0.7-2.7 5c-1 1.8-2.6 2.2-4.5 1.2s-2.4-2.7-1.4-4.4l2.7-5 1.2 0.7z"></path>
    <path class="st1" d="m77.2 21.9-0.9 3.8-1.2-1.1 1-3.6-1.2-1.1-2.2 2.5-1.1-1 5.8-6.5 2.8 2.5c1.2 1.1 1.4 2.6 0.3 3.9-1 1-2.2 1.1-3.3 0.6zm-1.5-3 1.6 1.4c0.7 0.6 1.5 0.6 2 0s0.5-1.4-0.2-2l-1.6-1.4-1.8 2z"></path>
    <path class="st1" d="m89.2 29.2c0.8 1.4 0.4 3-1 3.8s-3 0.3-3.7-1.1l-1-1.8-2.9 1.6-0.7-1.3 7.7-4.2 1.6 3zm-4.6 0.2 0.9 1.7c0.4 0.8 1.2 1 1.9 0.6 0.8-0.4 1-1.2 0.5-2l-0.8-1.7-2.5 1.4z"></path>
    <path class="st1" d="m92.1 35.8-8.2 2.9-0.5-1.4 8.2-2.9 0.5 1.4z"></path>
    <path class="st1" d="m93.9 42.4c0.4 2.1-0.6 3.5-2.5 3.9h-0.3l-0.2-1.4h0.3c1.1-0.2 1.7-1 1.5-2.2-0.2-1.3-1.3-1.9-2.6-1.7l-2 0.4c-1.3 0.2-2.1 1.1-1.8 2.5 0.2 1.4 1.1 1.8 2.2 1.6l0.3-0.1 0.2 1.4h-0.3c-1.9 0.3-3.3-0.6-3.7-2.8-0.4-2.1 0.6-3.7 2.9-4.1l1.9-0.3c2.1-0.5 3.7 0.6 4.1 2.8z"></path>
    <path class="st1" d="m90.7 52.5-5.3 3.4 0.1-1.8 4.3-2.7-1-1.2-3.1-0.2 0.1-1.4 8.7 0.5-0.1 1.4-3.7-0.2v0.2l3.5 4-0.1 1.9-3.4-3.9z"></path>
    <path class="st1" d="m89.3 64.6-0.4-0.1 0.4-1.3 0.3 0.1c1 0.3 1.7-0.3 2-1.3 0.3-0.9 0-1.6-0.7-1.8-0.8-0.2-1.2 0.4-1.8 1.4l-0.1 0.1c-0.8 1.5-1.7 2.4-3.2 1.9-1.6-0.5-2.2-2-1.7-3.8 0.6-2 2.1-3 4-2.4l0.3 0.1-0.4 1.4-0.2-0.1c-1.2-0.3-2.1 0.1-2.5 1.5-0.3 1.1 0.1 1.8 0.8 2 0.8 0.2 1.3-0.4 1.8-1.4l0.1-0.2c0.8-1.6 1.7-2.4 3.2-2s2 1.9 1.6 3.6c-0.5 1.7-1.8 2.8-3.5 2.3z"></path>
    <path class="st1" d="m82.7 80.2c-1.5 1.6-3.4 1.7-5.1 0.2l-1.4-1.3c-1.7-1.6-1.7-3.4-0.3-5 1.5-1.6 3.4-1.7 5.1-0.2l1.4 1.3c1.8 1.5 1.8 3.4 0.3 5zm-2.6-5.3c-1-0.9-2.1-1-3.1 0s-0.7 2.2 0.2 3.1l1.5 1.4c1 0.9 2.2 1 3.1 0s0.7-2.2-0.2-3.1l-1.5-1.4z"></path>
    <path class="st1" d="m76.3 85.8-3.2-4.8c-0.7-1-1.6-1.2-2.6-0.5s-1.2 1.6-0.5 2.6l3.2 4.8-1.2 0.8-3.2-4.7c-1.1-1.7-0.8-3.4 1-4.5 1.8-1.2 3.5-0.9 4.6 0.8l3.2 4.7-1.3 0.8z"></path>
    <path class="st1" d="m62.4 87.1-2.9-2.6 1.5-0.6 2.8 2.5 1.6-0.6-1.2-3.1 1.4-0.5 3.1 8.2-3.5 1.3c-1.5 0.6-3 0-3.6-1.5-0.6-1.2-0.1-2.4 0.8-3.1zm3.4 0-2 0.8c-0.9 0.3-1.2 1-0.9 1.8s1 1.1 1.9 0.8l2-0.8-1-2.6z"></path>
    <path class="st1" d="m50.4 94.4c-1.6 0-2.8-1-2.9-2.7 0-1.6 1.1-2.7 2.7-2.8h2.1l-0.1-3.3h1.5l0.2 8.7-3.5 0.1zm2-4.2h-1.9c-0.9 0-1.5 0.6-1.5 1.4 0 0.9 0.6 1.4 1.5 1.4h1.9v-2.8z"></path>
    <path class="st1" d="m43.3 93.9 1.2-8.7 1.4 0.2-1.2 8.7-1.4-0.2z"></path>
    <path class="st1" d="m36.6 92.5c-2.1-0.6-2.9-2.2-2.3-4l0.1-0.3 1.4 0.4-0.1 0.3c-0.3 1.1 0.1 2 1.3 2.4 1.3 0.4 2.3-0.3 2.7-1.5l0.6-2c0.4-1.3 0-2.4-1.4-2.8-1.3-0.4-2.1 0.1-2.4 1.2l-0.1 0.3-1.4-0.4 0.1-0.3c0.6-1.8 2-2.7 4.2-2 2.1 0.6 3 2.3 2.3 4.5l-0.6 1.8c-0.6 2.1-2.3 3-4.4 2.4z"></path>
    <path class="st1" d="m29 85-0.6-6.3 1.5 0.9 0.4 5.1 1.5-0.3 1.6-2.7 1.2 0.7-4.4 7.5-1.2-0.8 1.9-3.2-0.1-0.1-5.1 1.3-1.7-1 5-1.1z"></path>
    <path class="st1" d="m18.9 78.2 0.3-0.3 1 1-0.2 0.2c-0.7 0.8-0.5 1.6 0.2 2.4 0.7 0.6 1.5 0.7 2 0.2 0.6-0.6 0.2-1.3-0.4-2.2l-0.2-0.2c-1-1.5-1.3-2.6-0.2-3.8 1.2-1.2 2.8-1 4.2 0.2 1.5 1.5 1.7 3.2 0.3 4.7l-0.2 0.2-1-1 0.2-0.2c0.9-0.9 0.9-1.9-0.1-2.9-0.8-0.8-1.7-0.8-2.2-0.2-0.6 0.6-0.3 1.3 0.4 2.3l0.2 0.2c1 1.5 1.3 2.6 0.3 3.8-1 1.1-2.7 0.9-3.9-0.3-1.6-1.2-1.9-2.9-0.7-4.1z"></path>
    <path class="st1" d="m8.1 65.2c-0.7-2.1 0.1-3.8 2.2-4.6l1.7-0.6c2.2-0.8 3.9 0 4.6 2.1s0 3.8-2.2 4.6l-1.8 0.6c-2 0.7-3.8-0.1-4.5-2.1zm5.9 0c1.3-0.5 1.9-1.4 1.4-2.7s-1.6-1.7-2.8-1.2l-1.9 0.7c-1.3 0.5-1.9 1.5-1.4 2.7 0.5 1.3 1.6 1.7 2.8 1.2l1.9-0.7z"></path>
    <path class="st1" d="m6.1 56.9 5.7-0.6c1.2-0.1 1.8-0.9 1.6-2.1-0.1-1.2-0.9-1.8-2.1-1.7l-5.7 0.6-0.2-1.4 5.7-0.6c2-0.2 3.3 0.9 3.6 3 0.2 2.1-0.8 3.5-2.8 3.7l-5.7 0.6-0.1-1.5z"></path>
    <path class="st1" d="m11.3 43.9 3.7-1.4-0.2 1.6-3.5 1.3-0.2 1.7 3.3 0.4-0.2 1.4-8.7-1 0.5-3.8c0.2-1.6 1.4-2.7 3-2.5 1.3 0.2 2.2 1.2 2.3 2.3zm-1.5 3 0.2-2.1c0.1-0.9-0.4-1.5-1.2-1.6-0.8-0.2-1.4 0.3-1.5 1.2l-0.3 2.2 2.8 0.3z"></path>
    <path class="st1" d="m10.4 29.9c0.7-1.5 2.2-2 3.7-1.3s1.9 2.3 1.2 3.7l-0.9 1.9 3 1.5-0.6 1.3-7.9-3.8 1.5-3.3zm2.8 3.7 0.8-1.7c0.4-0.8 0.2-1.6-0.6-2s-1.5-0.1-1.9 0.7l-0.8 1.7 2.5 1.3z"></path>
    <path class="st1" d="m14.2 23.8 7.1 5.1-0.8 1.1-7.2-5 0.9-1.2z"></path>
    <path class="st1" d="m18.5 18.5c1.5-1.5 3.2-1.5 4.6-0.2l0.2 0.2-1 1-0.2-0.2c-0.8-0.8-1.8-0.8-2.7 0.1s-0.8 2.2 0.1 3.1l1.5 1.4c1 1 2.1 1 3.1 0.1 1-1 0.8-1.9 0.1-2.7l-0.2-0.2 1-1 0.2 0.2c1.4 1.4 1.4 3-0.1 4.6-1.5 1.5-3.4 1.7-5.1 0l-1.4-1.4c-1.7-1.5-1.7-3.4-0.1-5z"></path>
    <path class="st1" d="m28.6 15.3 5.9 2.4-1.5 0.9-4.7-2-0.4 1.5 1.7 2.7-1.2 0.8-4.7-7.4 1.2-0.8 2 3.1 0.1-0.1 1.2-5.2 1.7-1-1.3 5.1z"></path>
    <path class="st1" d="m39.3 9.4 0.1 0.4-1.3 0.5-0.1-0.3c-0.4-1-1.2-1.2-2.2-0.9-0.9 0.3-1.3 1-1.1 1.6 0.3 0.8 1.1 0.8 2.2 0.6h0.3c1.7-0.2 2.9 0.1 3.4 1.5 0.6 1.6-0.4 3-2.1 3.6-2 0.7-3.6 0.1-4.3-1.9l-0.2-0.2 1.3-0.5 0.1 0.2c0.4 1.2 1.3 1.6 2.6 1.2 1.1-0.4 1.4-1.1 1.2-1.8-0.3-0.8-1-0.8-2.2-0.7h-0.3c-1.8 0.2-3 0-3.5-1.5-0.5-1.4 0.4-2.8 2-3.3 1.9-0.6 3.5-0.1 4.1 1.5z"></path>
    <path class="st1" d="m34 56.2c-1.7 1.2-4.1 0.9-5.3-0.8s-0.9-4.1 0.8-5.3l20.6-15.2c5.7-4.3 13.7-3.1 18 2.7 4.3 5.7 3.1 13.7-2.7 18l-11.9 8.8c-0.4 0.3-0.9 0.2-1.2-0.2s-0.2-0.9 0.2-1.2l11.9-9c4.9-3.7 6-10.6 2.3-15.6s-10.6-6-15.6-2.3l-20.6 15.2c-0.9 0.7-1.1 2.1-0.5 3 0.7 0.9 2.1 1.1 3 0.5l8.7-6.4c0.4-0.3 0.9-0.2 1.2 0.2s0.2 0.9-0.2 1.2l-8.7 6.4z"></path>
    <path class="st1" d="m44 56.2c-1.7 1.2-4.1 0.9-5.3-0.8s-0.9-4.1 0.8-5.3l5.7-4.3c0.4-0.3 0.9-0.2 1.2 0.2s0.2 0.9-0.2 1.2l-5.8 4.2c-0.9 0.7-1.1 2.1-0.5 3 0.7 0.9 2.1 1.1 3 0.5l5.7-4.3c0.4-0.3 0.9-0.2 1.2 0.2s0.2 0.9-0.2 1.2l-5.6 4.2z"></path>
    <path class="st1" d="m47.6 61.1c-1.7 1.2-4.1 0.9-5.3-0.8s-0.9-4.1 0.8-5.3l5.7-4.3c0.4-0.3 0.9-0.2 1.2 0.2s0.2 0.9-0.2 1.2l-5.7 4.3c-0.9 0.7-1.1 2.1-0.5 3 0.7 0.9 2.1 1.1 3 0.5l7.6-5.6c0.4-0.3 0.9-0.2 1.2 0.2s0.2 0.9-0.2 1.2l-7.6 5.4z"></path>
    <path class="st1" d="m54.4 53.9c1.5-1.1 3.7-0.8 4.9 0.8l0.4 0.6c1.1 1.5 0.8 3.7-0.8 4.9l-8.2 6c-1.5 1.1-3.7 0.8-4.9-0.8l-0.4-0.6c-1.1-1.5-0.8-3.7 0.8-4.9l8.2-6zm-7.5 9.8 0.4 0.6c0.6 0.8 1.7 0.9 2.4 0.3l8.2-6c0.8-0.6 0.9-1.7 0.3-2.4l-0.3-0.6c-0.6-0.8-1.7-0.9-2.4-0.3l-8.2 6c-0.8 0.6-1 1.7-0.4 2.4z"></path>
    <path class="st1" d="m56.5 49.7c1.2 1.7 0.9 4.1-0.8 5.3s-4.1 0.9-5.3-0.8l-7.2-9.6c-1.2-1.7-0.9-4.1 0.8-5.3 0.4-0.3 0.9-0.2 1.2 0.2s0.2 0.9-0.2 1.2c-0.9 0.7-1.1 2.1-0.5 3l7.2 9.6c0.7 0.9 2.1 1.1 3 0.5 0.9-0.7 1.1-2.1 0.5-3l-5.4-7.2c-0.3-0.4-0.2-0.9 0.2-1.2s0.9-0.2 1.2 0.2l5.3 7.1z"></path>
    <path class="st1" d="m60.9 43.9c0.4-0.3 0.9-0.2 1.2 0.2s0.2 0.9-0.2 1.2c-3.9 2.9-9.3 2.1-12.1-1.8-0.3-0.4-0.2-0.9 0.2-1.2s0.9-0.2 1.2 0.2c2.3 3 6.6 3.7 9.7 1.4z"></path>
    </svg>
    </symbol>
    </defs>
    </svg>
    <!-- done svg resources -->
    <a href="#skip-to-content" rel="nocaes" id="skip-to-content_1-0" class="skip-to-content mntl-text-link " data-tracking-container="true"><span class="link__wrapper">Skip to Content</span></a>
    <header id="header_1-0" class="comp header header--home js-menu-target js-search-target" role="banner" data-tracking-container="true"> <div class="header__inner l-container">
    <form id="header__search_1-0" class="comp header__search general-search" role="search" method="get" action="/search" data-tracking-container="true"> <button type="button" class="general-search__button general-search__button--close js-close-search btn btn--bare" aria-label="Close search form">
    Close search
    <svg class="icon icon-x-sticks btn__icon btn__close">
    <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#icon-x-sticks" href="#icon-x-sticks"></use>
    </svg>
    </button>
    <button type="button" class="general-search__button js-open-search" aria-label="Open search form">
    Search
    <svg class="icon icon-search general-search__icon btn__icon">
    <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#icon-search" href="#icon-search"></use>
    </svg>
    </button>
    <input class="general-search__input js-search-input" type="search" name="q" value="" placeholder="Search for recipes &amp; articles..." autocomplete="on" required="">
    <button type="submit" class="general-search__button js-submit-search is-hidden" aria-label="Submit search form">
    Search
    <svg class="icon icon-search general-search__icon btn__icon">
    <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#icon-search" href="#icon-search"></use>
    </svg>
    </button>
    </form> <div class="seriouseats-header__upper header__upper">
    <button id="menu-button_1-0" class="comp menu-button button btn--bare header__menu-btn js-menu-trigger btn" type="button">
    <svg class="btn__icon btn__hamburger">
    <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#icon-hamburger"></use>
    </svg>
    button
    </button>
    <button id="close-button_1-0" class="comp close-button button btn--bare header__close-btn js-menu-trigger btn--bare header__close-btn js-close-nav btn" type="button">
    <svg class="btn__icon btn__close">
    <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#icon-x-sticks"></use>
    </svg>
    button
    </button>
    <a id="logo-nav_1-0" class="comp logo-nav logo seriouseats" rel="home" href="https://www.seriouseats.com/" aria-label="Visit Serious Eats's homepage">
    <svg class="logo__text logo--light seriouseats-logo " xmlns="http://www.w3.org/2000/svg" viewBox="0 0 272 48" preserveAspectRatio="">
    <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#seriouseats-logo"></use>
    </svg>
    </a> </div>
    <div class="seriouseats-header__lower header__lower">
    <div class="header__nav-container js-search-target">
    <nav id="global-nav_1-0" class="comp global-nav" role="navigation" data-tracking-container="true"> <ul class="global-nav__list js-global-nav">
    <li class="global-nav__list-item js-global-nav-item is-disabled">
    <a href="https://www.seriouseats.com/all-recipes-5117985" rel="nocaes" class="global-nav__list-item-link"> <span>Recipes</span>
    </a>
    <svg class="icon icon-navigation-arrow global-nav__list-item-icon">
    <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#icon-navigation-arrow" href="#icon-navigation-arrow"></use>
    </svg>
    <ul class="global-nav__sub-list">
    <li>
    <a href="https://www.seriouseats.com/recipes-by-course-5117906" rel="nocaes" class="global-nav__sub-list-item">Recipes by Course</a>
    </li>
    <li>
    <a href="https://www.seriouseats.com/recipes-by-ingredient-recipes-5117749" rel="nocaes" class="global-nav__sub-list-item">Recipes by Ingredient</a>
    </li>
    <li>
    <a href="https://www.seriouseats.com/recipes-by-world-cuisine-5117277" rel="nocaes" class="global-nav__sub-list-item">Recipes by Cuisine</a>
    </li>
    <li>
    <a href="https://www.seriouseats.com/recipes-by-method-5117399" rel="nocaes" class="global-nav__sub-list-item">Recipes by Method</a>
    </li>
    <li>
    <a href="https://www.seriouseats.com/recipes-by-diet-5117779" rel="nocaes" class="global-nav__sub-list-item">Recipes by Diet</a>
    </li>
    <li>
    <a href="https://www.seriouseats.com/holiday-season-recipes-5117984" rel="nocaes" class="global-nav__sub-list-item">Recipes by Holiday &amp; Season</a>
    </li>
    <li>
    <a href="https://www.seriouseats.com/all-recipes-5117985" rel="nocaes" class="global-nav__sub-list-item global-nav__see-all"> View all
    </a> </li>
    </ul>
    </li>
    <li class="global-nav__list-item js-global-nav-item is-disabled">
    <a href="https://www.seriouseats.com/how-tos-5118034" rel="nocaes" class="global-nav__list-item-link"> <span>How-Tos</span>
    </a>
    <svg class="icon icon-navigation-arrow global-nav__list-item-icon">
    <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#icon-navigation-arrow" href="#icon-navigation-arrow"></use>
    </svg>
    <ul class="global-nav__sub-list">
    <li>
    <a href="https://www.seriouseats.com/techniques-5118032" rel="nocaes" class="global-nav__sub-list-item">Techniques</a>
    </li>
    <li>
    <a href="https://www.seriouseats.com/tips-trouble-shooting-5118014" rel="nocaes" class="global-nav__sub-list-item">Tips &amp; Troubleshooting</a>
    </li>
    <li>
    <a href="https://www.seriouseats.com/grilling-guides-5118026" rel="nocaes" class="global-nav__sub-list-item">Grilling Guides</a>
    </li>
    <li>
    <a href="https://www.seriouseats.com/stovetop-guides-5118016" rel="nocaes" class="global-nav__sub-list-item">Stovetop Guides</a>
    </li>
    <li>
    <a href="https://www.seriouseats.com/baking-guides-5118031" rel="nocaes" class="global-nav__sub-list-item">Baking Guides</a>
    </li>
    <li>
    <a href="https://www.seriouseats.com/entertaining-5118033" rel="nocaes" class="global-nav__sub-list-item">Entertaining</a>
    </li>
    <li>
    <a href="https://www.seriouseats.com/the-food-lab-5118015" rel="nocaes" class="global-nav__sub-list-item">The Food Lab</a>
    </li>
    <li>
    <a href="https://www.seriouseats.com/how-tos-5118034" rel="nocaes" class="global-nav__sub-list-item global-nav__see-all"> View all
    </a> </li>
    </ul>
    </li>
    <li class="global-nav__list-item js-global-nav-item is-disabled">
    <a href="https://www.seriouseats.com/world-cuisine-guides-5117177" rel="nocaes" class="global-nav__list-item-link"> <span>World Cuisines</span>
    </a>
    <svg class="icon icon-navigation-arrow global-nav__list-item-icon">
    <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#icon-navigation-arrow" href="#icon-navigation-arrow"></use>
    </svg>
    <ul class="global-nav__sub-list">
    <li>
    <a href="https://www.seriouseats.com/african-cuisine-guides-5117176" rel="nocaes" class="global-nav__sub-list-item">African Cuisines</a>
    </li>
    <li>
    <a href="https://www.seriouseats.com/asian-cuisine-guides-5117164" rel="nocaes" class="global-nav__sub-list-item">Asian Cuisines</a>
    </li>
    <li>
    <a href="https://www.seriouseats.com/caribbean-cuisine-guides-5117113" rel="nocaes" class="global-nav__sub-list-item">Caribbean Cuisines</a>
    </li>
    <li>
    <a href="https://www.seriouseats.com/central-american-cuisine-guides-5117136" rel="nocaes" class="global-nav__sub-list-item">Central American Cuisines</a>
    </li>
    <li>
    <a href="https://www.seriouseats.com/european-cuisine-guides-5117108" rel="nocaes" class="global-nav__sub-list-item">European Cuisines</a>
    </li>
    <li>
    <a href="https://www.seriouseats.com/middle-eastern-cuisine-guides-5117157" rel="nocaes" class="global-nav__sub-list-item">Middle Eastern Cuisines</a>
    </li>
    <li>
    <a href="https://www.seriouseats.com/north-american-cuisine-guides-5117134" rel="nocaes" class="global-nav__sub-list-item">North American Cuisine Guides</a>
    </li>
    <li>
    <a href="https://www.seriouseats.com/oceanic-cuisine-guides-5117084" rel="nocaes" class="global-nav__sub-list-item">Oceanic Cuisines</a>
    </li>
    <li>
    <a href="https://www.seriouseats.com/south-american-cuisine-guides-5117118" rel="nocaes" class="global-nav__sub-list-item">South American Cuisines</a>
    </li>
    <li>
    <a href="https://www.seriouseats.com/world-cuisine-guides-5117177" rel="nocaes" class="global-nav__sub-list-item global-nav__see-all"> View all
    </a> </li>
    </ul>
    </li>
    <li class="global-nav__list-item js-global-nav-item is-disabled">
    <a href="https://www.seriouseats.com/ingredients-guides-5118013" rel="nocaes" class="global-nav__list-item-link"> <span>Ingredients</span>
    </a>
    <svg class="icon icon-navigation-arrow global-nav__list-item-icon">
    <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#icon-navigation-arrow" href="#icon-navigation-arrow"></use>
    </svg>
    <ul class="global-nav__sub-list">
    <li>
    <a href="https://www.seriouseats.com/chicken-guides-5118008" rel="nocaes" class="global-nav__sub-list-item">Chicken Guides</a>
    </li>
    <li>
    <a href="https://www.seriouseats.com/drink-guides-5181286" rel="nocaes" class="global-nav__sub-list-item">Drink Guides</a>
    </li>
    <li>
    <a href="https://www.seriouseats.com/egg-guides-5118005" rel="nocaes" class="global-nav__sub-list-item">Egg Guides</a>
    </li>
    <li>
    <a href="https://www.seriouseats.com/fruit-guides-5118003" rel="nocaes" class="global-nav__sub-list-item">Fruit Guides</a>
    </li>
    <li>
    <a href="https://www.seriouseats.com/noodle-guides-5117999" rel="nocaes" class="global-nav__sub-list-item">Noodle Guides</a>
    </li>
    <li>
    <a href="https://www.seriouseats.com/pantry-guides-5181287" rel="nocaes" class="global-nav__sub-list-item">Pantry Guides</a>
    </li>
    <li>
    <a href="https://www.seriouseats.com/pork-guides-5117995" rel="nocaes" class="global-nav__sub-list-item">Pork Guides</a>
    </li>
    <li>
    <a href="https://www.seriouseats.com/rice-grain-guides-5117992" rel="nocaes" class="global-nav__sub-list-item">Rice &amp; Grain Guides</a>
    </li>
    <li>
    <a href="https://www.seriouseats.com/seafood-guides-5117991" rel="nocaes" class="global-nav__sub-list-item">Seafood Guides</a>
    </li>
    <li>
    <a href="https://www.seriouseats.com/vegetable-guides-5117987" rel="nocaes" class="global-nav__sub-list-item">Vegetable Guides</a>
    </li>
    <li>
    <a href="https://www.seriouseats.com/taste-tests-5117986" rel="nocaes" class="global-nav__sub-list-item">Taste Tests</a>
    </li>
    <li>
    <a href="https://www.seriouseats.com/ingredients-guides-5118013" rel="nocaes" class="global-nav__sub-list-item global-nav__see-all"> View all
    </a> </li>
    </ul>
    </li>
    <li class="global-nav__list-item js-global-nav-item is-disabled">
    <a href="https://www.seriouseats.com/equipment-5117081" rel="nocaes" class="global-nav__list-item-link"> <span>Equipment</span>
    </a>
    <svg class="icon icon-navigation-arrow global-nav__list-item-icon">
    <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#icon-navigation-arrow" href="#icon-navigation-arrow"></use>
    </svg>
    <ul class="global-nav__sub-list">
    <li>
    <a href="https://www.seriouseats.com/coffee-tea-5118050" rel="nocaes" class="global-nav__sub-list-item">Coffee &amp; Tea</a>
    </li>
    <li>
    <a href="https://www.seriouseats.com/cookware-5118049" rel="nocaes" class="global-nav__sub-list-item">Cookware</a>
    </li>
    <li>
    <a href="https://www.seriouseats.com/knives-5118044" rel="nocaes" class="global-nav__sub-list-item">Knives</a>
    </li>
    <li>
    <a href="https://www.seriouseats.com/tools-gadgets-5118041" rel="nocaes" class="global-nav__sub-list-item">Tools &amp; Gadgets</a>
    </li>
    <li>
    <a href="https://www.seriouseats.com/grilling-outdoor-5118046" rel="nocaes" class="global-nav__sub-list-item">Grilling &amp; Outdoor</a>
    </li>
    <li>
    <a href="https://www.seriouseats.com/electric-appliances-5118048" rel="nocaes" class="global-nav__sub-list-item">Electric Appliances</a>
    </li>
    <li>
    <a href="https://www.seriouseats.com/seriously-good-kitchen-gear-7255050" rel="nocaes" class="global-nav__sub-list-item">Seriously Good Gear</a>
    </li>
    <li>
    <a href="https://www.seriouseats.com/bakeware-5118053" rel="nocaes" class="global-nav__sub-list-item">Bakeware</a>
    </li>
    <li>
    <a href="https://www.seriouseats.com/kitchen-basics-5118045" rel="nocaes" class="global-nav__sub-list-item">Kitchen Basics</a>
    </li>
    <li>
    <a href="https://www.seriouseats.com/storage-organization-5118043" rel="nocaes" class="global-nav__sub-list-item">Storage &amp; Organization</a>
    </li>
    <li>
    <a href="https://www.seriouseats.com/barware-5118052" rel="nocaes" class="global-nav__sub-list-item">Barware</a>
    </li>
    <li>
    <a href="https://www.seriouseats.com/tableware-5118042" rel="nocaes" class="global-nav__sub-list-item">Tableware</a>
    </li>
    <li>
    <a href="https://www.seriouseats.com/books-5118051" rel="nocaes" class="global-nav__sub-list-item">Books</a>
    </li>
    <li>
    <a href="https://www.seriouseats.com/gifts-5118047" rel="nocaes" class="global-nav__sub-list-item">Seasonal &amp; Gifts</a>
    </li>
    <li>
    <a href="https://www.seriouseats.com/equipment-5117081" rel="nocaes" class="global-nav__sub-list-item global-nav__see-all"> View all
    </a> </li>
    </ul>
    </li>
    <li class="global-nav__list-item js-global-nav-item is-disabled">
    <a href="https://www.seriouseats.com/features-5118040" rel="nocaes" class="global-nav__list-item-link"> <span>Features</span>
    </a>
    <svg class="icon icon-navigation-arrow global-nav__list-item-icon">
    <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#icon-navigation-arrow" href="#icon-navigation-arrow"></use>
    </svg>
    <ul class="global-nav__sub-list">
    <li>
    <a href="https://www.seriouseats.com/dining-out-5118039" rel="nocaes" class="global-nav__sub-list-item">Dining Out</a>
    </li>
    <li>
    <a href="https://www.seriouseats.com/food-history-5118037" rel="nocaes" class="global-nav__sub-list-item">Food History</a>
    </li>
    <li>
    <a href="https://www.seriouseats.com/food-industry-5118038" rel="nocaes" class="global-nav__sub-list-item">Food Industry</a>
    </li>
    <li>
    <a href="https://www.seriouseats.com/food-science-5118036" rel="nocaes" class="global-nav__sub-list-item">Food Science</a>
    </li>
    <li>
    <a href="https://www.seriouseats.com/personal-essays-5118035" rel="nocaes" class="global-nav__sub-list-item">Personal Essays</a>
    </li>
    <li>
    <a href="https://www.seriouseats.com/features-5118040" rel="nocaes" class="global-nav__sub-list-item global-nav__see-all"> View all
    </a> </li>
    </ul>
    </li>
    <li class="global-nav__list-item js-global-nav-item nav-about-us is-disabled">
    <a href="https://www.seriouseats.com/about-us-5120006" rel="nocaes" class="global-nav__list-item-link"> <span>About Us</span>
    </a> </li>
    <li id="nav-sign-up--desktop_1-0" class="comp nav-sign-up--desktop button-nav-item global-nav__list-item"> <span class="global-nav__list-item-link button-nav-item__activator js-toggle-btn-nav" tabindex="0">
    <div id="nav-newsletter-btn_1-0" class="comp nav-newsletter-btn mntl-block"><span id="mntl-text-block_1-0" class="comp mntl-text-block">Newsletter</span></div> </span>
    <ul class="button-nav-item__content is-hidden">
    <div id="newsletter--nav-form_1-0" class="comp newsletter--nav-form newsletter--title-form mntl-block"><form id="newsletter__form_2-0" class="comp newsletter__form newsletter-signup mntl-newsletter-signup" data-tracking-container="true"><div id="mntl-newsletter-submit_2-0" class="comp mntl-newsletter-submit mntl-newsletter-submit__input-wrapper" data-reg-source-id="zs4vcu" data-newsletter-object-ids="j8zzd5" data-shared-services="true"> <input type="hidden" name="objectorObjectIds" value="relationship-builder,acquisition">
    <label class="mntl-newsletter-submit__label is-vishidden" for="mntl-newsletter-submit_2-0__input">Email Address</label>
    <input class="mntl-newsletter-submit__input" id="mntl-newsletter-submit_2-0__input" type="email" name="email" placeholder="Enter email" required="" pattern="\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,4}\b">
    <button id="mntl-newsletter-submit__button_2-0" class="comp mntl-newsletter-submit__button mntl-button newsletter-signup__button btn" type="submit" aria-label="Sign Up">
    <svg class="icon icon-circle-arrow mntl-button__icon ">
    <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#icon-circle-arrow" href="#icon-circle-arrow"></use>
    </svg>
    </button></div>
    <div id="newsletter-signup__success-container_2-0" class="comp newsletter-signup__success-container mntl-block"><svg class="icon newsletter-success-image newsletter-signup__success-image">
    <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#newsletter-success-image" href="#newsletter-success-image"></use>
    </svg></div><div id="mntl-newsletter-signup__error_2-0" class="comp mntl-newsletter-signup__error mntl-text-block">There was an error. Please try again.</div></form>
    <div id="newsletter-message--success_1-0" class="comp newsletter-message--success mntl-block is-hidden"><div id="mntl-text-block_2-0" class="comp mntl-text-block">Congrats, you’re signed up!</div></div></div> </ul>
    </li>
    <li id="nav-search-bar_1-0" class="comp nav-search-bar button-nav-item global-nav__list-item"> <span class="global-nav__list-item-link button-nav-item__activator js-toggle-btn-nav" tabindex="0">
    <div id="svg-hover-container_1-0" class="comp svg-hover-container mntl-block" aria-label="Search"><svg class="icon icon-search general-search__icon btn__icon">
    <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#icon-search" href="#icon-search"></use>
    </svg></div> </span>
    <ul class="button-nav-item__content is-hidden">
    <form id="nav-search_1-0" class="comp nav-search" role="search" method="get" action="/search" data-tracking-container="true"> <input class="nav-search__input js-nav-search-input" type="search" name="q" value="" placeholder="Search for recipes &amp; articles..." autocomplete="on" required="">
    <button type="submit" class="nav-btn-submit js-submit-nav-search" aria-label="Submit search form">
    <svg class="icon icon-circle-arrow nav-btn-submit__icon">
    <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#icon-circle-arrow" href="#icon-circle-arrow"></use>
    </svg>
    </button>
    </form> </ul>
    </li>
    <li id="relish-save-header_1-0" class="comp relish-save-header mntl-relish-saved-recipes-btn mntl-button global-nav__list-item nav-save-recipe" data-tracking-container="true">
    <div id="relish-save-header__content_1-0" class="comp relish-save-header__content mntl-block global-nav__list-item-link" role="button" tabindex="0" aria-label="Saved Recipes"><div id="relish-save-header__inner_1-0" class="comp relish-save-header__inner mntl-block nav-relish"><div id="svg-hover-container_2-0" class="comp svg-hover-container mntl-block"><svg class="icon icon-save-recipe global-nav__list-item-icon">
    <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#icon-save-recipe" href="#icon-save-recipe"></use>
    </svg></div></div></div></li>
    <li class="global-nav__list-item nav-sign-up">
    <div id="nav-sign-up_1-0" class="comp nav-sign-up newsletter--title-form mntl-block"><div id="newsletter__wrapper_1-0" class="comp newsletter__wrapper mntl-block"><span id="newsletter__title_1-0" class="comp newsletter__title text-block">
    </span>
    <span id="newsletter__subtitle_1-0" class="comp newsletter__subtitle text-block" tabindex="0"> Get fresh recipes, cooking tips, deal alerts, and more!
    </span></div>
    <form id="newsletter__form_1-0" class="comp newsletter__form newsletter-signup mntl-newsletter-signup" data-tracking-container="true"><div id="mntl-newsletter-submit_1-0" class="comp mntl-newsletter-submit mntl-newsletter-submit__input-wrapper" data-reg-source-id="zs4vcu" data-newsletter-object-ids="j8zzd5" data-shared-services="true"> <input type="hidden" name="objectorObjectIds" value="relationship-builder,acquisition">
    <label class="mntl-newsletter-submit__label is-vishidden" for="mntl-newsletter-submit_1-0__input">Email Address</label>
    <input class="mntl-newsletter-submit__input" id="mntl-newsletter-submit_1-0__input" type="email" name="email" placeholder="Enter email" required="" pattern="\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,4}\b">
    <button id="mntl-newsletter-submit__button_1-0" class="comp mntl-newsletter-submit__button mntl-button newsletter-signup__button btn" type="submit" aria-label="Sign Up">
    <svg class="icon icon-circle-arrow mntl-button__icon ">
    <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#icon-circle-arrow" href="#icon-circle-arrow"></use>
    </svg>
    </button></div>
    <div id="newsletter-signup__success-container_1-0" class="comp newsletter-signup__success-container mntl-block"><svg class="icon newsletter-success-image newsletter-signup__success-image">
    <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#newsletter-success-image" href="#newsletter-success-image"></use>
    </svg></div><div id="mntl-newsletter-signup__error_1-0" class="comp mntl-newsletter-signup__error mntl-text-block">There was an error. Please try again.</div></form></div> </li>
    <li class="global-nav__footer-wrapper">
    <ul class="global-nav__footer">
    <li class="global-nav__footer-item">
    <a href="https://www.seriouseats.com/about-us-5120006" rel="nocaes" class="global-nav__footer-link">About Us</a>
    </li>
    <li class="global-nav__footer-item">
    <a href="https://www.seriouseats.com/about-us-5120006#toc-contact-us" rel="nocaes" class="global-nav__footer-link">Contact Us</a>
    </li>
    </ul>
    </li>
    </ul>
    </nav> </div>
    </div>
    </div>
    </header>
  
    <div id="mm-ads-leaderboard-spacer_1-0" class="comp mm-ads-leaderboard-spacer mntl-block js-rollaway-spacer"></div><main id="main" class="loc main" role="main"><iframe id="height-change-listener" role="none" tabindex="-1" src="about:blank" aria-hidden="true"></iframe>
    <div id="skip-to-content" class="jump-link"></div>

     <!-- NLWeb Dropdown Chat Container -->
     <div id="ai-search-container" style="width: 90%; max-width: 800px; margin: 20px auto;"></div>

     <script type="module">
          import { NLWebDropdownChat } from '../nlweb-dropdown-chat.js';
          
          document.addEventListener('DOMContentLoaded', () => {
              // Initialize the dropdown chat
              const chat = new NLWebDropdownChat({
                  containerId: 'ai-search-container',
                  site: 'seriouseats',
                  placeholder: 'Find the recipe you are looking for...',
                  endpoint: window.location.origin
              });
          });
     </script>


     

    
     <div id="homepage-showcase_1-0" class="comp homepage-showcase mntl-block"><div id="eat-bar_1-0" class="comp eat-bar"> <div class="eat-bar__primary"> 
    <span class="eat-bar__primary-text--first">Good cooks know how.</span>
    <span class="eat-bar__primary-text--second">Great cooks know why.</span>
    </div>
    <div class="eat-bar__secondary">
    <span class="eat-bar__secondary-text">Founded in 2006, Serving 8 Million Food Nerds a Month</span>
    </div>
    </div>
    <a id="showcase__hero_1-0" class="comp showcase__hero showcase-hero" href="https://www.seriouseats.com/labneh-tuna-salad-recipe-8765207">
    <div class="loc showcase-hero__image"><figure id="showcase-hero__image_1-0" class="comp showcase-hero__image mntl-universal-primary-image card__image primary-image"> <div class="primary-image__media">
    <img src="https://www.seriouseats.com/thmb/7ZOl9J99S1VpSFqsoy4SG21Fr3I=/724x0/filters:no_upscale():max_bytes(150000):strip_icc():format(webp)/20241219-SEA-LabneTunaSalad-AmandaSuarez-18-563b5a27cc804cc9b306b86efe0308c4.jpg" width="4000" height="3000" srcset="https://www.seriouseats.com/thmb/3pQHOxS8lGT3EgkPBN6jJjUPkrE=/750x0/filters:no_upscale():max_bytes(150000):strip_icc():format(webp)/20241219-SEA-LabneTunaSalad-AmandaSuarez-18-563b5a27cc804cc9b306b86efe0308c4.jpg 750w" sizes="750px" alt="Side view of labne tuna salad" class=" primary-image__image" onload="" style="">
    <noscript>
    <img
    src="https://www.seriouseats.com/thmb/7ZOl9J99S1VpSFqsoy4SG21Fr3I=/724x0/filters:no_upscale():max_bytes(150000):strip_icc():format(webp)/20241219-SEA-LabneTunaSalad-AmandaSuarez-18-563b5a27cc804cc9b306b86efe0308c4.jpg"
    width="4000"
    height="3000"
    class="loaded primary-img--noscript primary-image__image"
    srcset="https://www.seriouseats.com/thmb/3pQHOxS8lGT3EgkPBN6jJjUPkrE=/750x0/filters:no_upscale():max_bytes(150000):strip_icc():format(webp)/20241219-SEA-LabneTunaSalad-AmandaSuarez-18-563b5a27cc804cc9b306b86efe0308c4.jpg 750w"
    sizes="750px"
    alt="Side view of labne tuna salad"
    />
    </noscript> </div>
    </figure>
    </div>
    <div class="loc showcase-hero__taxonomy-bar"><div class="taxonomy-bar">
    <div class="taxonomy-bar--text" data-taxonomy="Tuna"></div>
    <div class="taxonomy-bar--text" data-taxonomy="Tuna"></div>
    <div class="taxonomy-bar--text" data-taxonomy="Tuna"></div>
    <div class="taxonomy-bar--text" data-taxonomy="Tuna"></div>
    <div class="taxonomy-bar--text" data-taxonomy="Tuna"></div>
    <div class="taxonomy-bar--text" data-taxonomy="Tuna"></div>
    <div class="taxonomy-bar--text" data-taxonomy="Tuna"></div>
    <div class="taxonomy-bar--text" data-taxonomy="Tuna"></div>
    <div class="taxonomy-bar--text" data-taxonomy="Tuna"></div>
    <div class="taxonomy-bar--text" data-taxonomy="Tuna"></div>
    <div class="taxonomy-bar--text" data-taxonomy="Tuna"></div>
    <div class="taxonomy-bar--text" data-taxonomy="Tuna"></div>
    </div>
    </div>
    <div class="showcase-hero__content">
    <span class="showcase-hero__title">
    <span class="showcase-hero__title--underline">
    Labneh Tuna Salad
    </span>
    </span>
    <p class="showcase-hero__description">This tuna salad recipe uses labneh instead of mayonnaise and incorporates ingredients like quick-pickled onions, carrots, and dill to enhance the flavor.</p>
    <div class="showcase-hero__author" data-byline-author="By Yasmine Maggio"></div>
    <svg class="icon arrow-circle-right section__title-icon">
    <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#arrow-circle-right" href="#arrow-circle-right"></use>
    </svg> </div>
    </a></div>
    <section id="homepage-island--latest_1-0" class="comp homepage-island--latest homepage-island section--island section l-container"> <header class="section__header l-page-padding"><div class="loc section-header"><a href="https://www.seriouseats.com/latest-5117998" rel="nocaes" id="section__title_1-0" class="text-link section__title section__title--link" data-tracking-container="true"> <span class="link__wrapper"></span> <svg class="icon arrow-circle-right section__title-icon"> <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#arrow-circle-right" href="#arrow-circle-right"></use> </svg></a> <div id="section-title_1-0" class="comp section-title mntl-block"><span id="section-title-main_1-0" class="comp section-title-main mntl-text-block">The Latest</span></div> </div></header>
    <div class="loc section-content section__content "><div id="card-list_1-0" class="comp card-list mntl-block l-container" data-tracking-container="true"><div id="card-list__item_1-0" class="comp card-list__item mntl-block"><a id="card_1-0" class="comp card" href="https://www.seriouseats.com/butternut-squash-and-kale-farrotto-recipe-8764505">
    <div class="loc card__header"><div class="card__media mntl-universal-image universal-image__container"><img data-src="https://www.seriouseats.com/thmb/0py0S6rPK2oKprZ2wQkZ1akPBq8=/405x270/filters:no_upscale():max_bytes(150000):strip_icc():format(webp)/20241220-SEA-ButternutSquashFarrotto-AmandaSuarez-00-d4c2fb77607444a39b0804a2ffb6597d.jpg" width="405" height="270" alt="Overhead view of butternut squash farrotto" class="card__image universal-image__image lazyloadwait lazyloaded" src="https://www.seriouseats.com/thmb/0py0S6rPK2oKprZ2wQkZ1akPBq8=/405x270/filters:no_upscale():max_bytes(150000):strip_icc():format(webp)/20241220-SEA-ButternutSquashFarrotto-AmandaSuarez-00-d4c2fb77607444a39b0804a2ffb6597d.jpg">
    <noscript>
    <img
    src="https://www.seriouseats.com/thmb/0py0S6rPK2oKprZ2wQkZ1akPBq8=/405x270/filters:no_upscale():max_bytes(150000):strip_icc():format(webp)/20241220-SEA-ButternutSquashFarrotto-AmandaSuarez-00-d4c2fb77607444a39b0804a2ffb6597d.jpg"
    width="405"
    height="270"
    class="img--noscript card__image universal-image__image"
    alt="Overhead view of butternut squash farrotto"
    />
    </noscript></div>
    </div> <div class="card__wrapper">
    <div class="card__content">
    <div class="card__tag" data-tax-tag="Rice &amp; Grain Mains" data-updated="12/22/24"></div>
    <span class="card__title">
    <span class="card__underline">
    Butternut Squash and Kale Farrotto
    </span>
    </span>
    <div id="card__author-name_1-0" class="comp card__author-name mntl-block" data-byline-author="By Daniel Gritzer"></div> </div>
    </div>
    </a></div>
    <div id="card-list__item_2-0" class="comp card-list__item mntl-block"><a id="card_2-0" class="comp card" href="https://www.seriouseats.com/cheeseburger-pizza-recipe-8757382">
    <div class="loc card__header"><div class="card__media mntl-universal-image universal-image__container"><img data-src="https://www.seriouseats.com/thmb/Fitur-_PGCRMsNRxHGoyiaNtLww=/405x270/filters:no_upscale():max_bytes(150000):strip_icc():format(webp)/20241101-SEA-DeliStudios-CheeseburgerburgerPizza-Hero2-18-0610da93ee234e21a76d7164392684f7.jpg" width="405" height="270" alt="Slice of cheeseburger pizza on a blue plate. Rest of cheeseburger pizza on graphic background " class="card__image universal-image__image lazyloadwait lazyloaded" src="https://www.seriouseats.com/thmb/Fitur-_PGCRMsNRxHGoyiaNtLww=/405x270/filters:no_upscale():max_bytes(150000):strip_icc():format(webp)/20241101-SEA-DeliStudios-CheeseburgerburgerPizza-Hero2-18-0610da93ee234e21a76d7164392684f7.jpg">
    <noscript>
    <img
    src="https://www.seriouseats.com/thmb/Fitur-_PGCRMsNRxHGoyiaNtLww=/405x270/filters:no_upscale():max_bytes(150000):strip_icc():format(webp)/20241101-SEA-DeliStudios-CheeseburgerburgerPizza-Hero2-18-0610da93ee234e21a76d7164392684f7.jpg"
    width="405"
    height="270"
    class="img--noscript card__image universal-image__image"
    alt="Slice of cheeseburger pizza on a blue plate. Rest of cheeseburger pizza on graphic background "
    />
    </noscript></div>
    </div> <div class="card__wrapper">
    <div class="card__content">
    <div class="card__tag" data-tax-tag="Pizza" data-updated="12/06/24"></div>
    <span class="card__title">
    <span class="card__underline">
    Cheeseburger Pizza
    </span>
    </span>
    <div id="card__author-name_2-0" class="comp card__author-name mntl-block" data-byline-author="By Molly Allen"></div> </div>
    </div>
    </a></div>
    <div id="card-list__item_3-0" class="comp card-list__item mntl-block"><a id="card_3-0" class="comp card" href="https://www.seriouseats.com/le-creuset-new-year-sale-2024-8762399">
    <div class="loc card__header"><div class="card__media mntl-universal-image universal-image__container"><img data-src="https://www.seriouseats.com/thmb/9kBGGaWVhZq-yJB65jbgZVOC4bo=/405x270/filters:no_upscale():max_bytes(150000):strip_icc():format(webp)/tested-favorites-from-le-creuset-ftt-sale-tout-ccb4103f8230418e90c632121d2a49f4.jpg" width="405" height="270" alt="Tested Favorites from Le Creuset FTT Sale tout" class="card__image universal-image__image lazyloadwait ls-is-cached lazyloaded" src="https://www.seriouseats.com/thmb/9kBGGaWVhZq-yJB65jbgZVOC4bo=/405x270/filters:no_upscale():max_bytes(150000):strip_icc():format(webp)/tested-favorites-from-le-creuset-ftt-sale-tout-ccb4103f8230418e90c632121d2a49f4.jpg">
    <noscript>
    <img
    src="https://www.seriouseats.com/thmb/9kBGGaWVhZq-yJB65jbgZVOC4bo=/405x270/filters:no_upscale():max_bytes(150000):strip_icc():format(webp)/tested-favorites-from-le-creuset-ftt-sale-tout-ccb4103f8230418e90c632121d2a49f4.jpg"
    width="405"
    height="270"
    class="img--noscript card__image universal-image__image"
    alt="Tested Favorites from Le Creuset FTT Sale tout"
    />
    </noscript></div>
    </div> <div class="card__wrapper">
    <div class="card__content">
    <div class="card__tag" data-tax-tag="Equipment" data-updated="01/01/25"></div>
    <span class="card__title">
    <span class="card__underline">
    Le Creuset Is Having a Huge Sale—Here’s What’s Worth Buying
    </span>
    </span>
    <div id="card__author-name_3-0" class="comp card__author-name mntl-block" data-byline-author="By Rochelle Bilow"></div> </div>
    </div>
    </a></div>
    <div id="card-list__item_4-0" class="comp card-list__item mntl-block"><a id="card_4-0" class="comp card" href="https://www.seriouseats.com/winter-salad-recipes-8759237">
    <div class="loc card__header"><div class="card__media mntl-universal-image universal-image__container"><img data-src="https://www.seriouseats.com/thmb/ekq6e144lOdIC_0_85dynpWYIzU=/405x270/filters:no_upscale():max_bytes(150000):strip_icc():format(webp)/warm-kale-caramelized-mushroom-salad-recipe-hero-02_1-3fba9027280a4b27a103ff8bbcf2353b.JPG" width="405" height="270" alt="Warm kale and caramelized mushroom salad in a white ceramic bowl on a green surface. There are a pair of wooden spoons at the bottom right corner, a glass of golden liquid in the top left corner, a stack of plates covered with a napkin in the bottom left corner, and another plate of salad in the top right corner." class="card__image universal-image__image lazyloadwait lazyloaded" src="https://www.seriouseats.com/thmb/ekq6e144lOdIC_0_85dynpWYIzU=/405x270/filters:no_upscale():max_bytes(150000):strip_icc():format(webp)/warm-kale-caramelized-mushroom-salad-recipe-hero-02_1-3fba9027280a4b27a103ff8bbcf2353b.JPG">
    <noscript>
    <img
    src="https://www.seriouseats.com/thmb/ekq6e144lOdIC_0_85dynpWYIzU=/405x270/filters:no_upscale():max_bytes(150000):strip_icc():format(webp)/warm-kale-caramelized-mushroom-salad-recipe-hero-02_1-3fba9027280a4b27a103ff8bbcf2353b.JPG"
    width="405"
    height="270"
    class="img--noscript card__image universal-image__image"
    alt="Warm kale and caramelized mushroom salad in a white ceramic bowl on a green surface. There are a pair of wooden spoons at the bottom right corner, a glass of golden liquid in the top left corner, a stack of plates covered with a napkin in the bottom left corner, and another plate of salad in the top right corner."
    />
    </noscript></div>
    </div> <div class="card__wrapper">
    <div class="card__content">
    <div class="card__tag" data-tax-tag="Winter Salads" data-updated="12/11/24"></div>
    <span class="card__title">
    <span class="card__underline">
    14 Bright Winter Salad Recipes
    </span>
    </span>
    <div id="card__author-name_4-0" class="comp card__author-name mntl-block" data-byline-author="By Rabi Abonour and The Serious Eats Team"></div> </div>
    </div>
    </a></div></div>
    </div>
    </section>
    <div id="mm-ads-leaderboard-fixed-lazy_1-0" class="comp mm-ads-leaderboard-fixed-lazy mm-ads-leaderboard-fixed mm-ads-gpt-adunit has-right-label leaderboard js-lazy-ad gpt leaderboard dynamic">
    <div id="leaderboard-fixed-1" class="wrapper gpt-ignore-incrementor" data-type="leaderboard" data-pos="atf" data-priority="99" data-sizes="[[728, 90]]" data-rtb="true" data-wait-for-third-party="false" data-targeting="{}"></div>
    </div>
    <div id="homepage-editors_1-0" class="comp homepage-editors mntl-block homepage-island section--island l-container"><div id="homepage-editors__wrapper_1-0" class="comp homepage-editors__wrapper mntl-block"><section id="homepage-editors-information__wrapper_1-0" class="comp homepage-editors-information__wrapper section l-page-padding"> <header class="section__header "><h2 class="section__title " data-title-tag="">Who We Are</h2></header>
    <div class="loc section-content section__content "><p id="homepage-editors-information__description_1-0" class="comp homepage-editors-information__description mntl-text-block">We are the curious cooks, experts, journalists, and nerds behind Serious Eats, the foremost site of food science and culture since 2006.</p>
    <div id="homepage-editors-information__read-more_1-0" class="comp homepage-editors-information__read-more mntl-block"><a href="https://www.seriouseats.com/about-us-5120006" rel="nocaes" id="homepage-editors-information__read-more-link_1-0" class="homepage-editors-information__read-more-link mntl-text-link " data-tracking-container="true"><span class="link__wrapper">More About Us</span>
    <svg class="icon arrow-circle-right section__title-icon">
    <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#arrow-circle-right" href="#arrow-circle-right"></use>
    </svg></a></div>
    </div>
    </section>
    <section id="homepage-editors-carousel__wrapper_1-0" class="comp homepage-editors-carousel__wrapper section l-container section--meet-the-team section--island island-list--home homepage__island-list"> <header class="section__header "><h2 class="section__title " data-title-tag="">Who We Actually Are</h2></header>
    <div class="loc section-content section__content "><div id="editors-list_1-0" class="comp editors-list editors-carousel mntl-carousel mntl-carousel" data-use-placeholders="false" data-scroll-by-page="false" data-update-arrows-when-scrolling="false">
    <div class="mntl-carousel__wrapper">
    <div class="mntl-carousel__arrow mntl-carousel__arrow--left" data-click-tracked="true" role="button" aria-label="Present previous article" disabled="" tabindex="-1" aria-disabled="true">
    <svg class="icon mntl-carousel__arrow-icon ">
    <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#mntl-carousel__arrow-icon" href="#mntl-carousel__arrow-icon"></use>
    </svg>
    </div>
    <ul class="loc carousel-items mntl-carousel__items js-animatable"><div id="mntl-carousel__item_1-0" class="comp mntl-carousel__item mntl-block" data-ordinal="0" style="transform: translateX(0px);"><div id="mntl-carousel__wrapper-item_1-0" class="comp mntl-carousel__wrapper-item mntl-block" data-ordinal="0"><div id="editor-image-block_1-0" class="comp editor-image-block"><a href="https://www.seriouseats.com/daniel-gritzer-5118638" rel="nocaes" class="editor__bio-wrapper"><div class="loc editor__image"><img data-src="https://www.seriouseats.com/thmb/_EpBNNpFz6Sgc3_4bF5z240BjlE=/90x90/filters:no_upscale():max_bytes(150000):strip_icc():format(webp)/daniel-gritzer-095af850d50f4cd593487117353243fd.jpg" width="90" height="90" alt="Daniel Gritzer" class=" universal-image__image lazyloadwait">
    <noscript>
    <img
    src="https://www.seriouseats.com/thmb/_EpBNNpFz6Sgc3_4bF5z240BjlE=/90x90/filters:no_upscale():max_bytes(150000):strip_icc():format(webp)/daniel-gritzer-095af850d50f4cd593487117353243fd.jpg"
    width="90"
    height="90"
    class="img--noscript universal-image__image"
    alt="Daniel Gritzer"
    />
    </noscript>
    </div>
    <div class="editor__details">
    <span class="editor__name">
    Daniel Gritzer
    </span>
    <h3 class="editor__title">
    Editorial Director
    </h3>
    <h3 class="editor__title">
    Human Garbage Can
    </h3>
    </div>
    </a></div></div>
    <div id="mntl-carousel__wrapper-item_2-0" class="comp mntl-carousel__wrapper-item mntl-block" data-ordinal="0"><div id="editor-image-block_2-0" class="comp editor-image-block"><a href="https://www.seriouseats.com/tess-koman-5210571" rel="nocaes" class="editor__bio-wrapper"><div class="loc editor__image"><img data-src="https://www.seriouseats.com/thmb/ewLsmP1a1zTwi6QQeQNv8wIOfyI=/90x90/filters:no_upscale():max_bytes(150000):strip_icc():format(webp)/SE_Tess_Koman_color_Headshot-1-cd6bfb45e8bf490a88d1f7c50cc5e0e0.jpg" width="90" height="90" alt="Tess Koman headshot." class=" universal-image__image lazyloadwait">
    <noscript>
    <img
    src="https://www.seriouseats.com/thmb/ewLsmP1a1zTwi6QQeQNv8wIOfyI=/90x90/filters:no_upscale():max_bytes(150000):strip_icc():format(webp)/SE_Tess_Koman_color_Headshot-1-cd6bfb45e8bf490a88d1f7c50cc5e0e0.jpg"
    width="90"
    height="90"
    class="img--noscript universal-image__image"
    alt="Tess Koman headshot."
    />
    </noscript>
    </div>
    <div class="editor__details">
    <span class="editor__name">
    Tess Koman
    </span>
    <h3 class="editor__title">
    Executive Editorial Director
    </h3>
    <h3 class="editor__title">
    Expertise Unclear
    </h3>
    </div>
    </a></div></div>
    <div id="mntl-carousel__wrapper-item_3-0" class="comp mntl-carousel__wrapper-item mntl-block" data-ordinal="0"><div id="editor-image-block_3-0" class="comp editor-image-block"><a href="https://www.seriouseats.com/megan-o-steintrager-7371874" rel="nocaes" class="editor__bio-wrapper"><div class="loc editor__image"><img data-src="https://www.seriouseats.com/thmb/LTrB6gFCjnI6BCItOjaml9TK0Ak=/90x90/filters:no_upscale():max_bytes(150000):strip_icc():format(webp)/megan-steintrager-headshot-600c7ec12c764af4a0b8cf1d0d1e3994.jpg" width="90" height="90" alt="Megan O. Steintrager" class=" universal-image__image lazyloadwait">
    <noscript>
    <img
    src="https://www.seriouseats.com/thmb/LTrB6gFCjnI6BCItOjaml9TK0Ak=/90x90/filters:no_upscale():max_bytes(150000):strip_icc():format(webp)/megan-steintrager-headshot-600c7ec12c764af4a0b8cf1d0d1e3994.jpg"
    width="90"
    height="90"
    class="img--noscript universal-image__image"
    alt="Megan O. Steintrager"
    />
    </noscript>
    </div>
    <div class="editor__details">
    <span class="editor__name">
    Megan O. Steintrager
    </span>
    <h3 class="editor__title">
    Associate Editorial Director
    </h3>
    <h3 class="editor__title">
    Loves All Vegetables
    </h3>
    </div>
    </a></div></div>
    <div id="mntl-carousel__wrapper-item_4-0" class="comp mntl-carousel__wrapper-item mntl-block" data-ordinal="0"><div id="editor-image-block_4-0" class="comp editor-image-block"><a href="https://www.seriouseats.com/amanda-suarez-5221998" rel="nocaes" class="editor__bio-wrapper"><div class="loc editor__image"><img data-src="https://www.seriouseats.com/thmb/1hl6pe1V0c7DMpdM3X3sKilu4-k=/90x90/filters:no_upscale():max_bytes(150000):strip_icc():format(webp)/Amanda-Suarez-WebReady-2-f1a81713d9b74568bdfeb53d4f4cdfac.jpg" width="90" height="90" alt="Amanda Suarez's Headshot" class=" universal-image__image lazyloadwait">
    <noscript>
    <img
    src="https://www.seriouseats.com/thmb/1hl6pe1V0c7DMpdM3X3sKilu4-k=/90x90/filters:no_upscale():max_bytes(150000):strip_icc():format(webp)/Amanda-Suarez-WebReady-2-f1a81713d9b74568bdfeb53d4f4cdfac.jpg"
    width="90"
    height="90"
    class="img--noscript universal-image__image"
    alt="Amanda Suarez&#39;s Headshot"
    />
    </noscript>
    </div>
    <div class="editor__details">
    <span class="editor__name">
    Amanda Suarez
    </span>
    <h3 class="editor__title">
    Associate Director, Visuals
    </h3>
    <h3 class="editor__title">
    Knows Sports
    </h3>
    </div>
    </a></div></div></div>
    <div id="mntl-carousel__item_2-0" class="comp mntl-carousel__item mntl-block" data-ordinal="1" style="transform: translateX(0px);"><div id="mntl-carousel__wrapper-item_5-0" class="comp mntl-carousel__wrapper-item mntl-block" data-ordinal="4"><div id="editor-image-block_5-0" class="comp editor-image-block"><a href="https://www.seriouseats.com/leah-colins-senior-culinary-editor-at-serious-eats-7504244" rel="nocaes" class="editor__bio-wrapper"><div class="loc editor__image"><img data-src="https://www.seriouseats.com/thmb/12hQ9D7QF56GbGiE4ZIVApMYO08=/90x90/filters:no_upscale():max_bytes(150000):strip_icc():format(webp)/LeahColinsHeadshot-7a6fabb7a70844a39f08ada94b32e842.JPG" width="90" height="90" alt="A studio portrait of editor Leah Colins." class=" universal-image__image lazyloadwait">
    <noscript>
    <img
    src="https://www.seriouseats.com/thmb/12hQ9D7QF56GbGiE4ZIVApMYO08=/90x90/filters:no_upscale():max_bytes(150000):strip_icc():format(webp)/LeahColinsHeadshot-7a6fabb7a70844a39f08ada94b32e842.JPG"
    width="90"
    height="90"
    class="img--noscript universal-image__image"
    alt="A studio portrait of editor Leah Colins."
    />
    </noscript>
    </div>
    <div class="editor__details">
    <span class="editor__name">
    Leah Colins
    </span>
    <h3 class="editor__title">
    Senior Culinary Editor
    </h3>
    <h3 class="editor__title">
    Bay Leaf Believer
    </h3>
    </div>
    </a></div></div>
    <div id="mntl-carousel__wrapper-item_6-0" class="comp mntl-carousel__wrapper-item mntl-block" data-ordinal="4"><div id="editor-image-block_6-0" class="comp editor-image-block"><a href="https://www.seriouseats.com/genevieve-yam-6830491" rel="nocaes" class="editor__bio-wrapper"><div class="loc editor__image"><img data-src="https://www.seriouseats.com/thmb/dURB88YDMbtvn-1CphdbMUm4a-8=/90x90/filters:no_upscale():max_bytes(150000):strip_icc():format(webp)/GenevieveYam-Headshot-SquareCrop-e1a53ef1a50447d9b3e3f666b079c045.jpg" width="90" height="90" alt="Headshot of Genevieve Yam" class=" universal-image__image lazyloadwait">
    <noscript>
    <img
    src="https://www.seriouseats.com/thmb/dURB88YDMbtvn-1CphdbMUm4a-8=/90x90/filters:no_upscale():max_bytes(150000):strip_icc():format(webp)/GenevieveYam-Headshot-SquareCrop-e1a53ef1a50447d9b3e3f666b079c045.jpg"
    width="90"
    height="90"
    class="img--noscript universal-image__image"
    alt="Headshot of Genevieve Yam"
    />
    </noscript>
    </div>
    <div class="editor__details">
    <span class="editor__name">
    Genevieve Yam
    </span>
    <h3 class="editor__title">
    Culinary Editor
    </h3>
    <h3 class="editor__title">
    Hungry Bookworm
    </h3>
    </div>
    </a></div></div>
    <div id="mntl-carousel__wrapper-item_7-0" class="comp mntl-carousel__wrapper-item mntl-block" data-ordinal="4"><div id="editor-image-block_7-0" class="comp editor-image-block"><a href="https://www.seriouseats.com/kelli-solomon-8584406" rel="nocaes" class="editor__bio-wrapper"><div class="loc editor__image"><img data-src="https://www.seriouseats.com/thmb/IukCeU4D3iera2kh8z8go9GFP1g=/90x90/filters:no_upscale():max_bytes(150000):strip_icc():format(webp)/Kelli-Solomon-WebReady_02-e0624ecce52848e6bfe9c491cc7c5e66.jpg" width="90" height="90" alt="Headshot of Kelli Solomon" class=" universal-image__image lazyloadwait">
    <noscript>
    <img
    src="https://www.seriouseats.com/thmb/IukCeU4D3iera2kh8z8go9GFP1g=/90x90/filters:no_upscale():max_bytes(150000):strip_icc():format(webp)/Kelli-Solomon-WebReady_02-e0624ecce52848e6bfe9c491cc7c5e66.jpg"
    width="90"
    height="90"
    class="img--noscript universal-image__image"
    alt="Headshot of Kelli Solomon"
    />
    </noscript>
    </div>
    <div class="editor__details">
    <span class="editor__name">
    Kelli Solomon
    </span>
    <h3 class="editor__title">
    Senior Social Media Editor
    </h3>
    <h3 class="editor__title">
    Pickles Everything
    </h3>
    </div>
    </a></div></div>
    <div id="mntl-carousel__wrapper-item_8-0" class="comp mntl-carousel__wrapper-item mntl-block" data-ordinal="4"><div id="editor-image-block_8-0" class="comp editor-image-block"><a href="https://www.seriouseats.com/yasmine-maggio-5119057" rel="nocaes" class="editor__bio-wrapper"><div class="loc editor__image"><img data-src="https://www.seriouseats.com/thmb/O82tKGFlvGe6DeX-qY1qtKXhQd8=/90x90/filters:no_upscale():max_bytes(150000):strip_icc():format(webp)/yasmine-maggio-8caf0add3c484a3792049ce8b026c2b8.jpg" width="90" height="90" alt="Yasmine Maggio" class=" universal-image__image lazyloadwait">
    <noscript>
    <img
    src="https://www.seriouseats.com/thmb/O82tKGFlvGe6DeX-qY1qtKXhQd8=/90x90/filters:no_upscale():max_bytes(150000):strip_icc():format(webp)/yasmine-maggio-8caf0add3c484a3792049ce8b026c2b8.jpg"
    width="90"
    height="90"
    class="img--noscript universal-image__image"
    alt="Yasmine Maggio"
    />
    </noscript>
    </div>
    <div class="editor__details">
    <span class="editor__name">
    Yasmine Maggio
    </span>
    <h3 class="editor__title">
    Associate Editor
    </h3>
    <h3 class="editor__title">
    Pizza Enthusiast
    </h3>
    </div>
    </a></div></div></div>
    <div id="mntl-carousel__item_3-0" class="comp mntl-carousel__item mntl-block" data-ordinal="2" style="transform: translateX(0px);"><div id="mntl-carousel__wrapper-item_9-0" class="comp mntl-carousel__wrapper-item mntl-block" data-ordinal="8"><div id="editor-image-block_9-0" class="comp editor-image-block"><a href="https://www.seriouseats.com/riddley-gemperlein-schirm-5209769" rel="nocaes" class="editor__bio-wrapper"><div class="loc editor__image"><img data-src="https://www.seriouseats.com/thmb/GmRb0iiIjqFwSMOVvvKrtJroFmQ=/90x90/filters:no_upscale():max_bytes(150000):strip_icc():format(webp)/image-56de66632c184ec0824068a796be40c4.png" width="90" height="90" alt="Riddley Gemperlein-Schirm Serious Eats" class=" universal-image__image lazyloadwait">
    <noscript>
    <img
    src="https://www.seriouseats.com/thmb/GmRb0iiIjqFwSMOVvvKrtJroFmQ=/90x90/filters:no_upscale():max_bytes(150000):strip_icc():format(webp)/image-56de66632c184ec0824068a796be40c4.png"
    width="90"
    height="90"
    class="img--noscript universal-image__image"
    alt="Riddley Gemperlein-Schirm Serious Eats"
    />
    </noscript>
    </div>
    <div class="editor__details">
    <span class="editor__name">
    Riddley Gemperlein-Schirm
    </span>
    <h3 class="editor__title">
    Associate Editorial Director
    </h3>
    <h3 class="editor__title">
    Gear Nerd
    </h3>
    </div>
    </a></div></div>
    <div id="mntl-carousel__wrapper-item_10-0" class="comp mntl-carousel__wrapper-item mntl-block" data-ordinal="8"><div id="editor-image-block_10-0" class="comp editor-image-block"><a href="https://www.seriouseats.com/grace-kelly-5324217" rel="nocaes" class="editor__bio-wrapper"><div class="loc editor__image"><img data-src="https://www.seriouseats.com/thmb/nqYL-nHclOqmRyI31Ymsu71t30c=/90x90/filters:no_upscale():max_bytes(150000):strip_icc():format(webp)/SE-grace-kelly-headshot-686ca38a674047838a8e6f56e721e667.jpg" width="90" height="90" alt="Grace Kelly headshot against a black background" class=" universal-image__image lazyloadwait">
    <noscript>
    <img
    src="https://www.seriouseats.com/thmb/nqYL-nHclOqmRyI31Ymsu71t30c=/90x90/filters:no_upscale():max_bytes(150000):strip_icc():format(webp)/SE-grace-kelly-headshot-686ca38a674047838a8e6f56e721e667.jpg"
    width="90"
    height="90"
    class="img--noscript universal-image__image"
    alt="Grace Kelly headshot against a black background"
    />
    </noscript>
    </div>
    <div class="editor__details">
    <span class="editor__name">
    Grace Kelly
    </span>
    <h3 class="editor__title">
    Editor
    </h3>
    <h3 class="editor__title">
    Chourico Evangelist
    </h3>
    </div>
    </a></div></div>
    <div id="mntl-carousel__wrapper-item_11-0" class="comp mntl-carousel__wrapper-item mntl-block" data-ordinal="8"><div id="editor-image-block_11-0" class="comp editor-image-block"><a href="https://www.seriouseats.com/rochelle-bilow-5215280" rel="nocaes" class="editor__bio-wrapper"><div class="loc editor__image"><img data-src="https://www.seriouseats.com/thmb/xy3O-rQjuJW1KMyGp9VkMKK-apY=/90x90/filters:no_upscale():max_bytes(150000):strip_icc():format(webp)/rochelle-bilow_headshot-176b4cc505184a8988fd1b16ce00cbe7.jpg" width="90" height="90" alt="Rochelle's headshot" class=" universal-image__image lazyloadwait">
    <noscript>
    <img
    src="https://www.seriouseats.com/thmb/xy3O-rQjuJW1KMyGp9VkMKK-apY=/90x90/filters:no_upscale():max_bytes(150000):strip_icc():format(webp)/rochelle-bilow_headshot-176b4cc505184a8988fd1b16ce00cbe7.jpg"
    width="90"
    height="90"
    class="img--noscript universal-image__image"
    alt="Rochelle&#39;s headshot"
    />
    </noscript>
    </div>
    <div class="editor__details">
    <span class="editor__name">
    Rochelle Bilow
    </span>
    <h3 class="editor__title">
    Editor
    </h3>
    <h3 class="editor__title">
    Scone Apologist
    </h3>
    </div>
    </a></div></div>
    <div id="mntl-carousel__wrapper-item_12-0" class="comp mntl-carousel__wrapper-item mntl-block" data-ordinal="8"><div id="editor-image-block_12-0" class="comp editor-image-block"><a href="https://www.seriouseats.com/an-uong-6385429" rel="nocaes" class="editor__bio-wrapper"><div class="loc editor__image"><img data-src="https://www.seriouseats.com/thmb/Ld1u-IU0U3F-FuBdn39cruaGSCk=/90x90/filters:no_upscale():max_bytes(150000):strip_icc():format(webp)/AnUongPhoto-720e523030de4693ae9f11ee2b63d2fd.jpg" width="90" height="90" alt="An Uong Bio photo" class=" universal-image__image lazyloadwait">
    <noscript>
    <img
    src="https://www.seriouseats.com/thmb/Ld1u-IU0U3F-FuBdn39cruaGSCk=/90x90/filters:no_upscale():max_bytes(150000):strip_icc():format(webp)/AnUongPhoto-720e523030de4693ae9f11ee2b63d2fd.jpg"
    width="90"
    height="90"
    class="img--noscript universal-image__image"
    alt="An Uong Bio photo"
    />
    </noscript>
    </div>
    <div class="editor__details">
    <span class="editor__name">
    An Uong
    </span>
    <h3 class="editor__title">
    Writer
    </h3>
    <h3 class="editor__title">
    Recipe Tinkerer
    </h3>
    </div>
    </a></div></div></div>
    </ul>
    <div class="mntl-carousel__arrow mntl-carousel__arrow--right is-active" data-click-tracked="true" role="button" aria-label="Present next article" tabindex="0" aria-disabled="false">
    <svg class="icon mntl-carousel__arrow-icon ">
    <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#mntl-carousel__arrow-icon" href="#mntl-carousel__arrow-icon"></use>
    </svg>
    </div>
    </div>
    </div>
    </div>
    </section></div></div>
    <section id="food-science_1-0" class="comp food-science section--featured-island section--island section l-container"> <header class="section__header l-container"><h2 class="section__title " data-title-tag=""><span class="section__title--filled">Food</span> <span class="section__title--gradient">Science</span></h2> <div class="loc section-header"><a href="https://www.seriouseats.com/food-science-5118036" rel="nocaes" id="section__title_2-0" class="text-link section__title--featured-island" data-tracking-container="true"> <span class="link__wrapper"></span> <svg class="icon arrow-circle-right section__title-icon"> <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#arrow-circle-right" href="#arrow-circle-right"></use> </svg> <span id="featured-island__description_1-0" class="comp featured-island__description mntl-text-block">In the lab with Serious Eats</span></a> </div></header>
    <div class="loc section-content section__content "><div id="card-list_2-0" class="comp card-list mntl-block l-container" data-tracking-container="true"><div id="card-list__item_5-0" class="comp card-list__item mntl-block"><a id="card_5-0" class="comp card" href="https://www.seriouseats.com/coffee-cooling-science-8649621">
    <div class="loc card__header"><div class="card__media mntl-universal-image universal-image__container"><video autoplay="" loop="" playsinline="" muted="" class="mntl-gif__video card__image universal-image__image" width="603" height="414" aria-label="coffee through out the day gif" data-src="https://www.seriouseats.com/thmb/8Vlxr-MXwX1_4-AOPj_RVtoOFOQ=/603x414/filters:gifv(webm)/SE_CoffeeThroughoutDay-edea4dbaf421460f927517ad67844d44.gif">
    </video>
    <noscript>
    <video
    autoplay
    loop
    playsinline
    muted
    class="mntl-gif__video mntl-gif__video--fallback card__image universal-image__image "
    width="603"
    height="414"
    aria-label="coffee through out the day gif"
    src="https://www.seriouseats.com/thmb/qCmeLCV7IgP4KQQ8pU0TVkJrt2g=/300x0/filters:gifv(webm)/SE_CoffeeThroughoutDay-edea4dbaf421460f927517ad67844d44.gif"
    >
    </video>
    </noscript></div>
    </div> <div class="card__wrapper">
    <div class="card__content">
    <span class="card__title">
    <span class="card__underline">
    What Happens to Your Coffee As It Sits
    </span>
    </span>
    <div id="card__author-name_5-0" class="comp card__author-name mntl-block" data-byline-author="By Ashley Rodriguez"></div> </div>
    </div>
    </a></div>
    <div id="card-list__item_6-0" class="comp card-list__item mntl-block"><a id="card_6-0" class="comp card" href="https://www.seriouseats.com/alka-seltzer-cheese-sauce-recipe-8643844">
    <div class="loc card__header"><div class="card__media mntl-universal-image universal-image__container"><img data-src="https://www.seriouseats.com/thmb/dx7luNlHlHTmesEWSf63SsIuyjs=/300x527/filters:no_upscale():max_bytes(150000):strip_icc():format(webp)/20240506-SEA-AlkaSeltzerNachoCheese-AmandaSuarez-HERO1-ceedd89a01bc4d8f9e1d3d3a523d4300.jpg" width="300" height="527" alt="Alka-Seltzer cheese sauce atop nachos." class=" card__image universal-image__image lazyloadwait">
    <noscript>
    <img
    src="https://www.seriouseats.com/thmb/dx7luNlHlHTmesEWSf63SsIuyjs=/300x527/filters:no_upscale():max_bytes(150000):strip_icc():format(webp)/20240506-SEA-AlkaSeltzerNachoCheese-AmandaSuarez-HERO1-ceedd89a01bc4d8f9e1d3d3a523d4300.jpg"
    width="300"
    height="527"
    class="img--noscript card__image universal-image__image"
    alt="Alka-Seltzer cheese sauce atop nachos."
    />
    </noscript></div>
    </div> <div class="card__wrapper">
    <div class="card__content">
    <span class="card__title">
    <span class="card__underline">
    The Genius Ingredient for Gooey Cheese Sauce
    </span>
    </span>
    <div id="card__author-name_6-0" class="comp card__author-name mntl-block" data-byline-author="By Swetha Sivakumar"></div> </div>
    </div>
    </a></div>
    <div id="card-list__item_7-0" class="comp card-list__item mntl-block"><a id="card_7-0" class="comp card" href="https://www.seriouseats.com/how-does-knuckle-method-for-rice-work-8721408">
    <div class="loc card__header"><div class="card__media mntl-universal-image universal-image__container"><img data-src="https://www.seriouseats.com/thmb/oJXtGhoy1Fox7-Ops-3t0__O8Y4=/300x527/filters:no_upscale():max_bytes(150000):strip_icc():format(webp)/2024-10-10SEA-KnuckleRice-AmandaSuarez-FINALFINAL-e130c8c9f7e849cbb145720fddbfcbb8.jpg" width="300" height="527" alt="Graphic for measuring knuckle rice" class=" card__image universal-image__image lazyloadwait">
    <noscript>
    <img
    src="https://www.seriouseats.com/thmb/oJXtGhoy1Fox7-Ops-3t0__O8Y4=/300x527/filters:no_upscale():max_bytes(150000):strip_icc():format(webp)/2024-10-10SEA-KnuckleRice-AmandaSuarez-FINALFINAL-e130c8c9f7e849cbb145720fddbfcbb8.jpg"
    width="300"
    height="527"
    class="img--noscript card__image universal-image__image"
    alt="Graphic for measuring knuckle rice"
    />
    </noscript></div>
    </div> <div class="card__wrapper">
    <div class="card__content">
    <span class="card__title">
    <span class="card__underline">
    Is This the Most Foolproof Method for Making Rice?
    </span>
    </span>
    <div id="card__author-name_7-0" class="comp card__author-name mntl-block" data-byline-author="By Genevieve Yam"></div> </div>
    </div>
    </a></div></div>
    </div>
    </section>
    <section id="tile-island_1-0" class="comp tile-island section--island section l-container"> <header class="section__header l-container"><div class="loc section-header"><a href="#" rel="nofollow nocaes" id="section__title_3-0" class="text-link section__title" data-tracking-container="true"> <span class="link__wrapper">Serious Eats Voices</span> <span id="tile-island__description_1-0" class="comp tile-island__description mntl-text-block">Personal Essays and More Features From Our Writers</span></a> </div></header>
    <div class="loc section-content section__content "><div id="card-list_3-0" class="comp card-list mntl-block" data-tracking-container="true"><div id="card-list__item_8-0" class="comp card-list__item mntl-block"><a id="card_8-0" class="comp card" href="https://www.seriouseats.com/el-impenetrable-chaqueno-cuisine-5509593">
    <div class="card__wrapper">
    <div class="card__content">
    <div class="card__tag" data-tax-tag="Features" data-updated="11/21/24"></div>
    <span class="card__title">
    <span class="card__underline">
    My Journey Into Gran Chaco's El Impenetrable
    </span>
    </span>
    <p class="card__text">On the southern end of Gran Chaco sits the entrance of El Impenetrable, known for its forboding ecosystem of tightly packed wilderness and razor-sharp thorny shrubs. This section of Argentinian forest is home to an isolated community whose cuisine is intimately connected to the land's bounty and scarcity.</p>
    <div id="card__author-name_8-0" class="comp card__author-name mntl-block" data-byline-author="By Kevin Vaughn"></div>
    <svg class="icon arrow-circle-right section__title-icon">
    <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#arrow-circle-right" href="#arrow-circle-right"></use>
    </svg> </div>
    </div>
    </a></div>
    <div id="card-list__item_9-0" class="comp card-list__item mntl-block"><a id="card_9-0" class="comp card" href="https://www.seriouseats.com/history-of-eating-the-last-piece-8719134">
    <div class="card__wrapper">
    <div class="card__content">
    <div class="card__tag" data-tax-tag="Food History" data-updated="09/26/24"></div>
    <span class="card__title">
    <span class="card__underline">
    The History of Eating the Last Piece
    </span>
    </span>
    <p class="card__text">There is a sense of shame when eating the last bite of food that transcends cultures. We dove into the historical and sociological roots of this international taboo.</p>
    <div id="card__author-name_9-0" class="comp card__author-name mntl-block" data-byline-author="By Rachel Baron"></div>
    <svg class="icon arrow-circle-right section__title-icon">
    <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#arrow-circle-right" href="#arrow-circle-right"></use>
    </svg> </div>
    </div>
    </a></div>
    <div id="card-list__item_10-0" class="comp card-list__item mntl-block"><a id="card_10-0" class="comp card" href="https://www.seriouseats.com/oyster-cracker-history-8685870">
    <div class="card__wrapper">
    <div class="card__content">
    <div class="card__tag" data-tax-tag="Ingredients" data-updated="09/05/24"></div>
    <span class="card__title">
    <span class="card__underline">
    An Ode to the Oyster Cracker
    </span>
    </span>
    <p class="card__text">The oyster cracker is mainstay for topping soups, stews, and chilis, and has also become a blank canvas for creative snack mixes. Here, we shine the spotlight on this salty little cracker, its disputed origin story, and why it deserves your attention.</p>
    <div id="card__author-name_10-0" class="comp card__author-name mntl-block" data-byline-author="By Lauren Breedlove"></div>
    <svg class="icon arrow-circle-right section__title-icon">
    <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#arrow-circle-right" href="#arrow-circle-right"></use>
    </svg> </div>
    </div>
    </a></div></div>
    </div>
    </section>
    <div id="mm-ads-leaderboard-fixed-lazy_2-0" class="comp mm-ads-leaderboard-fixed-lazy mm-ads-leaderboard-fixed mm-ads-gpt-adunit has-right-label leaderboard js-lazy-ad gpt leaderboard dynamic">
    <div id="leaderboard-fixed-2" class="wrapper gpt-ignore-incrementor" data-type="leaderboard" data-pos="atf" data-priority="99" data-sizes="[[728, 90]]" data-rtb="true" data-wait-for-third-party="false" data-targeting="{}"></div>
    </div>
    <section id="homepage-island_1-0" class="comp homepage-island section--island section l-container"> <header class="section__header l-page-padding"><div class="loc section-header"><a href="https://www.seriouseats.com/world-cuisine-guides-5117177" rel="nocaes" id="section__title_4-0" class="text-link section__title section__title--link" data-tracking-container="true"> <span class="link__wrapper">World Cuisines</span> <svg class="icon arrow-circle-right section__title-icon"> <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#arrow-circle-right" href="#arrow-circle-right"></use> </svg></a> <div id="section-title_2-0" class="comp section-title mntl-block"><span id="section-title-main_2-0" class="comp section-title-main mntl-text-block">World Cuisines</span></div> </div></header>
    <div class="loc section-content section__content "><div id="card-list_4-0" class="comp card-list mntl-block l-container" data-tracking-container="true"><div id="card-list__item_11-0" class="comp card-list__item mntl-block"><a id="card_11-0" class="comp card" href="https://www.seriouseats.com/global-eats-travel-guides-8736299">
    <div class="loc card__header"><div class="card__media mntl-universal-image universal-image__container"><video autoplay="" loop="" playsinline="" muted="" class="mntl-gif__video card__image universal-image__image" width="405" height="270" aria-label="Global Eats Hero" data-src="https://www.seriouseats.com/thmb/AVLy0R3gIHcSG-bj0kScxcMz2UU=/405x270/filters:gifv(webm)/GlobalEatsHero-5d3592483ca94fc7940ef217af655c7d.gif">
    </video>
    <noscript>
    <video
    autoplay
    loop
    playsinline
    muted
    class="mntl-gif__video mntl-gif__video--fallback card__image universal-image__image "
    width="405"
    height="270"
    aria-label="Global Eats Hero"
    src="https://www.seriouseats.com/thmb/8_EdpgFceZBVqHl3ElmEowbrL9Q=/300x0/filters:gifv(webm)/GlobalEatsHero-5d3592483ca94fc7940ef217af655c7d.gif"
    >
    </video>
    </noscript></div>
    </div> <div class="card__wrapper">
    <div class="card__content">
    <div class="card__tag" data-tax-tag="Dining Out" data-updated="11/07/24"></div>
    <span class="card__title">
    <span class="card__underline">
    Global Eats: Our Expert Guide to Seven of the World's Most Delicious Dining Destinations
    </span>
    </span>
    <div id="card__author-name_11-0" class="comp card__author-name mntl-block" data-byline-author="By Megan O. Steintrager and The Serious Eats Team"></div> </div>
    </div>
    </a></div>
    <div id="card-list__item_12-0" class="comp card-list__item mntl-block"><a id="card_12-0" class="comp card" href="https://www.seriouseats.com/turkey-ssam-recipe-8749227">
    <div class="loc card__header"><div class="card__media mntl-universal-image universal-image__container"><img data-src="https://www.seriouseats.com/thmb/tz3a_nCeN0gfdL3kMM4SM2tl0iU=/405x270/filters:no_upscale():max_bytes(150000):strip_icc():format(webp)/__opt__aboutcom__coeus__resources__content_migration__serious_eats__seriouseats.com__2020__11__20201015-banchan-thanksgiving-group-vicky-wasik-1-10-1078b1091be741c5b403f30dfa1d7f69.jpg" width="405" height="270" alt="20201015-banchan-thanksgiving-group-vicky-wasik-1-10" class=" card__image universal-image__image lazyloadwait">
    <noscript>
    <img
    src="https://www.seriouseats.com/thmb/tz3a_nCeN0gfdL3kMM4SM2tl0iU=/405x270/filters:no_upscale():max_bytes(150000):strip_icc():format(webp)/__opt__aboutcom__coeus__resources__content_migration__serious_eats__seriouseats.com__2020__11__20201015-banchan-thanksgiving-group-vicky-wasik-1-10-1078b1091be741c5b403f30dfa1d7f69.jpg"
    width="405"
    height="270"
    class="img--noscript card__image universal-image__image"
    alt="20201015-banchan-thanksgiving-group-vicky-wasik-1-10"
    />
    </noscript></div>
    </div> <div class="card__wrapper">
    <div class="card__content">
    <div class="card__tag" data-tax-tag="Thanksgiving" data-updated="12/23/24"></div>
    <span class="card__title">
    <span class="card__underline">
    Turkey Ssam
    </span>
    </span>
    <div id="card__author-name_12-0" class="comp card__author-name mntl-block" data-byline-author="By Sunny Lee"></div> </div>
    </div>
    </a></div>
    <div id="card-list__item_13-0" class="comp card-list__item mntl-block"><a id="card_13-0" class="comp card" href="https://www.seriouseats.com/sweet-potato-sausage-jeon">
    <div class="loc card__header"><div class="card__media mntl-universal-image universal-image__container"><img data-src="https://www.seriouseats.com/thmb/MdqIoJJMtQU0xSZMRmfut_jGedk=/405x270/filters:no_upscale():max_bytes(150000):strip_icc():format(webp)/__opt__aboutcom__coeus__resources__content_migration__serious_eats__seriouseats.com__2020__11__20201015-banchan-thanksgiving-sweet-potato-jeon-yangyeom-dipping-sauce-vicky-wasik-12-2923db4d02bd4cd9ad416e0092f336a0.jpg" width="405" height="270" alt="Korean jeon pancakes made with sweet potato and sausage. " class=" card__image universal-image__image lazyloadwait">
    <noscript>
    <img
    src="https://www.seriouseats.com/thmb/MdqIoJJMtQU0xSZMRmfut_jGedk=/405x270/filters:no_upscale():max_bytes(150000):strip_icc():format(webp)/__opt__aboutcom__coeus__resources__content_migration__serious_eats__seriouseats.com__2020__11__20201015-banchan-thanksgiving-sweet-potato-jeon-yangyeom-dipping-sauce-vicky-wasik-12-2923db4d02bd4cd9ad416e0092f336a0.jpg"
    width="405"
    height="270"
    class="img--noscript card__image universal-image__image"
    alt="Korean jeon pancakes made with sweet potato and sausage. "
    />
    </noscript></div>
    </div> <div class="card__wrapper">
    <div class="card__content">
    <div class="card__tag" data-tax-tag="Snacks &amp; Appetizers" data-updated="11/02/20"></div>
    <span class="card__title">
    <span class="card__underline">
    Sweet Potato and Sausage Jeon With Yangnyeom Dipping Sauce Recipe
    </span>
    </span>
    <div id="card__author-name_13-0" class="comp card__author-name mntl-block" data-byline-author="By Sunny Lee"></div> </div>
    </div>
    </a></div></div>
    </div>
    </section>
    <div id="homepage-equipment_1-0" class="comp homepage-equipment mntl-block l-container l-page-padding"><div id="mm-ads-leaderboard-fixed-lazy_3-0" class="comp mm-ads-leaderboard-fixed-lazy mm-ads-leaderboard-fixed mm-ads-gpt-adunit has-right-label leaderboard js-lazy-ad gpt leaderboard dynamic">
    <div id="leaderboard-fixed-3" class="wrapper gpt-ignore-incrementor" data-type="leaderboard" data-pos="atf" data-priority="99" data-sizes="[[728, 90]]" data-rtb="true" data-wait-for-third-party="false" data-targeting="{}"></div>
    </div>
    <div id="homepage-equipment__title_1-0" class="comp homepage-equipment__title mntl-block"><h2 id="homepage-equipment__list-title_1-0" class="comp homepage-equipment__list-title mntl-text-block">Top Pots</h2>
    <p id="homepage-equipment__week-title_1-0" class="comp homepage-equipment__week-title mntl-text-block">Equipment of the week</p></div>
    <div id="homepage-equipment__list_1-0" class="comp homepage-equipment__list mntl-block"><div id="homepage-equipment__list-item_1-0" class="comp homepage-equipment__list-item mntl-block"><div id="homepage-equipment__tab-text-container_1-0" class="comp homepage-equipment__tab-text-container mntl-block"><p id="homepage-equipment__tab-text_1-0" class="comp homepage-equipment__tab-text mntl-text-block">EQ Review</p></div>
    <svg class="icon our-picks ">
    <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#our-picks" href="#our-picks"></use>
    </svg>
    <a id="homepage-equipment__card_1-0" class="comp homepage-equipment__card card" href="https://www.seriouseats.com/le-creuset-new-year-sale-2024-8762399">
    <div class="loc card__header"><div class="card__media mntl-universal-image universal-image__container"><img data-src="https://www.seriouseats.com/thmb/b7B2BQ4UQi3vSSOf_FmLRzrIrLI=/460x345/filters:no_upscale():max_bytes(150000):strip_icc():format(webp)/tested-favorites-from-le-creuset-ftt-sale-tout-ccb4103f8230418e90c632121d2a49f4.jpg" width="460" height="345" alt="Tested Favorites from Le Creuset FTT Sale tout" class=" card__image universal-image__image lazyloadwait">
    <noscript>
    <img
    src="https://www.seriouseats.com/thmb/b7B2BQ4UQi3vSSOf_FmLRzrIrLI=/460x345/filters:no_upscale():max_bytes(150000):strip_icc():format(webp)/tested-favorites-from-le-creuset-ftt-sale-tout-ccb4103f8230418e90c632121d2a49f4.jpg"
    width="460"
    height="345"
    class="img--noscript card__image universal-image__image"
    alt="Tested Favorites from Le Creuset FTT Sale tout"
    />
    </noscript></div>
    </div> <div class="card__wrapper">
    <div class="card__content">
    <div class="card__tag" data-tax-tag="Equipment"></div>
    <span class="card__title">
    <span class="card__underline">
    Le Creuset Is Having a Huge Sale—Here’s What’s Worth Buying
    </span>
    </span>
    <div id="card__author-name_14-0" class="comp card__author-name mntl-block" data-byline-author="By Rochelle Bilow"></div> </div>
    </div>
    </a></div>
    <div id="homepage-equipment__list-item_2-0" class="comp homepage-equipment__list-item mntl-block"><div id="homepage-equipment__tab-text-container_2-0" class="comp homepage-equipment__tab-text-container mntl-block"><p id="homepage-equipment__tab-text_2-0" class="comp homepage-equipment__tab-text mntl-text-block">What to Do</p></div>
    <svg class="icon our-picks ">
    <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#our-picks" href="#our-picks"></use>
    </svg>
    <a id="homepage-equipment__card_2-0" class="comp homepage-equipment__card card" href="https://www.seriouseats.com/le-creuset-vs-staub-dutch-oven-7108901">
    <div class="loc card__header"><div class="card__media mntl-universal-image universal-image__container"><img data-src="https://www.seriouseats.com/thmb/SW8Y5gO3hdCbBFyt2jpOqaTLbI4=/460x345/filters:no_upscale():max_bytes(150000):strip_icc():format(webp)/SE-le-creuset-vs-staub-01_correctsize1-731b71b5e69a496ea9886a068dfe384d.jpg" width="460" height="345" alt="Le Creuset and Staub dutch ovens collaged against patterned yellow background" class=" card__image universal-image__image lazyloadwait">
    <noscript>
    <img
    src="https://www.seriouseats.com/thmb/SW8Y5gO3hdCbBFyt2jpOqaTLbI4=/460x345/filters:no_upscale():max_bytes(150000):strip_icc():format(webp)/SE-le-creuset-vs-staub-01_correctsize1-731b71b5e69a496ea9886a068dfe384d.jpg"
    width="460"
    height="345"
    class="img--noscript card__image universal-image__image"
    alt="Le Creuset and Staub dutch ovens collaged against patterned yellow background"
    />
    </noscript></div>
    </div> <div class="card__wrapper">
    <div class="card__content">
    <div class="card__tag" data-tax-tag="Cookware"></div>
    <span class="card__title">
    <span class="card__underline">
    Le Creuset vs. Staub Dutch Ovens: The Battle of the Best
    </span>
    </span>
    <div id="card__author-name_15-0" class="comp card__author-name mntl-block" data-byline-author="By Riddley Gemperlein-Schirm"></div> </div>
    </div>
    </a></div>
    <div id="homepage-equipment__list-item_3-0" class="comp homepage-equipment__list-item mntl-block"><div id="homepage-equipment__tab-text-container_3-0" class="comp homepage-equipment__tab-text-container mntl-block"><p id="homepage-equipment__tab-text_3-0" class="comp homepage-equipment__tab-text mntl-text-block">What to Make</p></div>
    <svg class="icon our-picks ">
    <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#our-picks" href="#our-picks"></use>
    </svg>
    <a id="homepage-equipment__card_3-0" class="comp homepage-equipment__card card" href="https://www.seriouseats.com/red-wine-braised-beef-short-ribs-recipe">
    <div class="loc card__header"><div class="card__media mntl-universal-image universal-image__container"><img data-src="https://www.seriouseats.com/thmb/GHD4BSK7za3QSlKRfmBN4ddldlg=/460x345/filters:no_upscale():max_bytes(150000):strip_icc():format(webp)/__opt__aboutcom__coeus__resources__content_migration__serious_eats__seriouseats.com__2019__11__20191104-red-wine-braised-short-ribs-vicky-wasik-17-1adb4ad27d7f475cbc58814a45e3198b.jpg" width="460" height="345" alt="Red Wine-Braised Beef Short Ribs" class=" card__image universal-image__image lazyloadwait">
    <noscript>
    <img
    src="https://www.seriouseats.com/thmb/GHD4BSK7za3QSlKRfmBN4ddldlg=/460x345/filters:no_upscale():max_bytes(150000):strip_icc():format(webp)/__opt__aboutcom__coeus__resources__content_migration__serious_eats__seriouseats.com__2019__11__20191104-red-wine-braised-short-ribs-vicky-wasik-17-1adb4ad27d7f475cbc58814a45e3198b.jpg"
    width="460"
    height="345"
    class="img--noscript card__image universal-image__image"
    alt="Red Wine-Braised Beef Short Ribs"
    />
    </noscript></div>
    </div> <div class="card__wrapper">
    <div class="card__content">
    <div class="card__tag" data-tax-tag="Short Ribs"></div>
    <span class="card__title">
    <span class="card__underline">
    Red Wine–Braised Beef Short Ribs Recipe
    </span>
    </span>
    <div id="card__author-name_16-0" class="comp card__author-name mntl-block" data-byline-author="By Daniel Gritzer"></div> </div>
    </div>
    </a></div></div></div>
    <section id="cooking-techniques_1-0" class="comp cooking-techniques section--featured-island section--island section l-container"> <header class="section__header l-container"><h2 class="section__title " data-title-tag="Cooking Techniques">Cooking Techniques</h2> <div class="loc section-header"><a href="https://www.seriouseats.com/tips-trouble-shooting-5118014" rel="nocaes" id="section__title_5-0" class="text-link section__title--featured-island" data-tracking-container="true"> <span class="link__wrapper"></span> <svg class="icon arrow-circle-right section__title-icon"> <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#arrow-circle-right" href="#arrow-circle-right"></use> </svg> <span id="featured-island__description_2-0" class="comp featured-island__description mntl-text-block"></span></a> </div></header>
    <div class="loc section-content section__content "><div id="card-list_5-0" class="comp card-list mntl-block l-container" data-tracking-container="true"><div id="card-list__item_14-0" class="comp card-list__item mntl-block"><a id="card_14-0" class="comp card" href="https://www.seriouseats.com/how-to-pasteurize-eggs-8675279">
    <div class="loc card__header"><div class="card__media mntl-universal-image universal-image__container"><img data-src="https://www.seriouseats.com/thmb/gZYu1PaBgIKM2fjd6xQTS5EMn_M=/603x414/filters:no_upscale():max_bytes(150000):strip_icc():format(webp)/20240709PasterizingEggs-VickyWasik-02-c0870e61f64848dca4ee554d3e405400.jpg" width="603" height="414" alt="Side view of two eggs" class=" card__image universal-image__image lazyloadwait">
    <noscript>
    <img
    src="https://www.seriouseats.com/thmb/gZYu1PaBgIKM2fjd6xQTS5EMn_M=/603x414/filters:no_upscale():max_bytes(150000):strip_icc():format(webp)/20240709PasterizingEggs-VickyWasik-02-c0870e61f64848dca4ee554d3e405400.jpg"
    width="603"
    height="414"
    class="img--noscript card__image universal-image__image"
    alt="Side view of two eggs"
    />
    </noscript></div>
    </div> <div class="card__wrapper">
    <div class="card__content">
    <span class="card__title">
    <span class="card__underline">
    How to (Safely) Pasteurize Eggs at Home
    </span>
    </span>
    <div id="card__author-name_17-0" class="comp card__author-name mntl-block" data-byline-author="By Megan O. Steintrager"></div> </div>
    </div>
    </a></div>
    <div id="card-list__item_15-0" class="comp card-list__item mntl-block"><a id="card_15-0" class="comp card" href="https://www.seriouseats.com/how-to-quickly-soften-canned-chickpeas-and-beans-8665074">
    <div class="loc card__header"><div class="card__media mntl-universal-image universal-image__container"><img data-src="https://www.seriouseats.com/thmb/elft30KHOk6a8Vp0SWUjhqKg02M=/300x527/filters:no_upscale():max_bytes(150000):strip_icc():format(webp)/20240618SEA-CreamyChickpeas-AmandaSureaz-426c5af067b149a9a81e2ae3f440c182.jpg" width="300" height="527" alt="Overhead view of chickpeas" class=" card__image universal-image__image lazyloadwait">
    <noscript>
    <img
    src="https://www.seriouseats.com/thmb/elft30KHOk6a8Vp0SWUjhqKg02M=/300x527/filters:no_upscale():max_bytes(150000):strip_icc():format(webp)/20240618SEA-CreamyChickpeas-AmandaSureaz-426c5af067b149a9a81e2ae3f440c182.jpg"
    width="300"
    height="527"
    class="img--noscript card__image universal-image__image"
    alt="Overhead view of chickpeas"
    />
    </noscript></div>
    </div> <div class="card__wrapper">
    <div class="card__content">
    <span class="card__title">
    <span class="card__underline">
    The Trick to Softening Canned Chickpeas
    </span>
    </span>
    <div id="card__author-name_18-0" class="comp card__author-name mntl-block" data-byline-author="By Swetha Sivakumar"></div> </div>
    </div>
    </a></div>
    <div id="card-list__item_16-0" class="comp card-list__item mntl-block"><a id="card_16-0" class="comp card" href="https://www.seriouseats.com/salting-cucumbers-technique-8684481">
    <div class="loc card__header"><div class="card__media mntl-universal-image universal-image__container"><img data-src="https://www.seriouseats.com/thmb/N9-If6W3DtynblOP60ZLIlZDE18=/300x527/filters:no_upscale():max_bytes(150000):strip_icc():format(webp)/20210815-Creamy-cucumber-salad-1-Lynn-Wolsted-bfcc0a657a3748d3934aac8c489e5b32.jpg" width="300" height="527" alt="salted cucumbers" class=" card__image universal-image__image lazyloadwait">
    <noscript>
    <img
    src="https://www.seriouseats.com/thmb/N9-If6W3DtynblOP60ZLIlZDE18=/300x527/filters:no_upscale():max_bytes(150000):strip_icc():format(webp)/20210815-Creamy-cucumber-salad-1-Lynn-Wolsted-bfcc0a657a3748d3934aac8c489e5b32.jpg"
    width="300"
    height="527"
    class="img--noscript card__image universal-image__image"
    alt="salted cucumbers"
    />
    </noscript></div>
    </div> <div class="card__wrapper">
    <div class="card__content">
    <span class="card__title">
    <span class="card__underline">
    Why You Should Salt Your Cucumbers
    </span>
    </span>
    <div id="card__author-name_19-0" class="comp card__author-name mntl-block" data-byline-author="By Yasmine Maggio"></div> </div>
    </div>
    </a></div></div>
    </div>
    </section>
    <section id="homepage-island_2-0" class="comp homepage-island section--island section l-container"> <header class="section__header l-page-padding"><div class="loc section-header"><a href="https://www.seriouseats.com/all-recipes-5117985" rel="nocaes" id="section__title_6-0" class="text-link section__title section__title--link" data-tracking-container="true"> <span class="link__wrapper">Fresh Recipes</span> <svg class="icon arrow-circle-right section__title-icon"> <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#arrow-circle-right" href="#arrow-circle-right"></use> </svg></a> <div id="section-title_3-0" class="comp section-title mntl-block"><span id="section-title-main_3-0" class="comp section-title-main mntl-text-block">Fresh Recipes</span></div> </div></header>
    <div class="loc section-content section__content "><div id="card-list_6-0" class="comp card-list mntl-block l-container" data-tracking-container="true"><div id="card-list__item_17-0" class="comp card-list__item mntl-block"><a id="card_17-0" class="comp card" href="https://www.seriouseats.com/almond-cream-creme-d-amande-recipe-8764359">
    <div class="loc card__header"><div class="card__media mntl-universal-image universal-image__container"><img data-src="https://www.seriouseats.com/thmb/7kecHUZG8ZbXiuabiHWuvuvtzgU=/405x270/filters:no_upscale():max_bytes(150000):strip_icc():format(webp)/20241219-SEA-AlmondCream-AmandaSuarez-07-029e50701ee34adbae3dd30df5cc41f6.jpg" width="405" height="270" alt="Overhead view of almond cream" class=" card__image universal-image__image lazyloadwait">
    <noscript>
    <img
    src="https://www.seriouseats.com/thmb/7kecHUZG8ZbXiuabiHWuvuvtzgU=/405x270/filters:no_upscale():max_bytes(150000):strip_icc():format(webp)/20241219-SEA-AlmondCream-AmandaSuarez-07-029e50701ee34adbae3dd30df5cc41f6.jpg"
    width="405"
    height="270"
    class="img--noscript card__image universal-image__image"
    alt="Overhead view of almond cream"
    />
    </noscript></div>
    </div> <div class="card__wrapper">
    <div class="card__content">
    <div class="card__tag" data-tax-tag="Baking" data-updated="12/19/24"></div>
    <span class="card__title">
    <span class="card__underline">
    French Almond Cream
    </span>
    </span>
    <div id="card__author-name_20-0" class="comp card__author-name mntl-block" data-byline-author="By Zola Gregory"></div> </div>
    </div>
    </a></div>
    <div id="card-list__item_18-0" class="comp card-list__item mntl-block"><a id="card_18-0" class="comp card" href="https://www.seriouseats.com/italian-pressure-cooker-chickpea-pork-rib-stew-8754879">
    <div class="loc card__header"><div class="card__media mntl-universal-image universal-image__container"><img data-src="https://www.seriouseats.com/thmb/piFPNAh4p3Q0pkUuYC_YAgCa1h4=/405x270/filters:no_upscale():max_bytes(150000):strip_icc():format(webp)/20241204ItalianPressureCookerChickpeaandPorkRibStew-AmandaSuarez-hero-55e3e2270f334016b83206c63145f3fb.jpg" width="405" height="270" alt="Overhead view of stew" class=" card__image universal-image__image lazyloadwait">
    <noscript>
    <img
    src="https://www.seriouseats.com/thmb/piFPNAh4p3Q0pkUuYC_YAgCa1h4=/405x270/filters:no_upscale():max_bytes(150000):strip_icc():format(webp)/20241204ItalianPressureCookerChickpeaandPorkRibStew-AmandaSuarez-hero-55e3e2270f334016b83206c63145f3fb.jpg"
    width="405"
    height="270"
    class="img--noscript card__image universal-image__image"
    alt="Overhead view of stew"
    />
    </noscript></div>
    </div> <div class="card__wrapper">
    <div class="card__content">
    <div class="card__tag" data-tax-tag="Pressure Cooked Stews" data-updated="12/04/24"></div>
    <span class="card__title">
    <span class="card__underline">
    Italian Chickpea and Pork Rib Stew
    </span>
    </span>
    <div id="card__author-name_21-0" class="comp card__author-name mntl-block" data-byline-author="By Daniel Gritzer"></div> </div>
    </div>
    </a></div>
    <div id="card-list__item_19-0" class="comp card-list__item mntl-block"><a id="card_19-0" class="comp card" href="https://www.seriouseats.com/oatmeal-raisin-cookies-recipe-8759166">
    <div class="loc card__header"><div class="card__media mntl-universal-image universal-image__container"><img data-src="https://www.seriouseats.com/thmb/RRzW-HrKoQoIdwgczNbn_45Yw_g=/405x270/filters:no_upscale():max_bytes(150000):strip_icc():format(webp)/20241007-SEA-OatmealRaisinCookies-FredHardy-Hero-94-70645ebdd8c540488081305b4cfc6e19.jpg" width="405" height="270" alt="oatmeal raisin cookies stacked up on top of each other, and one cookie leaning on the stack. 2 glasses of milk in back and a red swirl background wall." class=" card__image universal-image__image lazyloadwait">
    <noscript>
    <img
    src="https://www.seriouseats.com/thmb/RRzW-HrKoQoIdwgczNbn_45Yw_g=/405x270/filters:no_upscale():max_bytes(150000):strip_icc():format(webp)/20241007-SEA-OatmealRaisinCookies-FredHardy-Hero-94-70645ebdd8c540488081305b4cfc6e19.jpg"
    width="405"
    height="270"
    class="img--noscript card__image universal-image__image"
    alt="oatmeal raisin cookies stacked up on top of each other, and one cookie leaning on the stack. 2 glasses of milk in back and a red swirl background wall."
    />
    </noscript></div>
    </div> <div class="card__wrapper">
    <div class="card__content">
    <div class="card__tag" data-tax-tag="Cookies" data-updated="12/10/24"></div>
    <span class="card__title">
    <span class="card__underline">
    Oatmeal Raisin Cookies
    </span>
    </span>
    <div id="card__author-name_22-0" class="comp card__author-name mntl-block" data-byline-author="By Nicole Hopper and Genevieve Yam"></div> </div>
    </div>
    </a></div></div>
    </div>
    </section>
    <div id="mm-ads-leaderboard-fixed-lazy_4-0" class="comp mm-ads-leaderboard-fixed-lazy mm-ads-leaderboard-fixed mm-ads-gpt-adunit has-right-label leaderboard js-lazy-ad gpt leaderboard dynamic">
    <div id="leaderboard-fixed-4" class="wrapper gpt-ignore-incrementor" data-type="leaderboard" data-pos="atf" data-priority="99" data-sizes="[[728, 90]]" data-rtb="true" data-wait-for-third-party="false" data-targeting="{}"></div>
    </div>
    </main><footer id="footer_1-0" class="comp footer mntl-footer" role="contentinfo" data-tracking-container="true">
    <div class="l-container">
    <div class="footer__inner">
    <div class="loc upper-content footer__upper"><div id="footer__logo-wrapper_1-0" class="comp footer__logo-wrapper mntl-block"><a id="logo_1-0" class="comp logo seriouseats" rel="home" href="https://www.seriouseats.com/" aria-label="Visit Serious Eats's homepage">
    <svg class="logo__text logo--light seriouseats-logo " xmlns="http://www.w3.org/2000/svg" viewBox="0 0 272 48" preserveAspectRatio="">
    <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#seriouseats-logo"></use>
    </svg>
    </a></div>
    <div id="newsletter--title-form_1-0" class="comp newsletter--title-form mntl-block newsletter-footer-container"><div id="newsletter__wrapper_2-0" class="comp newsletter__wrapper mntl-block"><span id="newsletter__subtitle_2-0" class="comp newsletter__subtitle text-block" tabindex="0"> Get fresh recipes, cooking tips, deal alerts, and more!
    </span></div>
    <form id="newsletter__form_3-0" class="comp newsletter__form newsletter-signup mntl-newsletter-signup" data-tracking-container="true"><div id="mntl-newsletter-submit_3-0" class="comp mntl-newsletter-submit mntl-newsletter-submit__input-wrapper" data-reg-source-id="isapvp" data-newsletter-object-ids="j8zzd5" data-shared-services="true"> <input type="hidden" name="objectorObjectIds" value="relationship-builder,acquisition">
    <label class="mntl-newsletter-submit__label is-vishidden" for="mntl-newsletter-submit_3-0__input">Email Address</label>
    <input class="mntl-newsletter-submit__input" id="mntl-newsletter-submit_3-0__input" type="email" name="email" placeholder="Enter email" required="" pattern="\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,4}\b">
    <button id="mntl-newsletter-submit__button_3-0" class="comp mntl-newsletter-submit__button mntl-button newsletter-signup__button btn" type="submit" aria-label="Sign Up">
    <svg class="icon icon-circle-arrow mntl-button__icon ">
    <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#icon-circle-arrow" href="#icon-circle-arrow"></use>
    </svg>
    </button></div>
    <div id="newsletter-signup__success-container_3-0" class="comp newsletter-signup__success-container mntl-block"><svg class="icon newsletter-success-image newsletter-signup__success-image">
    <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#newsletter-success-image" href="#newsletter-success-image"></use>
    </svg></div><div id="mntl-newsletter-signup__error_3-0" class="comp mntl-newsletter-signup__error mntl-text-block">There was an error. Please try again.</div></form></div>
    <div id="social-nav-lifestyle--footer_1-0" class="comp social-nav-lifestyle--footer lifestyle-food-social-nav mntl-social-nav social-nav" data-tracking-container="true">
    <ul class="social-nav__list">
    <li class="social-nav__item social-nav__item--facebook">
    <a href="https://www.facebook.com/seriouseats" target="_blank" rel="noopener nocaes" class="social-nav__link social-nav__link--facebook" aria-label="Visit Serious Eats's facebook"> <svg class="icon icon-facebook social-nav__icon">
    <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#icon-facebook" href="#icon-facebook"></use>
    </svg>
    </a> </li>
    <li class="social-nav__item social-nav__item--instagram">
    <a href="https://www.instagram.com/seriouseats" target="_blank" rel="noopener nocaes" class="social-nav__link social-nav__link--instagram" aria-label="Visit Serious Eats's instagram"> <svg class="icon icon-instagram social-nav__icon">
    <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#icon-instagram" href="#icon-instagram"></use>
    </svg>
    </a> </li>
    <li class="social-nav__item social-nav__item--pinterest">
    <a href="https://www.pinterest.com/seriouseats" target="_blank" rel="noopener nocaes" class="social-nav__link social-nav__link--pinterest" aria-label="Visit Serious Eats's pinterest"> <svg class="icon icon-pinterest social-nav__icon">
    <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#icon-pinterest" href="#icon-pinterest"></use>
    </svg>
    </a> </li>
    <li class="social-nav__item social-nav__item--youtube">
    <a href="https://www.youtube.com/user/seriouseats" target="_blank" rel="noopener nocaes" class="social-nav__link social-nav__link--youtube" aria-label="Visit Serious Eats's youtube"> <svg class="icon icon-youtube social-nav__icon">
    <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#icon-youtube" href="#icon-youtube"></use>
    </svg>
    </a> </li>
    <li class="social-nav__item social-nav__item--reddit">
    <a href="https://www.reddit.com/r/seriouseats" target="_blank" rel="noopener nocaes" class="social-nav__link social-nav__link--reddit" aria-label="Visit Serious Eats's reddit"> <svg class="icon icon-reddit social-nav__icon">
    <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#icon-reddit" href="#icon-reddit"></use>
    </svg>
    </a> </li>
    </ul>
    </div>
    </div><div class="loc lower-content footer__lower"><ul id="icon-menu_1-0" class="comp icon-menu icon-menu--tags" data-tracking-container="true"> <li class="icon-menu__item">
    <a href="https://www.seriouseats.com/all-recipes-5117985" rel="nocaes" class="icon-menu__link"> <span class="icon-menu__text icon-menu__text--recipes">
    Recipes
    </span>
    </a> </li>
    <li class="icon-menu__item">
    <a href="https://www.seriouseats.com/how-tos-5118034" rel="nocaes" class="icon-menu__link"> <span class="icon-menu__text icon-menu__text--how-tos">
    How-Tos
    </span>
    </a> </li>
    <li class="icon-menu__item">
    <a href="https://www.seriouseats.com/world-cuisine-guides-5117177" rel="nocaes" class="icon-menu__link"> <span class="icon-menu__text icon-menu__text--world-cuisines">
    World Cuisines
    </span>
    </a> </li>
    <li class="icon-menu__item">
    <a href="https://www.seriouseats.com/ingredients-guides-5118013" rel="nocaes" class="icon-menu__link"> <span class="icon-menu__text icon-menu__text--ingredients">
    Ingredients
    </span>
    </a> </li>
    <li class="icon-menu__item">
    <a href="https://www.seriouseats.com/equipment-5117081" rel="nocaes" class="icon-menu__link"> <span class="icon-menu__text icon-menu__text--equipment">
    Equipment
    </span>
    </a> </li>
    <li class="icon-menu__item">
    <a href="https://www.seriouseats.com/features-5118040" rel="nocaes" class="icon-menu__link"> <span class="icon-menu__text icon-menu__text--features">
    Features
    </span>
    </a> </li>
    </ul>
    <ul id="horizontal-nav_1-0" class="comp horizontal-nav"> <li class="horizontal-nav__item "><a href="https://www.seriouseats.com/about-us-5120006" rel="nocaes" class="horizontal-nav__link">About Us</a></li>
    <li class="horizontal-nav__item "><a href="https://www.dotdashmeredith.com/advertising" target="_blank" rel="noopener nofollow nocaes" class="horizontal-nav__link">Advertise</a></li>
    <li class="horizontal-nav__item "><a href="https://www.dotdashmeredith.com/brands-termsofservice" target="_blank" rel="noopener nofollow nocaes" class="horizontal-nav__link">Terms of Service</a></li>
    <li class="horizontal-nav__item "><a href="https://www.dotdashmeredith.com/careers" target="_blank" rel="noopener nofollow nocaes" class="horizontal-nav__link">Careers</a></li>
    <li class="horizontal-nav__item "><a href="https://www.seriouseats.com/about-us-5120006#toc-recipe-development-and-testing" rel="nocaes" class="horizontal-nav__link">Editorial Guidelines</a></li>
    <li class="horizontal-nav__item "><a href="https://www.seriouseats.com/sweepstakes" rel="nocaes" class="horizontal-nav__link">Sweepstakes</a></li>
    <li class="horizontal-nav__item "><a href="https://www.seriouseats.com/about-us-5120006#toc-contact-us" rel="nocaes" class="horizontal-nav__link">Contact</a></li>
    <li class="horizontal-nav__item "><a href="https://www.dotdashmeredith.com/brands-privacy" target="_blank" rel="noopener nofollow nocaes" class="horizontal-nav__link">Privacy Policy</a></li>
    <li class="horizontal-nav__item ot-pref-trigger"><a href="#" rel="nofollow nocaes" class="horizontal-nav__link">Your Privacy Choices<span class="loc privacy-options-icon"> <svg class="icon icon-privacy-options " role="img" aria-label="Manage your privacy choices">
    <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#icon-privacy-options" href="#icon-privacy-options"></use>
    </svg>
    </span></a></li>
    <li class="loc truste-badge horizontal-nav__item"><div id="truste-badge_1-0" class="comp truste-badge mntl-block"><div id="mntl-universal-truste-badge_1-0" class="comp mntl-universal-truste-badge mntl-block"><a href="//privacy.truste.com/privacy-seal/validation?rid=************************************" target="_blank" rel="noopener nocaes" id="mntl-universal-truste-badge_1-0-link" class="mntl-truste-badge-link" data-tracking-container="true"> <img class="mntl-truste-badge-image lazyloadwait" alt="Access TRUSTe's Enterprise Privacy Certification program" data-src="//privacy-policy.truste.com/privacy-seal/seal?rid=************************************" width="100" height="32">
    </a></div></div>
    </li></ul>
    </div> </div>
    <div class="loc bottom-content footer__bottom"><div id="mntl-dotdash-universal-nav_1-0" class="comp mntl-dotdash-universal-nav" data-tracking-container="true"> <div class="mntl-dotdash-universal-nav__content">
    <svg class="icon mntl-dotdash-universal-nav__logo ">
    <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#mntl-dotdash-universal-nav__logo" href="#mntl-dotdash-universal-nav__logo"></use>
    </svg>
    <div class="mntl-dotdash-universal-nav__wrapper">
    <div class="mntl-dotdash-universal-nav__text">
    Serious Eats
    is part of the <a href="https://www.dotdashmeredith.com" target="_blank" rel="noopener nofollow nocaes" class="mntl-dotdash-universal-nav__text--link">Dotdash Meredith</a>&nbsp;publishing&nbsp;family.
    </div>
    </div>
    </div>
    </div>
    </div> </div>
    </footer><svg class="is-hidden">
    <defs>
    <symbol id="mntl-sc-block-starrating-icon">
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
    <path d="M0,0v20h20V0H0z M14.2,12.2l1.1,6.3l-5.4-3.2l-5.1,3.2l1-6.3L1.4,8l5.9-0.8l2.6-5.8l2.7,5.8L18.5,8L14.2,12.2z"></path>
    </svg>
    </symbol>
    </defs>
    </svg>
    <!-- Google Tag Manager (Testing) -->
    <noscript><iframe src="//www.googletagmanager.com/ns.html?id=GTM-5P3SZGS" height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
    <!-- End Google Tag Manager --><script type="text/javascript" data-glb-js="bottom" src="/static/6.85.0/cache/eNqdU1FugzAMvdAiDrHfXcIkJvFmki4xpfT0M9B1jALqJiHFdt4zz3lJVQSEbNVCFMbqljHVpXrX77PDPLxUv0G60QkxCWExdUcspicJZhfdamDOEIkZTLE5Ma9RNrWnFDFKqSgK5ghcOWwwj_wpyOgOSNA0qggEDVP8MBn7TDKzFdVitriHOeiKl1kKXWfOdCgPxZ8GyXWME4jhOhg4UtyT86hrQPJBXgNEj29UBKPK3ireey2aeE61nlRAcBNrXLZwEc7ktZjijWK0sgi3OE3KrTKxL4yiI5tCPnanx8rBkH9hb8utO5EUR41GzWrX-T-Fj_T9339bk5GphNHOOTI26d2M8oSra-q-Tj96qy4UhGzDKt26WuPzgR6Gh71F6xrK9PIaxgvVjMdXkVrwE9xT88RsFnLqCvI9OOA48pSCccgwoFulX72uuxk.min.js" async="" defer="true" data-loaded="loaded"></script>
    <script type="text/javascript">
    Mntl.utilities.scriptsOnLoad(document.querySelectorAll('script[data-glb-js="bottom"]'), function() {
    var Mntl = window.Mntl || {};
    window.Mntl.affiliateLinkRewriter.setMappings( {
    DOC_ID: '5094396'
    ,SITE: 'seriouseats'
    ,REQUEST_ID: 'n40a218489f2649cabda4435c8f5b6f6822'
    }
    );window.Mntl.externalizeLinks.addPlugin(window.Mntl.affiliateLinkRewriter);
    window.Mntl.externalizeLinks.addPlugin(window?.Trx?.amazonAffiliateTagger);
    window.Mntl.externalizeLinks.init();(function(Mntl) {
    Mntl.utilities.readyOrDeferred((context) => {
    const relishScriptArgs = {
    key: "fad43e07-2ce3-4c04-97a2-ffd099ecbfd9",
    domain: "relish.com"
    };
    Mntl.Relish.init(relishScriptArgs, "200", context);
    });
    })(window.Mntl || {});
    (function (Mntl) {
    Mntl.digioh = {
    script: null,
    brandId: null,
    initialized: false,
    init() {
    if (this.initialized) return;
    const brandId = '55631c8a-65ef-454b-9544-3e8e173a3af3';
    const digiohFastActivation = 'false';
    const lightboxJs = digiohFastActivation === 'true' ? '/lightbox_speed.js' : '/lightbox_inline.js';
    // eslint-disable-next-line prefer-template
    const script = 'https://www.lightboxcdn.com/vendor/' + brandId + lightboxJs;
    this.brandId = brandId;
    this.script = script;
    this.initialized = true;
    },
    load(callback) {
    this.init();
    if (callback) {
    Mntl.utilities.loadExternalJS({ src: this.script }, callback);
    } else {
    Mntl.utilities.loadExternalJS({ src: this.script });
    }
    }
    };
    })(window.Mntl || {});
    Mntl.digiohTimerDelay = parseFloat('30') * 1000;
    });
    </script>
    <div id="onetrust-consent-sdk" class="" data-tracking-container="true">
    <div id="onetrust-banner-sdk" class="otFlat bottom ot-wo-title" tabindex="0"><div role="dialog" aria-label="Privacy"><div class="ot-sdk-container"><div class="ot-sdk-row"><div id="onetrust-group-container" class="ot-sdk-eight ot-sdk-columns"><div class="banner_logo"></div><div id="onetrust-policy"><div id="onetrust-policy-text">By clicking “Accept All Cookies”, you agree to the storing of cookies on your device to enhance site navigation, analyze site usage, and assist in our marketing efforts. </div></div></div><div id="onetrust-button-group-parent" class="ot-sdk-three ot-sdk-columns"><div id="onetrust-button-group"><button id="onetrust-pc-btn-handler" class=" cookie-setting-link">Cookies Settings</button> <button id="onetrust-accept-btn-handler">Accept All Cookies</button></div></div></div></div><!-- Close Button --><div id="onetrust-close-btn-container"><button class="onetrust-close-btn-handler onetrust-close-btn-ui banner-close-button ot-close-icon" style="background-image: url('https://cdn.cookielaw.org/logos/static/ot_close.svg')" aria-label="Close"></button></div><!-- Close Button END--></div></div>
    </div>
    <iframe src="https://i.liadm.com/s/c/a-07sk?duid=93ce4054916e--01jepakhgwarqy4p9vk51w34kp&amp;euns=0&amp;s=CoYBCgYI-QEQ3hkKBQgKEN4ZCgYIpAEQ3hkKBgjdARDeGQoGCPgBENoZCgYIiQEQ3hkKBQgJEN4ZCgYI4QEQ3hkKBgiBARDeGQoFCAwQ4xkKBgj1ARDeGQoJCP____8HEOMZCgUICxDeGQoGCKIBEN4ZCgYI_wEQ2xkKBgjSARDeGQoFCH4Q3hkSoAENiaTH7hKYAQoGCMoBEN4ZCgYI5AEQ3hkKBgiTARDaGQoGCMkBEN4ZCgYIpQEQ3hkKBgj0ARDaGQoGCJQBENoZCgYIzAEQ3BkKBgjGARDcGQoGCMcBENoZCgYImgEQ2xkKBgjnARDeGQoGCMgBEN4ZCgYI5QEQ3hkKBgjFARDeGQoGCKwBENoZCgYI5gEQ3hkKBgj-ARDeGQoGCOgBEN4ZEj8NTUiIQhI4CgYIygEQ3hkKBgiTARDaGQoGCMkBEN4ZCgYIxQEQ3hkKBgjGARDcGQoGCMcBENoZCgYIyAEQ3hkSPw3WJG-mEjgKBgjKARDeGQoGCJMBENoZCgYIyQEQ3hkKBgjFARDeGQoGCMYBENwZCgYIxwEQ2hkKBgjIARDeGQ&amp;version=v3.6.0&amp;cd=.seriouseats.com&amp;pv=f5d86ad5-6472-4973-8a96-024623bab9cc" style="display: none; width: 0px; height: 0px; border: 0px;"></iframe><script type="text/javascript" id="" charset="">function opinionStageIframeListener(){window.addEventListener("message",function(b){try{var c=JSON.parse(b.data),a=c.event,d=c.OS_data;"https://www.opinionstage.com"!==b.origin||"widgetLoaded"!==a&&"startEngagementClicked"!==a&&"cardOptionClicked"!==a&&"resultCalculated"!==a&&"leadFormSubmitted"!==a&&"closingCardPresented"!==a&&"retakeClicked"!==a||dataLayer.push({event:"opinionStage_"+a,os_data:d})}catch(e){}},!1)}opinionStageIframeListener();</script><script type="text/javascript" id="" charset="">function sweepsIframeListener(){window.addEventListener("message",function(a){a.origin==="https://hosted.pushplanet.com"&&a.data.event==="sweepstakesEvent"&&dataLayer.push({event:a.data.event,eventAction:a.data.eventAction,eventCategory:a.data.eventCategory,eventLabel:a.data.eventLabel,sweepId:a.data.sweepId,sweepstakesName:a.data.sweepstakesName,brandId:a.data.brandId,registrationSource:a.data.registrationSource,sponsorOptIn:a.data.sponsorOptIn,newsletterCount:a.data.newsletterCount})},!1)}sweepsIframeListener();</script><script type="text/javascript" id="" charset="">window.name.indexOf("Partytown")===0&&window._syncDL&&(window._syncDL(window.dataLayer),window.dataLayer._push=window.dataLayer.push,window.dataLayer.push=function(a){window.dataLayer._push(a);window._syncDL(window.dataLayer)});</script><script type="text/javascript" id="" charset="">function browserReportListener(){window.addEventListener("message",function(a){var c="https://"+window.location.hostname;if(a.origin!==c&&typeof a.data==="string")try{var b=JSON.parse(a.data);b.type==="BrowserReport"&&(a=b,dataLayer.push({event:"transmitNonInteractiveEvent",eventCategory:"Reporting API",eventAction:JSON.stringify(a.report),eventLabel:a.ad_id+"|"+a.advertiser_id+"|"+a.campaign_id+"|"+a.creative_id}))}catch(d){}},!1)}
    if(window.name.indexOf("Partytown")===0){var brjs=browserReportListener.toString();window.injectPTJS(brjs+";browserReportListener();")}else browserReportListener();</script><script type="text/javascript" id="" charset="">window.dataLayer=window.dataLayer||[];
    (function(e,a){function x(b){for(var c=0;c<b.length;c++)e.addEventListener?e.addEventListener(b[c],g):e.attachEvent&&e.attachEvent("on"+b[c],g);window.onbeforeunload=function(){a.events.push("pageunload");n()};e.addEventListener("visibilitychange",function(){"visible"===e.visibilityState?(k=!0,l=1):k=!1})}function g(b){b=b.type;var c=Date.now();a.start&&(a.firstTime=c,a.start=!1);a.events[a.events.length-1]!==b&&a.events.push(b);a.lastTime=c}function p(){return{start:!0,firstTime:0,lastTime:0,events:[]}}
    function n(){var b=y*l;if(0<a.events.length)if(gapMilliseconds=a.firstTime-m.lastTime,engagedTime=gapMilliseconds<h?a.lastTime-m.lastTime:a.lastTime-a.firstTime,events=a.events.join(", "),0<engagedTime&&engagedTime<=2*h){var c=events,f=engagedTime;var d=Date.now();d-=a.lastTime;q("timeEngaged",c,f,b+" Second Interval",b,d)}else 1>engagedTime||(c=events,f=engagedTime,d=Date.now(),d-=a.lastTime,q("timeEngagedError",c,f,b+" Second Interval",b,d));m=a;a=p();k&&(a.events.push("browseractive"),g({type:"pageload"}));
    l++}function z(){a=p();x(A);g({type:"pageload"});setInterval(function(){n()},h)}function q(b,c,f,d,r,B){c=window.dataLayer||[];var C="transmitNonInteractiveEvent";if("timeEngaged"==b){var t="Time";var u="Time Engaged";var v=d;var w=r}else"timeEngagedError"==b&&(t="Error",u="Time Engaged",v=d,w=r,f="");c.push({event:C,eventCategory:t,eventAction:u,eventLabel:v,eventValue:w,millisecondsEngaged:f,hitTimeOffset:B})}var h=3E4,y=Math.round(h/1E3),l=1,A=["mouseover","touchstart","keydown"],m={lastTime:0},
    k=!0;z()})(window.document);</script><iframe src="https://gum.criteo.com/syncframe?origin=publishertagids&amp;topUrl=www.seriouseats.com#{&quot;lwid&quot;:{&quot;origin&quot;:0},&quot;bundle&quot;:{&quot;value&quot;:&quot;fCjVc18xUENnVlZ6UGFYcWc4RmxyNWpQUUR6YyUyQk9RdSUyRkVPa1l4MjM1MkdHZGRTT3c5Vjh3NEJoUXBlUUFhTWo1bXR6V0dqYW9EN1ltRkNZa1JxcXQ1cEw1eTEzVEl2WVF3OHhMekJZQ0ZJTjNhQU1SZ2dmY0V1RzJ4bFlOS1BadXBOaGFPUXAlMkY3TXZiZTAzNVpDR1FYNVVRZyUyQiUyQjZHOHVvamVwUE03aTZaaVViVFlWRjVQVFdQUDlUMG5pR3NlZnVvNXF4RUlnNDI3d2dKVklzbUptNkFlZmhjRlBYJTJGc1hBbm9MRE1ya3U2SVJHM0RQbTYyckpYM3lyR3NuYnAlMkZ4UXFFdzAzcDJDelM5dmVqSWh1d3FubmNEUGxvd01wREVtRllmZ3dUcWVoaDQyRlNyZWE2JTJGUGt3ZWpsJTJGQ0Vra0FsYWljQg&quot;,&quot;origin&quot;:3},&quot;optout&quot;:{&quot;value&quot;:false,&quot;origin&quot;:0},&quot;tld&quot;:&quot;seriouseats.com&quot;,&quot;topUrl&quot;:&quot;www.seriouseats.com&quot;,&quot;version&quot;:159,&quot;cw&quot;:true,&quot;lsw&quot;:true,&quot;origin&quot;:&quot;publishertagids&quot;,&quot;requestId&quot;:&quot;0.427958421739415&quot;}" width="0" height="0" frameborder="0" sandbox="allow-scripts allow-same-origin" aria-hidden="true" title="Criteo GUM iframe" style="border-width: 0px; margin: 0px; display: none;"></iframe><iframe name="__launchpadLocator" style="display: none;"></iframe><iframe src="https://s.amazon-adsystem.com/iu3?cm3ppd=1&amp;d=dtb-pub&amp;csif=t&amp;dl=n-index_n-minuteMedia_n-Ogury_rx_n-MediaNet_ox-db5_smrt_cnv_n-cadent_n-sharethrough_pm-db5_ym_rbd_ppt_n-vmg_n-Vidazoo_kg_n-nativo_an-db5_sovrn_n-Rise_3lift" style="display: none;"></iframe><iframe src="https://www.google.com/recaptcha/api2/aframe" width="0" height="0" style="display: none;"></iframe>
    <script type="text/javascript" id="" charset="">function loadComscore(){window._comscore=window._comscore||[];window._comscore.push({c1:"2",c2:"6036459",cs_fpid:"441bdb9e-1da2-45ac-8700-3c21d36989ec",cs_fpit:"lo",cs_fpdm:"*null",cs_fpdt:"*null",options:{enableFirstPartyCookie:!0}});var a=document.createElement("script"),b=document.getElementsByTagName("script")[0];a.async=!0;a.src="https://sb.scorecardresearch.com/cs/6036459/beacon.js";b.parentNode.insertBefore(a,b)}
    if(window.name.indexOf("Partytown")===0){var js=loadComscore.toString();window.injectPTJS(js+";loadComscore();")}else loadComscore();</script>
    
    <noscript>
      <img src="https://sb.scorecardresearch.com/p?c1=2&amp;c2=6036459&amp;cs_fpid=441bdb9e-1da2-45ac-8700-3c21d36989ec}&amp;cs_fpit=lo&amp;cs_fpdm=*null&amp;cs_fpdt=*null&amp;cv=4.4.0&amp;cj=1">
    </noscript>
    
    <script id="" text="" charset="" type="text/javascript" src="https://ak.sail-horizon.com/spm/spm.v1.min.js" data-loaded="loaded"></script><script type="text/javascript" id="" charset="">var pixel={init:function(){function m(a){try{return decodeURIComponent(a)}catch(c){}return a}function r(a,c,d){try{return dataLayer.find(function(b){return b.event==a})[c]||d}catch(b){return d}}function t(a){return!a||"string"===typeof a&&(a=a.trim(),"null"===a||"false"===a||"undefined"===a||"{}"===a||"[]"===a||"0"===a||'[""]'===a||'"null"'===a||'"false"'===a||'"undefined"'===a||"'null'"===a||"'false'"===a||"'undefined'"===a||"''"===a.replace(/\s+/g,"")||'""'===a.replace(/\s+/g,""))?!0:!1}function p(a){return"string"===
    typeof a&&a.match(/^[a-z0-9]{8}-[a-z0-9]{4}-4[a-z0-9]{3}-[a-z0-9]{4}-[a-z0-9]{12}$/g)}function u(){var a=(new Date).getTime();return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(c){var d=(a+16*Math.random())%16|0;a=Math.floor(a/16);return("x"===c?d:d&3|8).toString(16)})}function h(a,c,d){if(!a)return null;c||(c=function(g){return null!=g});var b=r("unifiedPageview",a);if(c(b))return b;b=v[a];if(c(b))return b;for(var e=document.cookie.split(";"),f=0;f<e.length;f++)if(b=e[f].trim(),
    0===b.indexOf(a+"\x3d")&&(b=m(b.substring(a.length+1)),c(b)))return b;e=window.location.search.replace(/^\?/,"").split("\x26");for(f=0;f<e.length;f++)if(b=e[f].trim(),0===b.indexOf(a+"\x3d")&&(b=m(b.substring(a.length+1)),c(b)))return b;return d}function q(a){if(a){a={};a.v=w;a.type="mdd";a.globalTI_SID=h("muid",p);a.request_id=x;a.url=window.location.href.toLowerCase();a.ref=document.referrer===a.url?"":document.referrer;a.domain=window.location.hostname.toLowerCase();a.ua=window.navigator.userAgent?
    window.navigator.userAgent.toLowerCase():"unknown";a._ga=h("_ga");a._gid=h("_gid");a.hid=h("hid");a.utime0=y;a.utime=Date.now();var c=m(h("OptanonConsent"));var d="\x26",b={};if("string"===typeof c)for(c=c.split(d),d=c.length-1;0<=d;d--){var e=c[d].trim(),f=e.indexOf("\x3d");e=[e.slice(0,f),e.slice(f+1)];b[e[0]]=m(e[1]||"")}c=b;c&&(a.compliance=c.groups);a.tmog=h("TMog");a.mint=h("Mint");a.page_id=h("internalRequestId");a.lotame_id=h("_cc_id");if(p(a.globalTI_SID)){c=[];for(k in a)if(f=a[k],!t(f)){b=
    c;d=b.push;e=k+"\x3d";a:{try{var g=encodeURIComponent(f);break a}catch(A){}g=f}d.call(b,e+g)}var k=c.join("\x26");k=z+"?"+k;g=document.createElement("img");g.setAttribute("src",k);g.setAttribute("height","1");g.setAttribute("width","1");g.setAttribute("alt","");g.setAttribute("display","none");g.setAttribute("class","is-hidden pixel_1x1");document.body.appendChild(g)}}}var w="ddm_0.0.2",z="https://d9jj3mjthpub.cloudfront.net/x.gif",y=Date.now(),v=function(){var a={setItem:function(){}};try{window.localStorage.setItem("__test",
    1),window.localStorage.removeItem("__test"),a=window.localStorage}catch(c){}return a}(),x=u();if("undefined"!==typeof document.hidden){var n="hidden";var l="visibilitychange"}else"undefined"!==typeof document.msHidden?(n="msHidden",l="msvisibilitychange"):"undefined"!==typeof document.webkitHidden&&(n="webkitHidden",l="webkitvisibilitychange");addEventListener(l,function(){q(document[n])});l="onpagehide"in self?"pagehide":"unload";addEventListener(l,function(){q(!0)},{capture:!0})}};pixel.init();</script><script type="text/javascript" id="" charset="">/*
     ************************************************* !*\
      !*** ./src/rprofiler/rprofiler.ts + 30 modules ***!
      \*************************************************/
    var googletag=window.googletag||{};googletag.cmd=googletag.cmd||[];var raw$Var=JSON.stringify(google_tag_manager["rm"]["13527222"](44));
    function loadCatchpoint(){(function(){function getResourceMetrics(){var resources=performance.getEntriesByType("resource");var resourceMetrics={transferSize:0,resourceCount:resources.length};for(var i=0;i<resources.length;i++)if("transferSize"in resources[i])resourceMetrics.transferSize+=resources[i].transferSize;return resourceMetrics}function init(){var adsRendered=0;var adsViewable=0;var clsTotal=1E-4;var rawABTestInfo=raw$Var;window.RProfiler.addInfo("tracepoint","rid","n40a218489f2649cabda4435c8f5b6f6822");
    window.RProfiler.addInfo("tracepoint","sid","n4c4f3e711ef341e39f43c983724bd55221");window.RProfiler.addInfo("pageGroup","homeTemplate");window.debug.log("rawABTestInfo",rawABTestInfo);Object.keys(rawABTestInfo).forEach(function(abTrackingId){var abTestInfo;if(abTrackingId.indexOf("99")===-1){window.debug.log("RUM Variation tracking: "+abTrackingId+"\x3d"+rawABTestInfo[abTrackingId]);abTestInfo=rawABTestInfo[abTrackingId].split(" | ");window.RProfiler.addInfo("tracepoint","ab_"+abTestInfo[0].toLowerCase(),
    abTrackingId+" ("+abTestInfo[2]+")")}});googletag.cmd.push(function(){googletag.pubads().addEventListener("slotRenderEnded",function(){adsRendered++;window.debug.log("ad: slot rendered "+arguments[0].slot.getSlotElementId())});googletag.pubads().addEventListener("impressionViewable",function(){adsViewable++;window.debug.log("ad: slot viewable "+arguments[0].slot.getSlotElementId())})});var po=new PerformanceObserver(function(list){list.getEntries().forEach(function(layoutShift){window.debug.log("layoutShift",
    layoutShift);if(!layoutShift.hadRecentInput)clsTotal+=layoutShift.value})});po.observe({type:"layout-shift",buffered:true});window.addEventListener("beforeunload",function(e){var resourceMetrics=getResourceMetrics();window.RProfiler.addInfo("indicator","adsrendered",adsRendered);window.RProfiler.addInfo("indicator","adsviewable",adsViewable);window.RProfiler.addInfo("indicator","cls",parseFloat(parseFloat(clsTotal).toFixed(4)));window.RProfiler.addInfo("indicator","resourcecount",resourceMetrics.resourceCount);
    window.RProfiler.addInfo("indicator","resourcetransfersize",resourceMetrics.transferSize);window.RProfiler.addInfo("conversion",adsViewable,adsRendered);window.debug.log("slots rendered: "+adsRendered);window.debug.log("slots viewable: "+adsViewable);window.debug.log("cls",parseFloat(parseFloat(clsTotal).toFixed(4)))})}if(!window.RProfiler)window.addEventListener("GlimpseLoaded",init);else init()})();(function(){var c={},yt,v,h,r,t,u,ii;!function(){c.d=function(n,t){for(var i in t)c.o(t,i)&&!c.o(n,
    i)&&Object.defineProperty(n,i,{enumerable:!0,get:t[i]})}}();!function(){c.o=function(n,t){return Object.prototype.hasOwnProperty.call(n,t)}}();!function(){c.r=function(n){typeof Symbol!="undefined"&&Symbol.toStringTag&&Object.defineProperty(n,Symbol.toStringTag,{value:"Module"});Object.defineProperty(n,"__esModule",{value:!0})}}();yt={};c.r(yt);c.d(yt,{"default":function(){return no}});var g,fi,pt,y=function(){var n=self.performance&&performance.getEntriesByType&&performance.getEntriesByType("navigation")[0];
    if(n&&n.responseStart>0&&n.responseStart<performance.now())return n},nt=function(n){if("loading"===document.readyState)return"loading";var t=y();if(t){if(n<t.domInteractive)return"loading";if(0===t.domContentLoadedEventStart||n<t.domContentLoadedEventStart)return"dom-interactive";if(0===t.domComplete||n<t.domComplete)return"dom-content-loaded"}return"complete"},hu=function(n){var t=n.nodeName;return 1===n.nodeType?t.toLowerCase():t.toUpperCase().replace(/^#/,"")},ot=function(n,t){var r="",i,u;try{for(;n&&
    9!==n.nodeType;){if(i=n,u=i.id?"#"+i.id:hu(i)+(i.classList&&i.classList.value&&i.classList.value.trim()&&i.classList.value.trim().length?"."+i.classList.value.trim().replace(/\s+/g,"."):""),r.length+u.length>(t||100)-1)return r||u;if(r=r?u+"\x3e"+r:u,i.id)break;n=i.parentNode}}catch(n){}return r},ei=-1,oi=function(){return ei},l=function(n){addEventListener("pageshow",function(t){t.persisted&&(ei=t.timeStamp,n(t))},!0)},st=function(){var n=y();return n&&n.activationStart||0},f=function(n,t){var r=
    y(),i="navigate";return oi()>=0?i="back-forward-cache":r&&(document.prerendering||st()>0?i="prerender":document.wasDiscarded?i="restore":r.type&&(i=r.type.replace(/_/g,"-"))),{name:n,value:void 0===t?-1:t,rating:"good",delta:0,entries:[],id:"v4-".concat(Date.now(),"-").concat(Math.floor(8999999999999*Math.random())+1E12),navigationType:i}},a=function(n,t,i){try{if(PerformanceObserver.supportedEntryTypes.includes(n)){var r=new PerformanceObserver(function(n){Promise.resolve().then(function(){t(n.getEntries())})});
    return r.observe(Object.assign({type:n,buffered:!0},i||{})),r}}catch(n){}},e=function(n,t,i,r){var u,f;return function(e){t.value>=0&&(e||r)&&((f=t.value-(u||0))||void 0===u)&&(u=t.value,t.delta=f,t.rating=function(n,t){return n>t[1]?"poor":n>t[0]?"needs-improvement":"good"}(t.value,i),n(t))}},wt=function(n){requestAnimationFrame(function(){return requestAnimationFrame(function(){return n()})})},tt=function(n){document.addEventListener("visibilitychange",function(){"hidden"===document.visibilityState&&
    n()})},ht=function(n){var t=!1;return function(){t||(n(),t=!0)}},p=-1,si=function(){return"hidden"!==document.visibilityState||document.prerendering?1/0:0},ct=function(n){"hidden"===document.visibilityState&&p>-1&&(p="visibilitychange"===n.type?n.timeStamp:0,cu())},hi=function(){addEventListener("visibilitychange",ct,!0);addEventListener("prerenderingchange",ct,!0)},cu=function(){removeEventListener("visibilitychange",ct,!0);removeEventListener("prerenderingchange",ct,!0)},bt=function(){return p<
    0&&(p=si(),hi(),l(function(){setTimeout(function(){p=si();hi()},0)})),{get firstHiddenTime(){return p}}},it=function(n){document.prerendering?addEventListener("prerenderingchange",function(){return n()},!0):n()},ci=[1800,3E3],li=function(n,t){t=t||{};it(function(){var r,o=bt(),i=f("FCP"),u=a("paint",function(n){n.forEach(function(n){"first-contentful-paint"===n.name&&(u.disconnect(),n.startTime<o.firstHiddenTime&&(i.value=Math.max(n.startTime-st(),0),i.entries.push(n),r(!0)))})});u&&(r=e(n,i,ci,t.reportAllChanges),
    l(function(u){i=f("FCP");r=e(n,i,ci,t.reportAllChanges);wt(function(){i.value=performance.now()-u.timeStamp;r(!0)})}))})},ai=[.1,.25],vi=function(n,t){!function(n,t){t=t||{};li(ht(function(){var i,r=f("CLS",0),u=0,o=[],s=function(n){n.forEach(function(n){if(!n.hadRecentInput){var t=o[0],i=o[o.length-1];u&&n.startTime-i.startTime<1E3&&n.startTime-t.startTime<5E3?(u+=n.value,o.push(n)):(u=n.value,o=[n])}});u>r.value&&(r.value=u,r.entries=o,i())},h=a("layout-shift",s);h&&(i=e(n,r,ai,t.reportAllChanges),
    tt(function(){s(h.takeRecords());i(!0)}),l(function(){u=0;r=f("CLS",0);i=e(n,r,ai,t.reportAllChanges);wt(function(){return i()})}),setTimeout(i,0))}))}(function(t){var i=function(n){var r,u={},t,i;return n.entries.length&&(t=n.entries.reduce(function(n,t){return n&&n.value>t.value?n:t}),t&&t.sources&&t.sources.length&&(i=(r=t.sources).find(function(n){return n.node&&1===n.node.nodeType})||r[0],i&&(u={largestShiftTarget:ot(i.node),largestShiftTime:t.startTime,largestShiftValue:t.value,largestShiftSource:i,
    largestShiftEntry:t,loadState:nt(t.startTime)}))),Object.assign(n,{attribution:u})}(t);n(i)},t)},to=function(n,t){li(function(t){var i=function(n){var r={timeToFirstByte:0,firstByteToFCP:n.value,loadState:nt(oi())},t,u,f,i;return n.entries.length&&(t=y(),u=n.entries[n.entries.length-1],t&&(f=t.activationStart||0,i=Math.max(0,t.responseStart-f),r={timeToFirstByte:i,firstByteToFCP:n.value-i,loadState:nt(n.entries[0].startTime),navigationEntry:t,fcpEntry:u})),Object.assign(n,{attribution:r})}(t);n(i)},
    t)},yi=0,kt=1/0,lt=0,lu=function(n){n.forEach(function(n){n.interactionId&&(kt=Math.min(kt,n.interactionId),lt=Math.max(lt,n.interactionId),yi=lt?(lt-kt)/7+1:0)})},au=function(){"interactionCount"in performance||g||(g=a("event",lu,{type:"event",buffered:!0,durationThreshold:0}))},o=[],rt=new Map,pi=0,vu=function(){return(g?yi:performance.interactionCount||0)-pi},wi=[],yu=function(n){var r,t,i;(wi.forEach(function(t){return t(n)}),n.interactionId||"first-input"===n.entryType)&&(r=o[o.length-1],t=rt.get(n.interactionId),
    (t||o.length<10||n.duration>r.latency)&&(t?n.duration>t.latency?(t.entries=[n],t.latency=n.duration):n.duration===t.latency&&n.startTime===t.entries[0].startTime&&t.entries.push(n):(i={id:n.interactionId,latency:n.duration,entries:[n]},rt.set(i.id,i),o.push(i)),o.sort(function(n,t){return t.latency-n.latency}),o.length>10&&o.splice(10).forEach(function(n){return rt["delete"](n.id)})))},dt=function(n){var i=self.requestIdleCallback||self.setTimeout,t=-1;return n=ht(n),"hidden"===document.visibilityState?
    n():(t=i(n),tt(n)),t},bi=[200,500],pu=function(n,t){"PerformanceEventTiming"in self&&"interactionId"in PerformanceEventTiming.prototype&&(t=t||{},it(function(){var u;au();var r,i=f("INP"),h=function(n){dt(function(){n.forEach(yu);var u,t=(u=Math.min(o.length-1,Math.floor(vu()/50)),o[u]);t&&t.latency!==i.value&&(i.value=t.latency,i.entries=t.entries,r())})},s=a("event",h,{durationThreshold:null!==(u=t.durationThreshold)&&void 0!==u?u:40});r=e(n,i,bi,t.reportAllChanges);s&&(s.observe({type:"first-input",
    buffered:!0}),tt(function(){h(s.takeRecords());r(!0)}),l(function(){pi=0;o.length=0;rt.clear();i=f("INP");r=e(n,i,bi,t.reportAllChanges)}))}))},w=[],b=new Map,k=[],gt=new WeakMap,d=new Map,ni=-1,wu=function(n){w=w.concat(n);ki()},ki=function(){ni<0&&(ni=dt(bu))},bu=function(){var r,n,t,i;for(d.size>10&&d.forEach(function(n,t){rt.has(t)||d["delete"](t)}),k=k.slice(-50),r=new Set(k.concat(o.map(function(n){return gt.get(n.entries[0])}))),b.forEach(function(n,t){r.has(t)||b["delete"](t)}),n=new Set,
    b.forEach(function(t){gi(t.startTime,t.processingEnd).forEach(function(t){n.add(t)})}),t=0;t<50;t++){if(i=w[w.length-1-t],!i||i.startTime<pt)break;n.add(i)}w=Array.from(n);ni=-1};wi.push(function(n){n.interactionId&&n.target&&!d.has(n.interactionId)&&d.set(n.interactionId,n.target)},function(n){var r,i=n.startTime+n.duration,u,t;for(pt=Math.max(pt,n.processingEnd),u=k.length-1;u>=0;u--)if(r=k[u],Math.abs(i-r)<=8){t=b.get(r);t.startTime=Math.min(n.startTime,t.startTime);t.processingStart=Math.min(n.processingStart,
    t.processingStart);t.processingEnd=Math.max(n.processingEnd,t.processingEnd);t.entries.push(n);i=r;break}i!==r&&(k.push(i),b.set(i,{startTime:n.startTime,processingStart:n.processingStart,processingEnd:n.processingEnd,entries:[n]}));(n.interactionId||"first-input"===n.entryType)&&gt.set(n,i);ki()});var s,ut,di,at,gi=function(n,t){for(var i,r=[],u=0;i=w[u];u++)if(!(i.startTime+i.duration<n)){if(i.startTime>t)break;r.push(i)}return r},nr=function(n,t){fi||(fi=a("long-animation-frame",wu));pu(function(t){var i=
    function(n){var t=n.entries[0],h=gt.get(t),r=b.get(h),u=t.processingStart,i=r.processingEnd,c=r.entries.sort(function(n,t){return n.processingStart-t.processingStart}),f=gi(t.startTime,i),e=n.entries.find(function(n){return n.target}),o=e&&e.target||d.get(t.interactionId),l=[t.startTime+t.duration,i].concat(f.map(function(n){return n.startTime+n.duration})),s=Math.max.apply(Math,l),a={interactionTarget:ot(o),interactionTargetElement:o,interactionType:t.name.startsWith("key")?"keyboard":"pointer",
    interactionTime:t.startTime,nextPaintTime:s,processedEventEntries:c,longAnimationFrameEntries:f,inputDelay:u-t.startTime,processingDuration:i-u,presentationDelay:Math.max(s-i,0),loadState:nt(t.startTime)};return Object.assign(n,{attribution:a})}(t);n(i)},t)},tr=[2500,4E3],ti={},ir=function(n,t){!function(n,t){t=t||{};it(function(){var r,h=bt(),i=f("LCP"),s=function(n){t.reportAllChanges||(n=n.slice(-1));n.forEach(function(n){n.startTime<h.firstHiddenTime&&(i.value=Math.max(n.startTime-st(),0),i.entries=
    [n],r())})},u=a("largest-contentful-paint",s),o;u&&(r=e(n,i,tr,t.reportAllChanges),o=ht(function(){ti[i.id]||(s(u.takeRecords()),u.disconnect(),ti[i.id]=!0,r(!0))}),["keydown","click"].forEach(function(n){addEventListener(n,function(){return dt(o)},!0)}),tt(o),l(function(u){i=f("LCP");r=e(n,i,tr,t.reportAllChanges);wt(function(){i.value=performance.now()-u.timeStamp;ti[i.id]=!0;r(!0)})}))})}(function(t){var i=function(n){var u={timeToFirstByte:0,resourceLoadDelay:0,resourceLoadDuration:0,elementRenderDelay:n.value},
    r;if(n.entries.length&&(r=y(),r)){var f=r.activationStart||0,t=n.entries[n.entries.length-1],i=t.url&&performance.getEntriesByType("resource").filter(function(n){return n.name===t.url})[0],e=Math.max(0,r.responseStart-f),o=Math.max(e,i?(i.requestStart||i.startTime)-f:0),s=Math.max(o,i?i.responseEnd-f:0),h=Math.max(s,t.startTime-f);u={element:ot(t.element),timeToFirstByte:e,resourceLoadDelay:o-e,resourceLoadDuration:s-o,elementRenderDelay:h-s,navigationEntry:r,lcpEntry:t};t.url&&(u.url=t.url);i&&(u.lcpResourceEntry=
    i)}return Object.assign(n,{attribution:u})}(t);n(i)},t)},rr=[800,1800],ku=function g(n){document.prerendering?it(function(){return g(n)}):"complete"!==document.readyState?addEventListener("load",function(){return g(n)},!0):setTimeout(n,0)},du=function(n,t){t=t||{};var i=f("TTFB"),r=e(n,i,rr,t.reportAllChanges);ku(function(){var u=y();u&&(i.value=Math.max(u.responseStart-st(),0),i.entries=[u],r(!0),l(function(){i=f("TTFB",0);(r=e(n,i,rr,t.reportAllChanges))(!0)}))})},io=function(n,t){du(function(t){var i=
    function(n){var r={waitingDuration:0,cacheDuration:0,dnsDuration:0,connectionDuration:0,requestDuration:0};if(n.entries.length){var t=n.entries[0],i=t.activationStart||0,u=Math.max((t.workerStart||t.fetchStart)-i,0),f=Math.max(t.domainLookupStart-i,0),e=Math.max(t.connectStart-i,0),o=Math.max(t.connectEnd-i,0);r={waitingDuration:u,cacheDuration:f-u,dnsDuration:e-f,connectionDuration:o-e,requestDuration:n.value-o,navigationEntry:t}}return Object.assign(n,{attribution:r})}(t);n(i)},t)},ft={passive:!0,
    capture:!0},gu=new Date,ur=function(n,t){s||(s=t,ut=n,di=new Date,er(removeEventListener),fr())},fr=function(){if(ut>=0&&ut<di-gu){var n={entryType:"first-input",name:s.type,target:s.target,cancelable:s.cancelable,startTime:s.timeStamp,processingStart:s.timeStamp+ut};at.forEach(function(t){t(n)});at=[]}},nf=function(n){if(n.cancelable){var t=(n.timeStamp>1E12?new Date:performance.now())-n.timeStamp;"pointerdown"==n.type?function(n,t){var i=function(){ur(n,t);u()},r=function(){u()},u=function(){removeEventListener("pointerup",
    i,ft);removeEventListener("pointercancel",r,ft)};addEventListener("pointerup",i,ft);addEventListener("pointercancel",r,ft)}(t,n):ur(t,n)}},er=function(n){["mousedown","keydown","touchstart","pointerdown"].forEach(function(t){return n(t,nf,ft)})},or=[100,300],tf=function(n,t){t=t||{};it(function(){var r,c=bt(),i=f("FID"),o=function(n){n.startTime<c.firstHiddenTime&&(i.value=n.processingStart-n.startTime,i.entries.push(n),r(!0))},h=function(n){n.forEach(o)},u=a("first-input",h);r=e(n,i,or,t.reportAllChanges);
    u&&(tt(ht(function(){h(u.takeRecords());u.disconnect()})),l(function(){var u;i=f("FID");r=e(n,i,or,t.reportAllChanges);at=[];ut=-1;s=null;er(addEventListener);u=o;at.push(u);fr()}))})},ro=function(n,t){tf(function(t){var i=function(n){var t=n.entries[0],i={eventTarget:ot(t.target),eventType:t.name,eventTime:t.startTime,eventEntry:t,loadState:nt(t.startTime)};return Object.assign(n,{attribution:i})}(t);n(i)},t)},et;(function(n){n.Load="load";n.BeforeUnload="beforeunload";n.Abort="abort";n.Error="error";
    n.Unload="unload"})(et||(et={})),function(n){n[n.None=0]="None";n[n.Loading=1]="Loading";n[n.Complete=2]="Complete";n[n.DomInteractive=3]="DomInteractive";n[n.DomContentLoaded=4]="DomContentLoaded"}(v||(v={})),function(n){n[n.Focus=0]="Focus";n[n.Blur=1]="Blur"}(h||(h={})),function(n){n[n.OnLoad=0]="OnLoad";n[n.OnBeforeUnload=1]="OnBeforeUnload";n[n.OnAbort=2]="OnAbort";n[n.Flush=3]="Flush"}(r||(r={})),function(n){n[n.DNS=0]="DNS";n[n.Connect=1]="Connect";n[n.Load=2]="Load";n[n.Wait=3]="Wait";n[n.Start=
    4]="Start";n[n.Redirect=5]="Redirect";n[n.Duration=6]="Duration";n[n.SSL=7]="SSL";n[n.TransferSize=8]="TransferSize"}(t||(t={})),function(n){n.UserId="u";n.SessionId="s";n.SessionTime="t";n.PageViewCount="c";n.UrlCheckSum="k";n.PostFlag="f"}(u||(u={})),function(n){n[n.GET=0]="GET";n[n.POST=1]="POST";n[n.HEAD=2]="HEAD";n[n.DELETE=3]="DELETE";n[n.OPTIONS=4]="OPTIONS";n[n.PUT=5]="PUT";n[n.TRACE=6]="TRACE";n[n.CONNECT=7]="CONNECT"}(ii||(ii={}));var rf=function(){function n(n,t,i,r){var u=this;this.getPerformanceTimings=
    function(n){u.connect=n.connectEnd-n.connectStart;u.dns=n.domainLookupEnd-n.domainLookupStart;u.duration=n.duration;u.load=n.responseEnd-n.responseStart;u.wait=n.responseStart-n.requestStart;u.start=n.startTime;u.redirect=n.redirectEnd-n.redirectStart;n.secureConnectionStart&&(u.ssl=n.connectEnd-n.secureConnectionStart)};this.url=n;this.method=t;this.isAsync=i;this.open=r}return n}(),sr=rf,uf=function(){function n(){var t=this;this.fetchRequests=[];this.fetchEntriesIndices={};this.compareEntriesDelay=
    100;this.hasPerformance=typeof performance=="object"&&typeof window.performance.now=="function"&&typeof window.performance.getEntriesByType=="function";this.captureFetchRequests=function(){var n=[],i=t,r=function(n){return n},u=function(n){return Promise.reject(n)};window.fetch&&(window.fetch=function(t){return function(){for(var o,f,s=[],e=0;e<arguments.length;e++)s[e]=arguments[e];return o=0,f=Promise.resolve(s),f=f.then(function(t){var r,u={},e,f;if(t.length&&t.length>=1)r=t[0],t.length>1&&(u=
    t[1]);else return[];return e="GET",u.method&&(e=u.method),o=n.length,f="",f=typeof r!="object"||!r?r:Array.isArray(r)&&r.length>0?r[0]:r.url,f&&n.push(new sr(f,e,!0,i.now())),[r,u]},r),f=f.then(function(n){return t.apply(void 0,n)}),f.then(function(t){var r=n[o],u=i.fetchRequests;return i.processPerformanceEntries(r,u),t},u)}}(window.fetch))};this.captureFetchRequests();n.startAjaxCapture(this)}return n.prototype.getAjaxRequests=function(){return this.fetchRequests},n.prototype.clear=function(){this.fetchRequests=
    []},n.prototype.now=function(){return this.hasPerformance?window.performance.now():(new Date).getTime()},n.prototype.processPerformanceEntries=function(n,t){var i=this;setTimeout(function(){var f,o,s,h,e;if(i.hasPerformance){var u=n.url,r=[],c=performance.getEntriesByType("resource");for(f=0,o=c;f<o.length;f++)s=o[f],s.name===u&&r.push(s);if(t.push(n),r.length!==0){if(i.fetchEntriesIndices[u]||(i.fetchEntriesIndices[u]=[]),r.length===1){n.getPerformanceTimings(r[0]);i.fetchEntriesIndices[u].push(0);
    return}h=i.fetchEntriesIndices[u];for(e in r)if(h.indexOf(e)===-1){n.getPerformanceTimings(r[e]);h.push(e);return}n.getPerformanceTimings(r[0])}}},i.compareEntriesDelay)},n.startAjaxCapture=function(n){var t=XMLHttpRequest.prototype,r=t.open,u=t.send,i=[];n.hasPerformance&&typeof window.performance.setResourceTimingBufferSize=="function"&&window.performance.setResourceTimingBufferSize(300);t.open=function(t,u,f,e,o){this.rpIndex=i.length;i.push(new sr(u,t,f,n.now()));r.call(this,t,u,f===!1?!1:!0,
    e,o)};t.send=function(t){var r=this,e=this.onreadystatechange,f;(this.onreadystatechange=function(t){var u=i[r.rpIndex],o,f;if(u){o=r.readyState;f=!!(r.response&&r.response!==null&&r.response!==undefined);switch(o){case 1:u.connectionEstablished=n.now();break;case 2:u.requestReceived=n.now();break;case 3:u.processingTime=n.now();break;case 4:u.complete=n.now();switch(r.responseType){case "text":case "":typeof r.responseText=="string"&&(u.responseSize=r.responseText.length);break;case "json":f&&typeof r.response.toString==
    "function"&&(u.responseSize=r.response.toString().length);break;case "arraybuffer":f&&typeof r.response.byteLength=="number"&&(u.responseSize=r.response.byteLength);break;case "blob":f&&typeof r.response.size=="number"&&(u.responseSize=r.response.size)}n.processPerformanceEntries(u,n.fetchRequests)}typeof e=="function"&&e.call(r,t)}},f=i[this.rpIndex],f)&&(t&&!isNaN(t.length)&&(f.sendSize=t.length),f.send=n.now(),u.call(this,t))}},n}(),ff=uf,ef=function(){function n(){this.events=[];this.hasAttachEvent=
    !!window.attachEvent}return n.prototype.add=function(n,t,i){this.events.push({type:n,target:t,func:i});this.hasAttachEvent?t.attachEvent("on"+n,i):t.addEventListener(n,i,!1)},n.prototype.remove=function(n,t,i){this.hasAttachEvent?t.detachEvent(n,i):t.removeEventListener(n,i,!1);var r=this.events.indexOf({type:n,target:t,func:i});r!==1&&this.events.splice(r,1)},n.prototype.clear=function(){for(var n,i=this.events,t=0;t<i.length;t++)n=i[t],this.remove(n.type,n.target,n.func);this.events=[]},n}(),hr=
    ef,of=function(){function n(){var n=this;this.hiddenStrings=["hidden","msHidden","webkitHidden","mozHidden"];this.visibilityStrings=["visibilitychange","msvisibilitychange","webkitvisibilitychange","mozvisibilitychange"];this.captureSoftNavigation=!1;this.hidden="hidden";this.visibilityChange="visibilitychange";this.visibilityEvents=[];this.eventManager=new hr;this.engagementTimeIntervalMs=1E3;this.engagementTime=0;this.firstEngagementTime=0;this.lastEventTimeStamp=0;this.timeoutId=undefined;this.startTime=
    (new Date).getTime();this.now=function(){return(new Date).getTime()};this.startVisibilityCapture=function(){n.initializeVisibilityProperties();document.addEventListener(n.visibilityChange,n.captureFocusEvent,!1)};this.initializeVisibilityProperties=function(){for(var r=n.hiddenStrings,i=0,t=0;t<r.length;t++)typeof document[r[t]]!="undefined"&&(i=t);n.visibilityChange=n.visibilityStrings[i];n.hidden=n.hiddenStrings[i]};this.captureFocusEvent=function(){n.updateVisibilityChangeTime();document[n.hidden]||
    n.captureEngagementTime()};this.updateVisibilityChangeTime=function(){document[n.hidden]?n.captureVisibilityEvent(h.Blur):n.captureVisibilityEvent(h.Focus)};this.onBlur=function(){n.captureVisibilityEvent(h.Blur)};this.onFocus=function(){n.captureVisibilityEvent(h.Focus)};this.captureVisibilityEvent=function(t){n.visibilityEvents.push({type:t,time:n.now()})};this.captureEngagementTime=function(t){if(t===void 0&&(t=!0),!n.lastEventTimeStamp){n.engagementTime=n.engagementTimeIntervalMs;n.lastEventTimeStamp=
    n.now();return}var i=n.now()-n.lastEventTimeStamp;if(n.lastEventTimeStamp=n.now(),t&&n.firstEngagementTime===0&&(n.firstEngagementTime=n.now()),i>0&&i<n.engagementTimeIntervalMs){clearTimeout(n.timeoutId);n.engagementTime+=i;return}n.startTimer()};this.captureMouseMove=function(){n.captureEngagementTime(!1)};this.startTimer=function(){n.timeoutId=window.setTimeout(function(){n.engagementTime+=n.engagementTimeIntervalMs},n.engagementTimeIntervalMs)};this.getFocusAwayTime=function(){var i=n.visibilityEvents,
    t=-1,s,c,o;if(i.length===0)return 0;for(var r=t,u=0,f=t,e=0;u<i.length;)i[u].type===h.Blur&&r===t&&(r=u),s=f===t&&r!==t,i[u].type===h.Focus&&s&&(f=u),c=r!==t&&f!==t,c&&(o=i[f].time-i[r].time,o>0&&(e+=o),r=t,f=t),u=u+1;return r===i.length-1&&(e+=n.now()-i[r].time),e};this.getEngagementTime=function(){return n.engagementTime};this.getStartTime=function(){return n.startTime};this.getFirstEngagementTime=function(){return n.firstEngagementTime};this.startSoftNavigationCapture=function(){n.captureSoftNavigation=
    !0};this.resetSoftNavigationCapture=function(){n.resetEngagementMetrics();n.visibilityEvents=[]};this.resetEngagementMetrics=function(){n.engagementTime=0;n.lastEventTimeStamp=n.now();n.firstEngagementTime=0};this.clear=function(){n.eventManager.clear()};this.captureEngagementTime(!1);this.eventManager.add("scroll",document,this.captureEngagementTime);this.eventManager.add("resize",window,this.captureEngagementTime);this.eventManager.add("mouseup",document,this.captureEngagementTime);this.eventManager.add("keyup",
    document,this.captureEngagementTime);this.eventManager.add("mousemove",document,this.captureMouseMove);this.eventManager.add("focus",window,this.onFocus);this.eventManager.add("blur",window,this.onBlur);this.eventManager.add("focus",document,this.onFocus);this.eventManager.add("blur",document,this.onBlur)}return n}(),sf=of,hf=function(){function n(n,t,i){this.count=0;this.message=n;this.url=t;this.lineNumber=i}return n.createText=function(n,t,i){return[n,t,i].join(":")},n.prototype.getText=function(){return n.createText(this.message,
    this.url,this.lineNumber)},n}(),cr=hf,vt=undefined&&undefined.__assign||function(){return vt=Object.assign||function(n){for(var t,r,i=1,u=arguments.length;i<u;i++){t=arguments[i];for(r in t)Object.prototype.hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},vt.apply(this,arguments)},cf=function(){function t(){}t.now=function(){return(new Date).getTime()};t.setWindowEvent=function(){n.windowEvent=this.pageWindow.WindowEvent};t.setRProfiler=function(){n.profiler=this.pageWindow.RProfiler};t.setWindowEventDef=
    function(){var t,i,r,u,f;n.windowEventDef={Load:(t=n.windowEvent)===null||t===void 0?void 0:t.Load,BeforeUnload:(i=n.windowEvent)===null||i===void 0?void 0:i.BeforeUnload,Unload:(r=n.windowEvent)===null||r===void 0?void 0:r.Unload,Abort:(u=n.windowEvent)===null||u===void 0?void 0:u.Abort,Error:(f=n.windowEvent)===null||f===void 0?void 0:f.Error}};t.setAppConfig=function(t){n.config=vt(vt({},n.config),t)};t.initValues=function(){n.setWindowEvent();n.setRProfiler();n.setWindowEventDef()};var i,r,u,
    f,e,n;return n=t,t.pageWindow=parent.window,t.location=n.pageWindow.location,t.profiler=n.pageWindow.RProfiler,t.windowEvent=n.pageWindow.WindowEvent,t.protocol=n.location.protocol+"//",t.windowEventDef={Load:(i=n.windowEvent)===null||i===void 0?void 0:i.Load,BeforeUnload:(r=n.windowEvent)===null||r===void 0?void 0:r.BeforeUnload,Unload:(u=n.windowEvent)===null||u===void 0?void 0:u.Unload,Abort:(f=n.windowEvent)===null||f===void 0?void 0:f.Abort,Error:(e=n.windowEvent)===null||e===void 0?void 0:e.Error},
    t.hasPerformanceApi=!!n.pageWindow.performance&&typeof n.pageWindow.performance=="object",t.hasGetEntriesApi=n.hasPerformanceApi&&typeof n.pageWindow.performance.getEntriesByType=="function",t.testUserId="test",t.version="v4.0.4",t.config={sampleRate:-999,waterfallSampleRate:-888,postUrl:n.protocol+"r.3gl.net/hawklogserver/r.p",siteId:+"5463",debugParameter:"GlimpseDebug",debugUrl:"g.3gl.net/jp/v4.0.4/D",waterfallParameter:"GlimpseWaterfall",sendOnLoad:!1,clearResources:!0,ajaxDomains:""},
    t}(),n=cf,lf=function(){function n(){this.hasErrors=!1;this._appErrors=null;this.hasIndicators=!1;this._indicators=null;this.hasTracepoints=!1;this._tracepoints=null}return n.prototype.addError=function(n,t){this.hasErrors||(this._appErrors={},this.hasErrors=!0);this._appErrors[n]=t},n.prototype.getErrors=function(){return this._appErrors},n.prototype.addIndicator=function(n){this.hasIndicators||(this._indicators={},this.hasIndicators=!0);for(var t in n)this._indicators[t]=n[t]},n.prototype.getIndicators=
    function(){return this._indicators},n.prototype.addTracepoint=function(n){this.hasTracepoints||(this._tracepoints={},this.hasTracepoints=!0);for(var t in n)this._tracepoints[t]=n[t]},n.prototype.getTracepoints=function(){return this._tracepoints},n}(),af=lf,vf=function(){function i(){}return i.getValue=function(n,r){var u=n.responseStart!==0;switch(r){case t.DNS:return i.getMetricValue(n.domainLookupEnd,n.domainLookupStart,u);case t.Connect:return i.getMetricValue(n.connectEnd,n.connectStart,u);case t.Load:return i.getMetricValue(n.responseEnd,
    n.responseStart,u);case t.Wait:return i.getMetricValue(n.responseStart,n.requestStart,u);case t.Start:return n.startTime;case t.Redirect:return i.getMetricValue(n.redirectEnd,n.redirectStart);case t.Duration:return n.duration;case t.SSL:if(n.secureConnectionStart)return u?n.connectEnd-n.secureConnectionStart:null;break;case t.TransferSize:return n.transferSize}return 0},i.getMetricValue=function(n,t,i){if(i===void 0&&(i=!0),i){if(n>=0&&n>=t&&t>=0){var r=n-t;return Math.round(r)}}else return null},
    i.getRoundedValue=function(n){return n?Math.round(n):n},i.getQueryStringValue=function(n){for(var u=location.search.substring(1),r=u.split("\x26"),t,i=0;i<r.length;i++)if(t=r[i].split("\x3d"),t[0]==n)return t[1];return""},i.stopEvents=function(){n.profiler&&(n.profiler.eventManager.clear(),n.profiler.getEventTimingHandler().clear())},i.getLoadStateEnum=function(n){switch(n){case "loading":return v.Loading;case "dom-content-loaded":return v.DomContentLoaded;case "dom-interactive":return v.DomInteractive;
    case "complete":return v.Complete;default:return v.None}},i.getNavigationTime=function(){var i=null,t=n.hasGetEntriesApi&&n.pageWindow.performance.getEntriesByType("navigation");return t&&t.length!==0&&(i=t[0]),i},i.getNavigationStart=function(n){var t=n;return t.startTime},i}(),i=vf,yf=function(){function n(n){this.dns=null;this.connect=null;this.load=null;this.wait=null;this.start=0;this.duration=0;this.redirect=0;this.ssl=null;this.url=n.name;var r=i.getValue;this.dns=r(n,t.DNS);this.connect=r(n,
    t.Connect);this.wait=r(n,t.Wait);this.load=r(n,t.Load);this.start=r(n,t.Start);this.duration=r(n,t.Duration);this.redirect=r(n,t.Redirect);this.ssl=r(n,t.SSL);this.transferSize=r(n,t.TransferSize)}return Object.defineProperty(n.prototype,"url",{get:function(){return this._url},set:function(n){var i,t,u,r;n.indexOf("http://")!==-1?this.protocol=0:n.indexOf("https://")!==-1&&(this.protocol=1);i=n.split("/").slice(1,3).join("");t=i.indexOf(":");t!=-1&&(u=i.substr(t+1),r=parseInt(u),isNaN(r)||(this.port=
    r));n=n.substr(n.indexOf(i)+i.length);t=n.indexOf("?");t!=-1&&(n=n.substr(0,t));t=n.indexOf("#");t!=-1&&(n=n.substr(0,t));n=n.substr(0,64);this._url=n},enumerable:!1,configurable:!0}),n.prototype.translateForPost=function(){var t=i.getRoundedValue,r={u:this.url,pr:this.protocol},n=function(n,t){typeof t!="number"||isNaN(t)||(r[n]=t)};return n("pt",this.port),n("dn",t(this.dns)),n("fc",t(this.connect)),n("ld",t(this.load)),n("wt",t(this.wait)),n("st",t(this.start)),n("rd",t(this.redirect)),n("dr",
    t(this.duration)),n("ssl",t(this.ssl)),n("ts",this.transferSize),r},n}(),lr=yf,pf=undefined&&undefined.__extends||function(){var n=function(t,i){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,t){n.__proto__=t}||function(n,t){for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(n[i]=t[i])},n(t,i)};return function(t,i){function r(){this.constructor=t}if(typeof i!="function"&&i!==null)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");
    n(t,i);t.prototype=i===null?Object.create(i):(r.prototype=i.prototype,new r)}}(),wf=function(n){function r(t,i,r){var u=n.call(this,i)||this;return u.responseTime=0,u.sendSize=0,u.responseSize=0,u.method=ii[t.method.toUpperCase()],t.complete&&t.connectionEstablished&&(u.responseTime=t.complete-t.connectionEstablished),u.isSummary=r,r?(u.start=undefined,u.count=1):u.isAsync=t.isAsync,u.sendSize=t.sendSize||0,u.responseSize=t.responseSize||0,u}return pf(r,n),r.prototype.update=function(n,r){var u=i.getValue;
    r.responseStart&&(this.dns+=u(r,t.DNS),this.connect+=u(r,t.Connect),this.wait+=u(r,t.Wait),this.load+=u(r,t.Load),this.ssl+=u(r,t.SSL));this.duration+=u(r,t.Duration);this.redirect+=u(r,t.Redirect);this.sendSize+=n.sendSize||0;this.responseSize+=n.responseSize||0;this.count++},r.prototype.translateForPost=function(){var t=n.prototype.translateForPost.call(this);return this.isSummary?t.n=this.count:t.ia=this.isAsync?1:0,t.md=this.method,t.rp=Math.round(this.responseTime),t.ss=this.sendSize,t.rs=this.responseSize,
    t},r}(lr),ar=wf,bf=function(){function n(){this.count=0;this.dns=null;this.connect=null;this.load=null;this.wait=null;this.duration=0;this.redirect=0;this.ssl=null}return n.prototype.addAjaxItem=function(n,t){var i,r,u,f;if(this.update(t),i=new ar(n,t,!0),typeof this._ajax=="undefined"){this._ajax=[];this._ajax.push(i);return}for(r=0,u=this._ajax;r<u.length;r++)if(f=u[r],f.url==i.url){f.update(n,t);return}this._ajax.length<10&&this._ajax.push(i)},n.prototype.update=function(n){var r=i.getValue;n.responseStart&&
    (this.dns+=r(n,t.DNS),this.connect+=r(n,t.Connect),this.wait+=r(n,t.Wait),this.load+=r(n,t.Load),this.ssl+=r(n,t.SSL));this.duration+=r(n,t.Duration);this.redirect+=r(n,t.Redirect);this.count++},n.prototype.translateForPost=function(){var n=i.getRoundedValue,f={n:this.count,dn:n(this.dns),fc:n(this.connect),ld:n(this.load),wt:n(this.wait),dr:n(this.duration),rd:n(this.redirect),ssl:n(this.ssl)},r,t,u,e;if(this._ajax){for(r=[],t=0,u=this._ajax;t<u.length;t++)e=u[t],r.push(e.translateForPost());f.ax=
    r}return f},n}(),kf=bf,df=function(){function n(){this._resources=[]}return n.prototype.translateForPost=function(){for(var r,t=[],n=0,i=this._resources;n<i.length;n++)r=i[n],t.push(r.translateForPost());return t},n.prototype.addItem=function(n){var t=new lr(n);this._resources.push(t)},n.prototype.addAjaxItem=function(n,t){var i=new ar(n,t,!1);this._resources.push(i)},n}(),gf=df,ne=function(){function n(n){this["char"]=n;this.children=[]}return n}(),vr=ne,te=function(){function n(){this.root=new vr("");
    this.isReversed=!0}return n.prototype.add=function(n,t){var u,i,f,e,r;for(n===void 0&&(n=this.root),u=n.children,i=0,f=u;i<f.length;i++)if(e=f[i],e["char"]==t)return e;return r=new vr(t),u.push(r),r.parent=n,r},n.prototype.toObject=function(){var n={},t=this.isReversed;return function i(n,r){var o=n["char"]=="",e,u,f;if(o)e=r;else{if(u=n["char"],n.children.length==1)while(n.children.length==1)n=n.children[0],u=t?n["char"]+u:u+n["char"],n.data&&(r[u]=n.data);r[u]=n.data||{};e=r[u]}for(f=0;f<n.children.length;f++)i(n.children[f],
    e)}(this.root,n),n},n}(),yr=te,ie=undefined&&undefined.__extends||function(){var n=function(t,i){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,t){n.__proto__=t}||function(n,t){for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(n[i]=t[i])},n(t,i)};return function(t,i){function r(){this.constructor=t}if(typeof i!="function"&&i!==null)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");n(t,i);t.prototype=i===null?Object.create(i):
    (r.prototype=i.prototype,new r)}}(),re=function(t){function i(){var n=t!==null&&t.apply(this,arguments)||this;return n.maxJsErrors=10,n.charCodes={"\b":"\\b","\t":"\\t","\n":"\\n","\f":"\\f","\r":"\\r",'"':'\\"',"\\":"\\\\","\x26":"%26"},n.strRegex=/["&\\\x00-\x1f\x7f-\x9f]/g,n}return ie(i,t),i.prototype.toString=function(n,t){var i=this.translateForPost(n,t);return typeof JSON=="undefined"?this.jsonStringify(i):JSON.stringify(i)},i.prototype.strEscape=function(n){var i=this.charCodes[n],t;return i?
    i:(t=n.charCodeAt(0),"\\u00"+Math.floor(t/16).toString(16)+(t%16).toString(16))},i.prototype.jsonStringify=function(n){var t=[],i,u,e,r,f;switch(typeof n){case "string":return this.strRegex.test(n)?'"'+n.replace(this.strRegex,this.strEscape)+'"':'"'+n+'"';case "number":return isFinite(n)?String(n):"null";case "boolean":return String(n);case "object":if(!n)return"null";if(n.constructor===Date,typeof n.length=="number"&&!n.propertyIsEnumerable("length")){for(i=0,u=n;i<u.length;i++)e=u[i],t.push(this.jsonStringify(e));
    return"["+t.join(",")+"]"}for(r in n)typeof r=="string"&&(f=this.jsonStringify(n[r]),!f||t.push(this.jsonStringify(r)+":"+f));return"{"+t.join(",")+"}"}return""},i.prototype.translateForPost=function(t,i){var s=this.viewCount>1,u={},e,f,o;if(u.v=n.version,u.pt=this.postType,u.ui=this.userId,u.si=this.sessionId,u.di=this.siteId,u.pi=this.pageViewId,u.jsc=this.jsErrorCount||0,u.rf=this.referrer,u.pc=this.pageViewCount,u.vc=this.viewCount,u.rc=this.redirectCount||0,this.jsErrors&&this.jsErrors.length>
    0){for(e=[],f=0;f<Math.min(this.jsErrors.length,this.maxJsErrors);f++)e.push(this.translateErrorForPost(this.jsErrors[f]));u.jse=e}return this.hasErrors&&(u.ae=this.getErrors()),this.pageGroup&&(u.pg=this.pageGroup),this.variation&&(u.ab=this.variation),(this.resources||this.ajaxRequests)&&(o=this.translateResources(this.resources,this.ajaxRequests),u.res=o.summary,i&&(u.wf=o.waterfall)),typeof this.isNewView=="boolean"&&(u.nv=this.isNewView?1:0),this.hasIndicators&&(u.ind=this.getIndicators()),this.hasTracepoints&&
    (u.tra=this.getTracepoints()),this.isConversion&&(u.cv=this.isConversion?1:0,this.revenue&&(u.rv=this.revenue),this.revenueItems&&(u.ri=this.revenueItems)),u.np=this.isNewPageView?1:0,(t===r.OnLoad||t===r.OnAbort)&&(u.dh=this.screenHeight,u.dw=this.screenWidth,this.isNewPageView&&(u.dn=Math.round(this.dns),u.fc=Math.round(this.fullConnect),u.wt=Math.round(this.wait),u.ld=Math.round(this.load),u.de=this.domInteractive,u.dl=this.domLoaded,u.dc=this.docComplete,u.rp=this.response,u.cl=this.contentLoad,
    u.rd=this.redirect,u.rc=this.redirectCount||0,u.cls=this.cls,u.lcp=this.lcp,u.inp=this.inp,u.frc=this.frc,u.fec=this.fec,u.fdc=this.fdc,u.ftc=this.ftc,this.secureConnect&&(u.sc=this.secureConnect),this.exitToEntry&&(u.xe=this.exitToEntry),this.entryToOnLoad&&(u.el=this.entryToOnLoad),this.prerender&&(u.pr=this.prerender))),t===r.OnBeforeUnload&&(u.maf=this.markAboveTheFold,u.mfl=this.markFullyLoaded,u.mfv=this.markFullyVisible,u.mtu=this.markTimeToUserAction,u.tp=this.timeOnPage,u.tti=this.timeToInteract,
    u.et=this.engagementTime,u.fet=this.firstEngagementTime,u.vct=this.visComplete,s||(u.fp=this.firstPaint,u.fcp=this.firstContentPaint,u.cls=this.cls,u.lcp=this.lcp,u.inp=this.inp,u.frc=this.frc,u.fec=this.fec,u.fdc=this.fdc,u.ftc=this.ftc,u.inpDe=this.inpDe)),(t===r.OnBeforeUnload||t===r.OnAbort)&&(u.rqc=this.rqc),u},i.prototype.translateErrorForPost=function(n){var i={},t,r;return i.m=n.message,i.n=n.lineNumber,i.c=n.count+1,t=n.url,t&&(r=t.indexOf("?"),r!=-1&&(t=t.substr(0,r))),i.u=t,i},i.prototype.translateResources=
    function(n,t){var g=this,b=!!n,k=!!t,d,r,s,v,u,i,y,o,h,p,c,w,a,f,e,l;if(!b&&!k)return null;if(d=function(n){var c,r,o,h,e,i;if(!b||!k)return null;for(c=g.filterAjaxResources(t),r=0,o=c;r<o.length;r++){var s=o[r],u=s.url,f="";if(u&&u.indexOf("http")!=0){for(h=0,e=location.href,i=0;i<e.length;i++)if(e[i]==="/"&&(h+=1),h===3){f=e.slice(0,i);f=f+u;break}if(f===n.name)return s}if(u===n.name)return s}return null},r={},n)for(s=0,v=n;s<v.length;s++)(u=v[s],u)&&(i=u.name.split("/").slice(1,3).join(""),i&&
    i.length!=0)&&(y=i.indexOf(":"),y!=-1&&(i=i.substr(0,y)),r.hasOwnProperty(i)||(r[i]={summary:new kf,waterfall:new gf}),o=r[i],h=d(u),h?(o.summary.addAjaxItem(h,u),o.waterfall.addAjaxItem(h,u)):(o.summary.update(u),o.waterfall.addItem(u)));p=new yr;for(c in r){for(f=undefined,e=c.length-1;e>=0;e--)l=c[e],f=p.add(f,l);f.data=r[c].summary.translateForPost()}w=new yr;for(a in r){for(f=undefined,e=a.length-1;e>=0;e--)l=a[e],f=w.add(f,l);f.data=r[a].waterfall.translateForPost()}return{summary:p.toObject(),
    waterfall:w.toObject()}},i.prototype.filterAjaxResources=function(t){if(n.config.ajaxDomains===""||n.config.ajaxDomains===undefined)return t;var u=function(n){var t=/^(?:https?:\/\/)?(?:[^@\/\n]+@)?(?:www\.)?([^:\/?\n]+)/.exec(n);if(t!==null)return t[1]},i=function(n){var t=/([a-z\-0-9]{2,63})\.([a-z\.]{2,5})$/.exec(n);return t&&t[0]},r=function(n){var t=/(http|https)?:\/\/(\S+)/g.test(n);return t?n:"https://".concat(n)},f=n.config.ajaxDomains.split(",");return t.filter(function(n){var t=u(n.url),
    e=r(n.url),o=(new URL(e)).host,s=i(o);return f.some(function(n){var o=r(n),u=(new URL(o)).host,f=u.replace("www.",""),e=i(u),h=f!==e;return h?f===t:s===e})})},i}(af),ue=re,fe=function(){function t(){}return t.save=function(i){if(t.canUseLocalStorage()){n.pageWindow.localStorage.setItem(t.storeKey,i);return}t.setCookie(i)},t.read=function(){if(t.canUseLocalStorage()){var i=n.pageWindow.localStorage.getItem(t.storeKey);if(i)return i}return this.readCookie()},t.canUseLocalStorage=function(){var r=!0,
    i,u,f;try{i=t.storeKey+"delete";u=i+0;n.pageWindow.localStorage.setItem(i,u);f=n.pageWindow.localStorage.getItem(i);r=u===f;r&&n.pageWindow.localStorage.removeItem(i)}catch(e){r=!1}return r},t.setCookie=function(i){var r=new Date;r.setTime(r.getTime()+t.cookieExpireDays*864E5);var e="; expires\x3d"+r.toUTCString(),u=n.pageWindow.document.domain.split("."),f=u.length,o=u[f-2]+"."+u[f-1];document.cookie=t.storeKey+"\x3d"+encodeURIComponent(i)+e+"; path\x3d/; domain\x3d"+o+"; SameSite\x3dLax;"},t.readCookie=
    function(){for(var i,f=n.pageWindow.document.cookie.split(";"),e=t.storeRegex,r=0,u=f;r<u.length;r++)if(i=u[r],e.test(i))return decodeURIComponent(i.substring(i.indexOf("\x3d")+1,i.length));return""},t.cookieExpireDays=365,t.storeKey="__CG",t.storeRegex=new RegExp("^(\\s)*"+t.storeKey+"\x3d","i"),t}(),pr=fe,ee=function(){function n(){this.userId=-1;this.sessionId=0;this.sessionTime=0;this.pageViewCount=0;this.viewCount=0;this.pageViewId=0;this.postFlag=-1;this.sendWaterfall=!1;this.exitToEntry=0}
    return n.prototype.resetViewCount=function(){this.viewCount=1},n.prototype.save=function(){var n=this.getText();pr.save(n)},n.prototype.load=function(){var e=pr.read(),o,r,f,s,i,t,h,n;if(!e)return!1;for(o=e.split(","),r=0,f=o;r<f.length;r++)if(s=f[r],i=s.split(":"),i.length===2){if(t=i[0],typeof t=="string"&&(t=t.trim()),h=i[1],t===u.UrlCheckSum){this.urlCheckSum=h;continue}if(n=parseInt(i[1],10),!isNaN(n))switch(t){case u.UserId:this.userId=n;break;case u.SessionId:this.sessionId=n;break;case u.SessionTime:this.sessionTime=
    n;break;case u.PageViewCount:this.pageViewCount=n;break;case u.PostFlag:this.postFlag=n}}return!0},n.prototype.getText=function(){var n=u.UserId+":"+this.userId+",";return n+=u.SessionId+":"+this.sessionId+",",n+=u.SessionTime+":"+this.sessionTime+",",n+=u.PageViewCount+":"+this.pageViewCount+",",n+=u.UrlCheckSum+":"+this.urlCheckSum+",",n+(u.PostFlag+":"+this.postFlag)},n}(),oe=ee,se=function(){function t(){this.sessionExpire=18E5;this.store=new oe}return t.prototype.updateSessionTime=function(){if(this.store){var n=
    (new Date).getTime();n-this.store.sessionTime>this.sessionExpire&&(this.store.sessionId=0);this.store.sessionTime=n;this.updateStore()}},t.prototype.updateStore=function(){this.store&&this.store.save()},t.prototype.initStore=function(){var n=this.store.load();!n},t.prototype.checkAndResetPostFlags=function(){var t,i;if(this.store.postFlag=0,n.config.sampleRate<0){this.store.postFlag=-1;return}t=this.getUserId(n.config.sampleRate/100);this.store.userId<=t&&(this.store.postFlag=1,i=t/(100/n.config.waterfallSampleRate),
    this.store.sendWaterfall=this.store.userId<=i)},t.prototype.getReferrer=function(n){for(var i=this.getHostName(n).replace(":","-"),r=n.indexOf("?"),u=0,t=i.length;t<n.length;t++)u+=n.charCodeAt(t)%t;return i+"/"+(r<0?n.length:r)+"/"+n.length+"/"+u},t.prototype.getHostName=function(n){var i=n.indexOf("//")+2,t=n.indexOf("/",i);return i<2&&t==-1?n:(t==-1&&(t=n.length),n.substring(i,t))},t.prototype.init=function(){var t=n.profiler.data.start;return this.initStore(),(this.store.userId==-1||this.store.userId==
    n.testUserId)&&(this.store.userId=this.getUserId()),this.checkAndResetPostFlags(),this.store.sessionId==0||t-this.store.sessionTime>this.sessionExpire?(this.store.sessionId=Math.floor(1+Math.random()*((Math.pow(2,32)-2)/2)),this.store.pageViewCount=1):(this.store.pageViewCount<65535&&this.store.pageViewCount++,this.getReferrer(n.pageWindow.document.referrer)==this.store.urlCheckSum&&this.store.sessionTime>0&&(this.store.exitToEntry=t-this.store.sessionTime)),this.store.sessionTime=(new Date).getTime(),
    this.store.pageViewId=Math.floor(1+Math.random()*((Math.pow(2,16)-2)/2)),this.store.urlCheckSum=this.getReferrer(n.pageWindow.location.href),this.store.resetViewCount(),this.updateStore(),this.store.load()},t.prototype.getUserId=function(n){n===void 0&&(n=Math.random());return Math.floor(1+n*((Math.pow(2,64)-2)/2))},t.prototype.shouldPost=function(){return this.store.postFlag==1},t}(),he=se,ce=function(){function t(t){var r=this,u;this.longTaskEndTime=0;this.waitTime=5E3;this.performanceObserverApi=
    n.pageWindow.PerformanceObserver;this.performanceLongTaskTiming=n.pageWindow.PerformanceLongTaskTiming;this.nowTime=0;this.isSoftnav=!1;this.getDomContentLoad=function(){var n=i.getNavigationTime(),t,r;if(n)return t=i.getNavigationStart(n),r=i.getMetricValue(n.domContentLoadedEventEnd,t),r};this.observeLongTask=function(n){for(var t=0;t<n.length;t++){var i=n[t],u=r.isSoftnav?i.startTime-r.nowTime:i.startTime,f=u-r.longTaskEndTime;f>=r.waitTime?r.performanceObserver.disconnect():r.setLongTaskTime(i)}};
    this.setLongTaskTime=function(n){var t=Math.round(n.startTime+n.duration);r.longTaskEndTime=t};this.getLongTaskTime=function(){return r.longTaskEndTime};this.performanceLongTaskTiming&&(this.isSoftnav=t,this.nowTime=n.pageWindow.performance.now(),u=this.getDomContentLoad(),t||(this.longTaskEndTime=u),this.observe(["longtask"],this.observeLongTask))}return t.prototype.observe=function(n,t){this.performanceObserverApi&&(this.performanceObserver=new this.performanceObserverApi(function(n){var i=n.getEntries();
    t(i)}),this.performanceObserver.observe({entryTypes:n}))},t}(),wr=ce,ri=new wr(!1),le=function(){function t(){var t=this,s,u,f,e,o;if(this.visitor=new he,this.postUrl=n.config.postUrl,this.didSendInitial=!1,this.isDebugging=!1,this.countResourcesSent=0,this.didSoftNavigation=!1,this.currentUrl="",this.softNavigationStart=0,this.MaxNumberOfPerformanceMarks=1E3,this.updatePerformanceMetrics=function(n){var t=i.getNavigationTime();if(t){var f=t,r=i.getNavigationStart(t),e=f.loadEventEnd,u=t.responseStart;
    n.dns=u?t.domainLookupEnd-t.domainLookupStart:null;n.fullConnect=u?t.connectEnd-t.connectStart:null;n.wait=u?t.responseStart-t.requestStart:null;n.load=u?t.responseEnd-t.responseStart:null;n.domInteractive=i.getMetricValue(t.domInteractive,r);n.domLoaded=i.getMetricValue(t.domContentLoadedEventStart,r);n.docComplete=i.getMetricValue(t.domComplete,r);n.response=i.getMetricValue(t.responseEnd,r);n.contentLoad=i.getMetricValue(t.loadEventStart,e);n.redirect=t.redirectEnd-t.redirectStart;t.secureConnectionStart&&
    (n.secureConnect=t.connectEnd-t.secureConnectionStart)}},this.updateResources=function(i,u){if(n.hasPerformanceApi){var f=[];n.hasGetEntriesApi&&(f=n.pageWindow.performance.getEntriesByType("resource"));i!=r.OnLoad&&(t.setClearResources(),n.config.clearResources&&n.pageWindow.performance.clearResourceTimings?(u.resources=f,u.rqc=f.length,n.pageWindow.performance.clearResourceTimings()):(u.resources=f.slice(t.countResourcesSent),t.countResourcesSent=f.length))}},this.getTimeOnPage=function(i){var r=
    t.getFocusAwayTime(),u=t.getNavigationStart(i);return n.now()-u-r},this.getVisuallyComplete=function(t){if(n.pageWindow.CPVisuallyComplete){var i=n.pageWindow.CPVisuallyComplete.getValue(t);if(typeof i=="number"&&i>=0)return i}},this.updateEngagementMetrics=function(r,u){var f,e;if(n.hasGetEntriesApi&&(f=n.pageWindow.performance.getEntriesByType("paint"),f&&f.length>0&&(r.firstPaint=t.getPaintTimings(f,"first-paint"),r.firstContentPaint=t.getPaintTimings(f,"first-contentful-paint"))),e=i.getNavigationTime(),
    e){var h=i.getNavigationStart(e),o=i.getMetricValue(e.domContentLoadedEventEnd,h),s=ri.getLongTaskTime();r.timeToInteract=u?s||t.getVisuallyComplete(u):o&&o<s?s:o}n.profiler.getEventTimingHandler&&(r.engagementTime=n.profiler.getEventTimingHandler().getEngagementTime(),r.timeOnPage=t.getTimeOnPage(u),r.firstEngagementTime=t.getFirstEngagementTime(u))},this.getFirstEngagementTime=function(i){var r=t.getNavigationStart(i),u=n.profiler.getEventTimingHandler().getFirstEngagementTime();return u&&r?u-r:
    0},this.getNavigationStart=function(i){var r=n.profiler.getEventTimingHandler&&typeof n.profiler.getEventTimingHandler=="function"&&n.profiler.getEventTimingHandler().getStartTime&&typeof n.profiler.getEventTimingHandler().getStartTime=="function"?n.profiler.getEventTimingHandler().getStartTime():0;return i?t.softNavigationStart:r},this.getElapsedTimeSinceLatestNavStart=function(){var i=n.profiler.data.start;return t.didSoftNavigation&&i?t.softNavigationStart-i:0},this.getFocusAwayTime=function(){return n.profiler.getEventTimingHandler().getFocusAwayTime()||
    0},this.updateDebugData=function(){var u=t.createInitPostObject(r.OnBeforeUnload,!1),f=t.visitor.store.sendWaterfall||!!i.getQueryStringValue(n.config.waterfallParameter),e=u.toString(r.OnLoad,f);n.profiler.debugData=u;n.profiler.unloadDebugData=e},this.onPageLoad=function(){if(t.isDebugging){n.profiler.debugData=t.createInitPostObject(r.OnLoad,!1);n.profiler.updateDebugData=t.updateDebugData;n.profiler.sendData=function(){t.doPost(r.OnBeforeUnload,!1)};i.stopEvents();return}t.visitor.updateSessionTime();
    t.doPost(r.OnLoad,!1)},this.captureSoftNavigations=function(){var i,r,u,f,e,o,s;(n.profiler.eventManager.add("hashchange",n.pageWindow,t.onSoftNavigation),i=n.pageWindow.history,i)&&(r="function",typeof i.go===r&&(u=i.go,i.go=function(n){t.onSoftNavigation();u.call(i,n)}),typeof i.back===r&&(f=i.back,i.back=function(){t.onSoftNavigation();f.call(i)}),typeof i.forward===r&&(e=i.forward,i.forward=function(){t.onSoftNavigation();e.call(i)}),typeof i.pushState===r&&(o=i.pushState,i.pushState=function(n,
    r,u){t.onSoftNavigation();o.call(i,n,r,u)}),typeof i.replaceState===r&&(s=i.replaceState,i.replaceState=function(n,r,u){t.onSoftNavigation();s.call(i,n,r,u)}))},this.onViewVisuallyComplete=function(){t.didSoftNavigation&&t.doPost(r.OnLoad,!0)},this.onSoftNavigation=function(){if(n.profiler.data.loadFired){var i;if(ri&&(ri=new wr(!0)),n.pageWindow.CPVisuallyComplete){i=n.pageWindow.CPVisuallyComplete;i.onComplete(t.onViewVisuallyComplete)}t.doPost(r.OnBeforeUnload,t.didSoftNavigation);t.visitor.store.viewCount++;
    i&&n.pageWindow.setTimeout(function(){i.reset()},0);n.pageWindow.setTimeout(function(){if(t.softNavigationStart=n.now(),n.profiler.getEventTimingHandler){var i=n.profiler.getEventTimingHandler();i.startSoftNavigationCapture();i.resetSoftNavigationCapture()}},0);t.didSoftNavigation=!0}},this.doPost=function(u,f){var e;if(t.visitor.shouldPost()){u!=r.OnBeforeUnload||n.profiler.data.loadFired||(u=r.OnAbort);t.didSendInitial?e=t.createDiffPostObject(u,f):(e=t.createInitPostObject(u,f),t.didSendInitial=
    !0);var o=t.visitor.store.sendWaterfall||!!i.getQueryStringValue(n.config.waterfallParameter),s=u==r.OnBeforeUnload||u==r.OnAbort,h=o&&s;t.makeRequest(u,e,h)}},n.profiler&&n.profiler.data){if(s=this.visitor.init(),this.isDebugging=!!i.getQueryStringValue(n.config.debugParameter),!this.isDebugging&&!s){i.stopEvents();return}this.captureSoftNavigations();u=!1;f=function(){u||(n.profiler.data.loadFired=!0,u=!0,t.onPageLoad())};n.profiler.data.loadFired||!n.pageWindow.document||(n.profiler.data.loadFired=
    n.pageWindow.document.readyState==="complete");n.profiler.data.loadFired?f():n.profiler.eventManager.add(n.windowEventDef.Load,parent.window,f);e=!1;o=function(){e||(e=!0,t.doPost(r.OnBeforeUnload,!1))};n.profiler.eventManager.add(n.windowEventDef.BeforeUnload,n.pageWindow,o);n.profiler.eventManager.add(n.windowEventDef.Unload,n.pageWindow,o)}}return t.prototype.createInitPostObject=function(t,i){var u=this.createBasePostObj(t,!0,i),e,o,f;return this.updatePerformanceMetrics(u),e=this.visitor.store,
    e.exitToEntry>0&&(u.exitToEntry=e.exitToEntry),o=n.profiler.data.loadTime-n.profiler.data.start,o>0&&(u.entryToOnLoad=o),document.webkitVisibilityState==="prerender"&&(u.prerender=1),n.hasPerformanceApi&&(f=void 0,n.hasGetEntriesApi&&(f=n.pageWindow.performance.getEntriesByType("resource"),u.resources=f),t!=r.OnLoad&&(this.setClearResources(),n.config.clearResources&&n.pageWindow.performance.clearResourceTimings?n.pageWindow.performance.clearResourceTimings():this.countResourcesSent=f.length)),u},
    t.prototype.createDiffPostObject=function(t,i){var e,o,s,h,r=this.createBasePostObj(t,!1,i),c,u,l,f;return this.updateResources(t,r),this.updateEngagementMetrics(r,i),this.addPerformanceMarksToPostData(n.pageWindow.performance,r),c=this.getVisuallyComplete(i),c&&(r.visComplete=c),((e=n===null||n===void 0?void 0:n.profiler)===null||e===void 0?void 0:e.getCPWebVitals)&&(u=n.profiler.getCPWebVitals(),u.cls&&(r.cls=u.cls),u.lcp&&(r.lcp=u.lcp),u.inp&&(r.inp=u.inp),u.inpDe&&u.inpDe.length>0&&(r.inpDe=u.inpDe)),
    ((s=(o=n===null||n===void 0?void 0:n.profiler)===null||o===void 0?void 0:o.data)===null||s===void 0?void 0:s.jsCount)>0&&(r.jsErrorCount=n.profiler.data.jsCount,r.jsErrors=n.profiler.data.jsErrors,n.profiler.clearErrors()),((h=n===null||n===void 0?void 0:n.profiler)===null||h===void 0?void 0:h.getAjaxRequests)&&(l=n.profiler.getAjaxRequests(),l&&(r.ajaxRequests=l.slice(),n.profiler.clearAjaxRequests())),(n===null||n===void 0?void 0:n.profiler.getFrustrationMetrics)&&(f=n.profiler.getFrustrationMetrics(),
    f&&(r.frc=f.frc,r.fec=f.fec,r.fdc=f.fdc,r.ftc=f.ftc)),r},t.prototype.createBasePostObj=function(t,i,u){var f=new ue,e;return f.postType=t,f.isNewPageView=i,f.siteId=n.config.siteId,f.referrer=encodeURI(decodeURI(n.pageWindow.location.href)),f.sampleRate=n.config.sampleRate,f.waterfallSampleRate=n.config.waterfallSampleRate,e=this.visitor.store,f.userId=e.userId,f.sessionId=e.sessionId,f.pageViewId=e.pageViewId,f.pageViewCount=e.pageViewCount,f.viewCount=e.viewCount,f.screenHeight=screen.height,f.screenWidth=
    screen.width,this.currentUrl=f.referrer,f.referrer=t===r.OnBeforeUnload||t===r.OnAbort?this.currentUrl||encodeURI(decodeURI(n.pageWindow.location.href)):encodeURI(decodeURI(n.pageWindow.location.href)),u&&(f.isNewView=t==r.OnLoad),n.profiler.hasInsight&&(this.addInsightForPost(f),n.profiler.clearInfo()),f},t.prototype.addPerformanceMarksToPostData=function(t,i){var r,e,o,u,f,s;if(n.hasPerformanceApi&&t.getEntriesByType&&(r=t.getEntriesByType("mark"),r&&r.length>0&&r.length<this.MaxNumberOfPerformanceMarks))for(e=
    this.getElapsedTimeSinceLatestNavStart(),o=r.filter(function(n){return n.startTime!=null&&n.startTime>=e}),u=0,f=o;u<f.length;u++)s=f[u],this.addMarkToPostData(s,i)},t.prototype.addMarkToPostData=function(n,t){var r=i.getRoundedValue(n.startTime+n.duration);switch(n.name){case "mark_fully_loaded":t.markFullyLoaded=r;break;case "mark_fully_visible":t.markFullyVisible=r;break;case "mark_above_the_fold":t.markAboveTheFold=r;break;case "mark_time_to_user_action":t.markTimeToUserAction=r}},t.prototype.getPaintTimings=
    function(n,t){var i=n.filter(function(n){return n.name===t});if(i&&i.length>0&&i[0].startTime)return i[0].startTime},t.prototype.setClearResources=function(){n.pageWindow.__cpPreventResourceClear&&(n.config.clearResources=n.pageWindow.__cpPreventResourceClear===!1)},t.prototype.addInsightForPost=function(t){var f=n.profiler.info,r,s,e,o,u,h,c,i;for(r in f)switch(r){case "appError":if(i=f[r],i&&typeof i=="object"){e=void 0;for(o in i)(e=Number(o),isNaN(e))||(u=i[o],u&&typeof u=="string"&&(s=u,s.length>
    32&&(s=s.substring(0,32)),t.addError(o,s)))}break;case "conversion":if(i=f[r],t.isConversion=!0,i&&typeof i=="object"){e=void 0;for(o in i)(e=Number(o),isNaN(e))||(u=i[o],u&&typeof u=="number"&&(t.revenue=e,t.revenueItems=u))}break;case "indicator":h=this.buildInsight(f[r],0);h[0]&&t.addIndicator(h[1]);break;case "tracepoint":c=this.buildInsight(f[r],"");c[0]&&t.addTracepoint(c[1]);break;case "pageGroup":i=f[r];i!==undefined&&typeof i=="string"&&(t.pageGroup=i);break;case "variation":i=f[r];i!==undefined&&
    typeof i=="string"&&(t.variation=i)}},t.prototype.buildInsight=function(n,t){var f={},i=!1,r,u;if(n&&typeof n=="object")for(r in n)r&&(u=n[r],u!=null&&typeof u==typeof t&&(f[r]=u,i=!0));return i?[i,f]:[i,f]},t.prototype.makeRequest=function(t,i,r){var f=i.toString(t,r),u;n.pageWindow.navigator&&typeof n.pageWindow.navigator.sendBeacon=="function"?n.pageWindow.navigator.sendBeacon(this.postUrl,f):(u=new XMLHttpRequest,window.XDomainRequest&&(u=new window.XDomainRequest,u.timeout=0,u.onload=function(){},
    u.onerror=function(){},u.ontimeout=function(){},u.onprogress=function(){}),u.open("POST",this.postUrl,!1),u.setRequestHeader?u.setRequestHeader("Content-Type","application/x-www-form-urlencoded; charset\x3dUTF-8"):null,u.send(f),u=null)},t}(),ae=le,br=undefined&&undefined.__awaiter||function(n,t,i,r){function u(n){return n instanceof i?n:new i(function(t){t(n)})}return new (i||(i=Promise))(function(i,f){function o(n){try{e(r.next(n))}catch(t){f(t)}}function s(n){try{e(r["throw"](n))}catch(t){f(t)}}
    function e(n){n.done?i(n.value):u(n.value).then(o,s)}e((r=r.apply(n,t||[])).next())})},kr=undefined&&undefined.__generator||function(n,t){function o(n){return function(t){return s([n,t])}}function s(o){if(e)throw new TypeError("Generator is already executing.");while(f&&(f=0,o[0]&&(r=0)),r)try{if(e=1,u&&(i=o[0]&2?u["return"]:o[0]?u["throw"]||((i=u["return"])&&i.call(u),0):u.next)&&!(i=i.call(u,o[1])).done)return i;(u=0,i)&&(o=[o[0]&2,i.value]);switch(o[0]){case 0:case 1:i=o;break;case 4:return r.label++,
    {value:o[1],done:!1};case 5:r.label++;u=o[1];o=[0];continue;case 7:o=r.ops.pop();r.trys.pop();continue;default:if(!(i=r.trys,i=i.length>0&&i[i.length-1])&&(o[0]===6||o[0]===2)){r=0;continue}if(o[0]===3&&(!i||o[1]>i[0]&&o[1]<i[3])){r.label=o[1];break}if(o[0]===6&&r.label<i[1]){r.label=i[1];i=o;break}if(i&&r.label<i[2]){r.label=i[2];r.ops.push(o);break}i[2]&&r.ops.pop();r.trys.pop();continue}o=t.call(n,r)}catch(s){o=[6,s];u=0}finally{e=i=0}if(o[0]&5)throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}
    var r={label:0,sent:function(){if(i[0]&1)throw i[1];return i[1]},trys:[],ops:[]},e,u,i,f;return f={next:o(0),"throw":o(1),"return":o(2)},typeof Symbol=="function"&&(f[Symbol.iterator]=function(){return this}),f},ve=function(){return br(void 0,void 0,void 0,function(){var i,t,r;return kr(this,function(u){switch(u.label){case 0:if(!document.getElementById||!(window.attachEvent||window.addEventListener))return[2];if(!n.windowEvent||!n.profiler)return[2];!n.pageWindow.__cpPostUrl||(n.config.postUrl=n.pageWindow.__cpPostUrl.trim());
    !n.pageWindow.__cpSendOnLoad||(n.config.sendOnLoad=n.pageWindow.__cpSendOnLoad===!0);i=function(){return br(void 0,void 0,void 0,function(){var n,t;return kr(this,function(i){switch(i.label){case 0:return[4,fetch("https://g.3gl.net/jp/5463/v4.0.4/AC")];case 1:return n=i.sent(),[4,n.json()];case 2:return t=i.sent(),[2,t]}})})};u.label=1;case 1:return u.trys.push([1,3,,4]),[4,i()];case 2:return t=u.sent(),n.setAppConfig({sampleRate:t.SampleRate,ajaxDomains:t.AjaxDomains,waterfallSampleRate:t.WaterfallSampleRate}),
    [3,4];case 3:return r=u.sent(),console.error("CP RUM Error",r),[3,4];case 4:return new ae,[2]}})})},dr=ve,gr=function(n){if(n&&n.startsWith("url")){var t=n.match(/url\(["']?([^"']*)["']?\)/),i=t&&t.length>1&&t[1];if(i&&!i.startsWith("data"))return i}return null},ye=function(n){var t=n.className!==""?".".concat(n.className):"",i=n.id!==""?"#".concat(n.id):"";return[n.nodeName,t,i].join(" ")},nu=parent.window||window,pe=function(){nu.CPVisuallyComplete=function(){var t=function(){function n(){var n=
    this;this.targetWindow=nu;this.mutationObserver=undefined;this.start=0;this.waitMs=2E3;this.maxResourceTiming=0;this.mutationObserverVal=0;this.scroll="scroll";this.click="click";this.maxDiffBetweenMutation=1E3;this.sinceLastXHR=500;this.disconnectObserverTimeout=5E3;this.hasPerformance=typeof this.targetWindow.performance=="object"&&typeof this.targetWindow.performance.getEntriesByType=="function";this.removeListeners=function(){document.removeEventListener(n.scroll,n.clear);document.removeEventListener(n.click,
    n.clear)};this.addListeners=function(){document.addEventListener(n.scroll,n.clear);document.addEventListener(n.click,n.clear)};this.imageListener=function(t){for(var r=n.targetWindow.performance.getEntriesByType("resource"),u=undefined,i=0;i<r.length;i++)if(r[i].name===t.target.currentSrc){u=r[i];break}u&&(n.maxResourceTiming=Math.max(n.maxResourceTiming,Math.round(u.responseEnd)));t.currentTarget.removeEventListener("load",n.imageListener)};this.videoListener=function(t){n.maxResourceTiming=Math.max(n.maxResourceTiming,
    Math.round(n.getPerformanceTime()));t.currentTarget.removeEventListener("canplay",n.videoListener)};this.addListenersForDynamicContent=function(t){for(var r,u,f,e=t.querySelectorAll("img"),i=0;i<e.length;i+=1)r=e[i],n.isVisible(r)&&r.addEventListener("load",n.imageListener);for(u=t.querySelectorAll("video"),i=0;i<u.length;i+=1)f=u[i],n.isVisible(f)&&f.addEventListener("canplay",n.videoListener)};this.clear=function(){clearTimeout(n.timeout);n.removeListeners();n.trigger()};this.onLoad=function(){n.timeout=
    window.setTimeout(function(){n.removeListeners();n.calculate()},n.waitMs)};this.getBackgroundImagesTiming=function(){for(var h,r,f,c,p,i,w,e,o,l,s=[],t=0;t<document.styleSheets.length;t+=1){h=document.styleSheets[t];try{for(r=0;r<h.cssRules.length;r+=1){var y=h.cssRules[r],b=y.selectorText,u=y.style;if(u)for(f=0;f<u.length;f+=1)c=u[f],c==="background-image"&&(p=u[c],i=gr(p),i&&(w=n.targetWindow.document.querySelector(b),n.isVisible(w)&&s.push(i)))}}catch(g){}}for(e=n.targetWindow.document.querySelectorAll('[style*\x3d"background"]'),
    t=0;t<e.length;t++)if(n.isVisible(e[t])){var k=n.targetWindow.getComputedStyle(e[t]),d=k.getPropertyValue("background-image"),i=gr(d);i&&s.push(i)}for(o=0,l=s;o<l.length;o++){var i=l[o],a=n.targetWindow.performance.getEntriesByType("resource"),v=undefined;for(t=0;t<a.length;t++)if(a[t].name===(new URL(i,n.targetWindow.location.href)).href){v=a[t];break}v&&(n.maxResourceTiming=Math.max(n.maxResourceTiming,Math.round(v.responseEnd)))}};this.calculate=function(){if(!n.targetWindow.performance){n.trigger();
    return}n.getBackgroundImagesTiming();n.trigger()};this.getPerformanceTime=function(){return n.targetWindow.performance.now()};this.resetValueOnSoftNav=function(){n.mutationObserverVal=0;n.maxResourceTiming=0;n.isSoftNav=!1};this.isVisible=function(t){var i=typeof t.getBoundingClientRect=="function"&&t.getBoundingClientRect(),u=i&&i.width*i.height>=8&&i.right>=0&&i.bottom>=0&&i.left<=n.targetWindow.innerWidth&&i.top<=n.targetWindow.innerHeight&&!t.hidden&&t.type!=="hidden",r;return u?(r=window.getComputedStyle(t),
    r.display!=="none"&&r.visibility!=="hidden"&&r.visibility!=="collapse"&&+r.opacity>0):!1};this.mutationCallback=function(t){t.forEach(function(t){var i,u,f,e,r;if(t.type==="childList"&&t.addedNodes.length>0){if(i=t.addedNodes[0],n.isVisible(i)){for(i.nodeName.toLowerCase()==="img"&&i.addEventListener("load",n.imageListener),i.nodeName.toLowerCase()==="video"&&i.addEventListener("canplay",n.videoListener),u=n.getPerformanceTime(),n.isSoftNav&&n.resetValueOnSoftNav(),f=n.targetWindow.performance.getEntriesByType("resource"),
    e=undefined,r=0;r<f.length;r++)if(f[r].initiatorType==="xmlhttprequest"){e=f[r];break}(n.mutationObserverVal===0||e&&u-e.responseEnd<n.sinceLastXHR||u-n.mutationObserverVal<=n.maxDiffBetweenMutation)&&(n.mutationObserverVal=Math.round(u))}}else t.type==="attributes"&&t.target.nodeName.toLowerCase()==="img"&&t.attributeName==="src"&&n.isVisible(t.target)&&t.target.addEventListener("load",n.imageListener)})};this.initMutationObserver=function(){var t=n.targetWindow.MutationObserver||n.targetWindow.WebKitMutationObserver||
    n.targetWindow.MozMutationObserver;t&&n.targetWindow.performance&&(n.mutationObserver=new t(n.mutationCallback),n.observe())};this.trigger=function(){if(n.callback){var t=n.getValue(!1);n.callback(t)}};this.observe=function(){n.mutationObserver.observe(n.targetWindow.document,{childList:!0,attributes:!0,characterData:!0,subtree:!0});setTimeout(function(){n.mutationObserver.disconnect()},n.disconnectObserverTimeout)};this.getValue=function(t){var r=t||n.isSoftNav,i;return n.maxResourceTiming||n.mutationObserverVal?
    (i=0,n.maxResourceTiming&&n.mutationObserverVal?i=Math.max(n.maxResourceTiming,n.mutationObserverVal):n.maxResourceTiming?i=n.maxResourceTiming:n.mutationObserverVal&&(i=n.mutationObserverVal),!r)?Math.round(Math.max(i-n.start,n.getFirstPaintTime())):Math.round(i-n.start):undefined};this.onComplete=function(t){n.callback=t};this.reset=function(){n.isSoftNav=!0;n.targetWindow.performance&&(n.start=n.getPerformanceTime(),n.mutationObserver.disconnect(),n.observe(),n.onLoad())};this.captureSoftNavigations=
    function(){var t,i,r,u,f,e,o;n.targetWindow.HashChangeEvent&&!n.targetWindow.RProfiler&&(n.addEvent("hashchange",n.targetWindow,n.reset),t=n.targetWindow.history,t)&&(i="function",typeof t.go===i&&(r=t.go,t.go=function(i){n.reset();r.call(t,i)}),typeof t.back===i&&(u=t.back,t.back=function(){n.reset();u.call(t)}),typeof t.forward===i&&(f=t.forward,t.forward=function(){n.reset();f.call(t)}),typeof t.pushState===i&&(e=t.pushState,t.pushState=function(i,r,u){n.reset();e.call(t,i,r,u)}),typeof t.replaceState===
    i&&(o=t.replaceState,t.replaceState=function(i,r,u){n.reset();o.call(t,i,r,u)}))};this.initMutationObserver();this.captureSoftNavigations();this.init()}return n.prototype.init=function(){var t=this,n=this.targetWindow.document;n.readyState==="complete"?this.onLoad():this.targetWindow.addEventListener("load",this.onLoad);n.readyState==="interactive"?this.addListenersForDynamicContent(n):this.targetWindow.addEventListener("DOMContentLoaded",function(){t.addListenersForDynamicContent(n)});this.removeListeners();
    this.addListeners()},n.prototype.addEvent=function(n,t,i){this.targetWindow.attachEvent?t.attachEvent("on"+n,i):t.addEventListener(n,i,!1)},n.prototype.getFirstPaintTime=function(){var r=0,n,t,i;try{n=this.targetWindow.performance.getEntriesByType("paint");n&&n.length>0&&(t=n.filter(function(n){return n.name==="first-paint"}),t&&t.length>0&&t[0].startTime&&(r=Math.round(t[0].startTime)),i=n.filter(function(n){return n.name==="first-contentful-paint"}),i&&i.length>0&&i[0].startTime&&(r=Math.round(i[0].startTime)))}catch(u){}return r},
    n}(),n=new t;return{getValue:n.getValue,onComplete:n.onComplete,reset:n.reset}}()},tu=pe,we=function(){function n(){this.clickCount=0;this.rageClickLimit=3;this.timeoutDuration=1E3;this.rageClickValue=null}return n.prototype.startListening=function(){this.clicklistener()},n.prototype.getRageClick=function(){return this.rageClickValue},n.prototype.clicklistener=function(){var t=this,n;this.clickCount++;n=setInterval(function(){t.clickCount=0;clearInterval(n)},this.timeoutDuration);this.clickCount>=
    this.rageClickLimit&&(this.rageClickValue=1,clearInterval(n))},n}(),iu=new we,be=function(){function n(){this.error="";this.errorClickValue=null}return n.prototype.startListening=function(){var n=this;window.onerror=function(t){n.error=t};this.clicklistener()},n.prototype.getErrorClick=function(){return this.errorClickValue},n.prototype.clicklistener=function(){var n=this;setTimeout(function(){n.error&&(n.errorClickValue=1)},0)},n}(),ru=new be,ke=function(){function n(){this.clickCounts={};this.deadClickLimit=
    2;this.deadClickValue=null;this.timeoutDuration=1E3}return n.prototype.getDeadClick=function(){return this.deadClickValue},n.prototype.clickListener=function(n){var r=this,i=setInterval(function(){r.clickCounts={};clearInterval(i)},this.timeoutDuration),t=ye(n.target);this.clickCounts[t]=this.clickCounts[t]?this.clickCounts[t]+1:1;this.clickCounts[t]===this.deadClickLimit&&(this.deadClickValue=1,clearInterval(i))},n.prototype.startListening=function(n){this.clickListener(n)},n}(),uu=new ke,de=function(){function n(){var n=
    this,t;this.mouseMoveListener=function(t){var i=Math.sign(t.movementX);n.distance+=Math.abs(t.movementX)+Math.abs(t.movementY);i!==n.direction&&(n.direction=i,n.directionChangeCount++)};this.directionChangeCount=0;this.distance=0;this.interval=350;this.threshold=.01;this.thrashedCursorValue=!1;t=setInterval(function(){var i=n.distance/n.interval,r;if(!n.velocity){n.velocity=i;return}r=(i-n.velocity)/n.interval;n.directionChangeCount&&r>n.threshold&&(clearInterval(t),n.thrashedCursorValue=!0);n.distance=
    0;n.directionChangeCount=0;n.velocity=i},this.interval)}return n.prototype.getThrashedCursor=function(){return this.thrashedCursorValue},n.prototype.startListening=function(n){this.mouseMoveListener(n)},n}(),fu=new de,ge=function(){function n(){}return n.prototype.listenClickEvent=function(n){iu.startListening(n);ru.startListening(n);uu.startListening(n)},n.prototype.listenMouseMove=function(n){fu.startListening(n)},n.prototype.startListeningClickEvent=function(){window.addEventListener("click",this.listenClickEvent.bind(this))},
    n.prototype.stopListeningClickEvent=function(){window.removeEventListener("click",this.listenClickEvent.bind(this))},n.prototype.startListeningMouseMove=function(){window.addEventListener("mousemove",this.listenMouseMove.bind(this))},n.prototype.stopListeningMouseMove=function(){window.removeEventListener("mousemove",this.listenMouseMove.bind(this))},n}(),eu=new ge,ui=undefined&&undefined.__assign||function(){return ui=Object.assign||function(n){for(var t,r,i=1,u=arguments.length;i<u;i++){t=arguments[i];
    for(r in t)Object.prototype.hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},ui.apply(this,arguments)},ou=function(){function n(){function u(n){var i=n.target||n.srcElement;return i.nodeType==3&&(i=i.parentNode),t("N/A",i.src||i.URL,-1),!1}var n=this,t,r;this.restUrl="g.3gl.net/jp/5463/v4.0.4/M";this.startTime=(new Date).getTime();this.eventsTimingHandler=new sf;this.inpDe=[];this.version="v4.0.4";this.info={};this.hasInsight=!1;this.data={start:this.startTime,jsCount:0,jsErrors:[],
    loadTime:-1,loadFired:window.document.readyState=="complete"};this.eventManager=new hr;this.setCLS=function(t){var i=t.name,r=t.delta,u=i==="CLS"?r:undefined;n.cls=u};this.setLCP=function(t){var r=t.name,i=t.value,u=t.delta,f=r==="LCP"?i:undefined;u>=0&&i>0&&(n.lcp=f)};this.setINP=function(t){var e=t.name,u=t.value,r=t.attribution,f;e==="INP"&&(n.inp=u,u>200&&(f={t:r.interactionTarget,eTy:r.interactionType,sTi:i.getRoundedValue(r.interactionTime),indl:i.getRoundedValue(r.inputDelay),psdu:i.getRoundedValue(r.processingDuration),
    prdl:i.getRoundedValue(r.presentationDelay),val:i.getRoundedValue(u),ls:i.getLoadStateEnum(r.loadState)},n.inpDe.push(ui({},f)),n.inpDe.sort(function(n,t){return t.val-n.val}),n.inpDe.length===10&&n.inpDe.pop()))};this.recordPageLoad=function(){n.data.loadTime=(new Date).getTime();n.data.loadFired=!0};this.addError=function(t,i,r){var s,f,u,e,o;for(n.data.jsCount++,s=cr.createText(t,i,r),f=n.data.jsErrors,u=0,e=f;u<e.length;u++)if(o=e[u],o.getText()==s){o.count++;return}f.push(new cr(t,i,r))};this.getAjaxRequests=
    function(){return n.ajaxHandler.getAjaxRequests()};this.clearAjaxRequests=function(){n.ajaxHandler.clear()};this.addInfo=function(t,i,r){if(!n.isNullOrEmpty(t)){if(n.isNullOrEmpty(r))n.info[t]=i;else{if(n.isNullOrEmpty(i))return;n.isNullOrEmpty(n.info[t])&&(n.info[t]={});n.info[t][i]=r}n.hasInsight=!0}};this.clearInfo=function(){n.info={};n.hasInsight=!1};this.clearErrors=function(){n.data.jsCount=0;n.data.jsErrors=[]};this.getInfo=function(){return n.hasInsight?n.info:null};this.getEventTimingHandler=
    function(){return n.eventsTimingHandler};this.getCPWebVitals=function(){return vi(n.setCLS),ir(n.setLCP),nr(n.setINP),{cls:n.cls,lcp:n.lcp,inp:n.inp,inpDe:n.inpDe}};this.getFrustrationMetrics=function(){return{frc:iu.getRageClick(),fec:ru.getErrorClick(),fdc:uu.getDeadClick(),ftc:fu.getThrashedCursor()}};this.eventManager.add(et.Load,window,this.recordPageLoad);t=this.addError;this.ajaxHandler=new ff;vi(this.setCLS);ir(this.setLCP,{reportAllChanges:!0});nr(this.setINP,{reportAllChanges:!0});eu.startListeningClickEvent();
    eu.startListeningMouseMove();window.opera?this.eventManager.add(et.Error,document,u):"onerror"in window&&(r=window.onerror,window.onerror=function(n,i,u){return(t(n,i!==null&&i!==void 0?i:"",u!==null&&u!==void 0?u:0),!!r)?r(n,i,u):!1});"onunhandledrejection"in window&&(window.onunhandledrejection=function(n){var r,u,f,e=(r=n.reason.stack)!==null&&r!==void 0?r:"",i=e!==""?e.split(/\bat\b/):[],s=i[1]?i[1].replace(/:\d+/g,""):"",o=i[1]?i[1].match(/:\d+/g):[],h=o[0]?o[0].replace(":",""):0;t((f=(u=i[0])===
    null||u===void 0?void 0:u.trim())!==null&&f!==void 0?f:"N/A",s.trim(),h)});!window.__cpCdnPath||(this.restUrl=window.__cpCdnPath.trim())}return n.prototype.isNullOrEmpty=function(n){if(n===undefined||n===null)return!0;if(typeof n=="string"){var t=n;return t.trim().length==0}return!1},n.prototype.dispatchCustomEvent=function(n){(function(n){function t(n,t){t=t||{bubbles:!1,cancelable:!1,detail:undefined};var i=document.createEvent("CustomEvent");return i.initCustomEvent(n,t.bubbles,t.cancelable,t.detail),
    i}if(typeof n.CustomEvent=="function")return!1;t.prototype=Event.prototype;n.CustomEvent=t})(window);var t=new CustomEvent(n);window.dispatchEvent(t)},n}(),no=ou,su=new ou;window.RProfiler=su;window.WindowEvent=et;document.readyState==="complete"?(n.initValues(),dr(),tu()):document.onreadystatechange=function(){document.readyState==="complete"&&(n.initValues(),dr(),tu())};su.dispatchCustomEvent("GlimpseLoaded")})()}
    if(window.name.indexOf("Partytown")===0){var js=loadCatchpoint.toString();js=js.replace("raw$Var",raw$Var);window.injectPTJS(js+";loadCatchpoint();")}else loadCatchpoint();</script><script id="" text="" charset="" type="text/javascript" src="//tru.am/scripts/custom/meredith.js" data-loaded="loaded"></script>
    
      <script id="" text="" charset="utf-8" type="text/javascript" src="//b-code.liadm.com/a-07sk.min.js" data-loaded="loaded"></script><img src="https://sync.graph.bluecava.com/ds.png?p=9274e5db-ddcb-11ea-a80b-0242ac110002&amp;segment=4l28r4sz3bl3555wt2hyave1w6u68syw&amp;uid=&amp;CampaignID=303C&amp;Channel=seriouseats&amp;CreativeID=&amp;Placement=5094396&amp;MAID=&amp;Keyword=&amp;Medium=&amp;Source=&amp;PageName=www.seriouseats.com%2F&amp;Event=&amp;Key1=5094396&amp;Key2=&amp;Key3=&amp;Key4=&amp;Key5=" alt="" style="width:1px !important;height:1px !important;border:0 !important">
    <script type="text/javascript" id="" charset="">Sailthru.init({customerId:google_tag_manager["rm"]["13527222"](43)});</script>
    
    
    </body><iframe name="goog_topics_frame" src="https://securepubads.g.doubleclick.net/static/topics/topics_frame.html" style="display: none;"></iframe></html>