<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OAuth Callback</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, 'Helvetica Neue', Arial, sans-serif;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            background-color: #f5f5f5;
        }
        .container {
            text-align: center;
            background: white;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 400px;
        }
        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .error {
            color: #e74c3c;
            margin-top: 20px;
        }
        .success {
            color: #27ae60;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="spinner" id="spinner"></div>
        <h2>Completing Authentication...</h2>
        <p id="status">Please wait while we complete your login.</p>
        <div id="message"></div>
    </div>

    <script>
        // Extract query parameters
        const urlParams = new URLSearchParams(window.location.search);
        const code = urlParams.get('code');
        const error = urlParams.get('error');
        const errorDescription = urlParams.get('error_description');
        
        const statusEl = document.getElementById('status');
        const messageEl = document.getElementById('message');
        const spinnerEl = document.getElementById('spinner');
        
        async function handleCallback() {
            if (error) {
                // OAuth error
                spinnerEl.style.display = 'none';
                statusEl.textContent = 'Authentication Failed';
                messageEl.className = 'error';
                messageEl.textContent = errorDescription || error || 'An error occurred during authentication.';
                
                // Notify parent window
                if (window.opener) {
                    window.opener.postMessage({
                        type: 'oauth_callback',
                        error: errorDescription || error
                    }, window.location.origin);
                }
                
                // Close window after delay
                setTimeout(() => {
                    window.close();
                }, 3000);
                return;
            }
            
            if (!code) {
                // No code received
                spinnerEl.style.display = 'none';
                statusEl.textContent = 'Authentication Failed';
                messageEl.className = 'error';
                messageEl.textContent = 'No authorization code received.';
                
                // Close window after delay
                setTimeout(() => {
                    window.close();
                }, 3000);
                return;
            }
            
            // Extract provider from sessionStorage first (as per PR #260)
            let provider = sessionStorage.getItem('oauth_provider') || 'unknown';
            
            // If not in sessionStorage, try other methods
            if (provider === 'unknown') {
                const pathname = window.location.pathname;
                
                // Check if provider is in the URL path
                if (pathname.includes('google')) provider = 'google';
                else if (pathname.includes('facebook')) provider = 'facebook';
                else if (pathname.includes('microsoft')) provider = 'microsoft';
                else if (pathname.includes('github')) provider = 'github';
            }
            
            // Alternative: check state parameter
            const state = urlParams.get('state');
            if (state) {
                try {
                    const stateData = JSON.parse(atob(state));
                    if (stateData.provider) provider = stateData.provider;
                } catch (e) {
                    // State might not be JSON
                    if (state === 'google' || state === 'facebook' || state === 'microsoft' || state === 'github') {
                        provider = state;
                    }
                }
            }
            
            // Determine provider from referrer if still unknown
            if (provider === 'unknown' && document.referrer) {
                if (document.referrer.includes('google')) provider = 'google';
                else if (document.referrer.includes('facebook')) provider = 'facebook';
                else if (document.referrer.includes('microsoft')) provider = 'microsoft';
                else if (document.referrer.includes('github')) provider = 'github';
            }
            
            statusEl.textContent = 'Exchanging authorization code...';
            
            try {
                // Exchange code for token
                const response = await fetch('/api/oauth/token', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        code: code,
                        provider: provider,
                        redirect_uri: window.location.origin + window.location.pathname
                    })
                });
                
                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.error || 'Token exchange failed');
                }
                
                const authData = await response.json();
                
                // Success!
                spinnerEl.style.display = 'none';
                statusEl.textContent = 'Authentication Successful!';
                messageEl.className = 'success';
                messageEl.textContent = `Welcome, ${authData.user_info.name || authData.user_info.email}!`;
                
                // Store auth data
                localStorage.setItem('authToken', authData.access_token);
                localStorage.setItem('userInfo', JSON.stringify(authData.user_info));
                
                // Clear sessionStorage (as per PR #260)
                sessionStorage.removeItem('oauth_provider');
                
                // Notify parent window
                if (window.opener) {
                    window.opener.postMessage({
                        type: 'oauth_callback',
                        authData: authData
                    }, window.location.origin);
                }
                
                // Close window after short delay
                setTimeout(() => {
                    window.close();
                }, 1500);
                
            } catch (error) {
                spinnerEl.style.display = 'none';
                statusEl.textContent = 'Authentication Failed';
                messageEl.className = 'error';
                messageEl.textContent = error.message || 'Failed to complete authentication.';
                
                // Notify parent window
                if (window.opener) {
                    window.opener.postMessage({
                        type: 'oauth_callback',
                        error: error.message
                    }, window.location.origin);
                }
                
                // Close window after delay
                setTimeout(() => {
                    window.close();
                }, 3000);
            }
        }
        
        // Handle the callback
        handleCallback();
    </script>
</body>
</html>